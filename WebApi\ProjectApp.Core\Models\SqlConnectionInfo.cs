using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Models
{
    public class SqlConnectionInfo
    {
        public string Name { get; set; }
        public string ConnectionString { get; set; }
    }

    public class SqlConnectionInfoList
    {
        public List<SqlConnectionInfo> Connections { get; set; } = new List<SqlConnectionInfo>();
    }


    public class SqlQueryRequest
    {
        public string ConnectionString { get; set; }
        public string SqlQuery { get; set; }
    }

    public class SqlQueryResponse
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public DataTable Data { get; set; }
        public int RowsAffected { get; set; }
    }
}
