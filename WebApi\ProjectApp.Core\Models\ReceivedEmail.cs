namespace ProjectApp.Core.Models
{
    public class ReceivedEmail
    {
        public string MessageId { get; set; } = string.Empty;
        public string From { get; set; } = string.Empty;
        public string To { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string HtmlBody { get; set; } = string.Empty;
        public DateTime ReceivedDate { get; set; }
        public List<string> Attachments { get; set; } = new();
        public bool IsRead { get; set; }
    }
}
