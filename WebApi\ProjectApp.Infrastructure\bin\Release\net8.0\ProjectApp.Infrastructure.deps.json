{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ProjectApp.Infrastructure/1.0.0": {"dependencies": {"BCrypt.Net-Core": "1.6.0", "Dapper": "2.1.66", "Hangfire": "1.8.18", "Hangfire.AspNetCore": "1.8.18", "Hangfire.SqlServer": "1.8.18", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.2", "Microsoft.Data.SqlClient": "6.0.1", "Microsoft.KernelMemory": "0.98.250324.1", "Microsoft.KernelMemory.Core": "0.98.250324.1", "Microsoft.SemanticKernel": "1.44.0", "Microsoft.SemanticKernel.Agents.Abstractions": "1.44.0-preview", "Microsoft.SemanticKernel.Agents.Core": "1.44.0-preview", "Microsoft.SemanticKernel.Connectors.Google": "1.44.0-alpha", "Microsoft.SemanticKernel.Connectors.Ollama": "1.44.0-alpha", "Microsoft.SemanticKernel.Plugins.OpenApi": "1.40.1", "ModelContextProtocol": "0.1.0-preview.6", "OpenTelemetry": "1.11.2", "OpenTelemetry.Api": "1.11.2", "OpenTelemetry.Exporter.Console": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "ProjectApp.Core": "1.0.0"}, "runtime": {"ProjectApp.Infrastructure.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AWSSDK.Core/3.7.402.24": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.402.24"}}}, "AWSSDK.S3/3.7.415.23": {"dependencies": {"AWSSDK.Core": "3.7.402.24"}, "runtime": {"lib/net8.0/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.415.23"}}}, "Azure.AI.ContentSafety/1.0.0": {"dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "9.0.3"}, "runtime": {"lib/netstandard2.0/Azure.AI.ContentSafety.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.23.61102"}}}, "Azure.AI.FormRecognizer/4.1.0": {"dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "9.0.3"}, "runtime": {"lib/netstandard2.0/Azure.AI.FormRecognizer.dll": {"assemblyVersion": "4.1.0.0", "fileVersion": "4.100.23.41002"}}}, "Azure.AI.OpenAI/2.2.0-beta.4": {"dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.2.0-beta.4", "System.ClientModel": "1.4.0-beta.1"}, "runtime": {"lib/net8.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.200.25.16901"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Memory.Data": "9.0.3", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "9.0.3", "System.Text.Json": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.13.2": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "1.13.2.0", "fileVersion": "1.1300.225.6404"}}}, "Azure.Search.Documents/11.6.0": {"dependencies": {"Azure.Core": "1.44.1", "System.Text.Json": "9.0.3", "System.Threading.Channels": "9.0.3"}, "runtime": {"lib/netstandard2.0/Azure.Search.Documents.dll": {"assemblyVersion": "11.6.0.0", "fileVersion": "11.600.24.36703"}}}, "Azure.Storage.Blobs/12.24.0": {"dependencies": {"Azure.Storage.Common": "12.23.0"}, "runtime": {"lib/net8.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.24.0.0", "fileVersion": "12.2400.25.16105"}}}, "Azure.Storage.Common/12.23.0": {"dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net8.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.23.0.0", "fileVersion": "12.2300.25.16105"}}}, "Azure.Storage.Queues/12.22.0": {"dependencies": {"Azure.Storage.Common": "12.23.0", "System.Memory.Data": "9.0.3"}, "runtime": {"lib/net8.0/Azure.Storage.Queues.dll": {"assemblyVersion": "12.22.0.0", "fileVersion": "12.2200.25.16105"}}}, "BCrypt.Net-Core/1.6.0": {"runtime": {"lib/netstandard2.0/BCrypt.Net-Core.dll": {"assemblyVersion": "1.6.0.0", "fileVersion": "1.6.0.0"}}}, "ClosedXML/0.104.2": {"dependencies": {"ClosedXML.Parser": "1.2.0", "DocumentFormat.OpenXml": "3.3.0", "ExcelNumberFormat": "1.1.0", "RBush": "4.0.0", "SixLabors.Fonts": "1.0.0"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.104.2.0", "fileVersion": "0.104.2.0"}}}, "ClosedXML.Parser/1.2.0": {"runtime": {"lib/netstandard2.1/ClosedXML.Parser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.HighPerformance/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "Dapper/2.1.66": {"runtime": {"lib/net8.0/Dapper.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.66.48463"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "DocumentFormat.OpenXml/3.3.0": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elastic.Clients.Elasticsearch/8.12.1": {"dependencies": {"Elastic.Transport": "0.4.18"}, "runtime": {"lib/net8.0/Elastic.Clients.Elasticsearch.dll": {"assemblyVersion": "*******", "fileVersion": "8.12.1.0"}}}, "Elastic.Transport/0.4.18": {"runtime": {"lib/net8.0/Elastic.Transport.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.4.18.0"}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.0.0"}}}, "FluentValidation/11.5.1": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.5.1.0"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.5.1", "FluentValidation.DependencyInjectionExtensions": "11.5.1"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.3.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"dependencies": {"FluentValidation": "11.5.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.5.1.0"}}}, "Google.Protobuf/3.27.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.27.1.0", "fileVersion": "3.27.1.0"}}}, "Hangfire/1.8.18": {"dependencies": {"Hangfire.AspNetCore": "1.8.18", "Hangfire.Core": "1.8.18", "Hangfire.SqlServer": "1.8.18"}}, "Hangfire.AspNetCore/1.8.18": {"dependencies": {"Hangfire.NetCore": "1.8.18"}, "runtime": {"lib/netcoreapp3.0/Hangfire.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.Core/1.8.18": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Hangfire.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netstandard2.0/ca/Hangfire.Core.resources.dll": {"locale": "ca"}, "lib/netstandard2.0/de/Hangfire.Core.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fa/Hangfire.Core.resources.dll": {"locale": "fa"}, "lib/netstandard2.0/fr/Hangfire.Core.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/nb/Hangfire.Core.resources.dll": {"locale": "nb"}, "lib/netstandard2.0/nl/Hangfire.Core.resources.dll": {"locale": "nl"}, "lib/netstandard2.0/pt-BR/Hangfire.Core.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/pt-PT/Hangfire.Core.resources.dll": {"locale": "pt-PT"}, "lib/netstandard2.0/pt/Hangfire.Core.resources.dll": {"locale": "pt"}, "lib/netstandard2.0/sv/Hangfire.Core.resources.dll": {"locale": "sv"}, "lib/netstandard2.0/tr-TR/Hangfire.Core.resources.dll": {"locale": "tr-TR"}, "lib/netstandard2.0/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard2.0/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Hangfire.NetCore/1.8.18": {"dependencies": {"Hangfire.Core": "1.8.18", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "runtime": {"lib/netstandard2.1/Hangfire.NetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Hangfire.SqlServer/1.8.18": {"dependencies": {"Hangfire.Core": "1.8.18"}, "runtime": {"lib/netstandard2.0/Hangfire.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "HtmlAgilityPack/1.12.0": {"runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "JetBrains.Annotations/2021.2.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "2021.2.0.0", "fileVersion": "2021.2.0.0"}}}, "Json.More.Net/1.9.0": {"dependencies": {"System.Text.Json": "9.0.3"}, "runtime": {"lib/netstandard2.0/Json.More.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "JsonPointer.Net/3.0.3": {"dependencies": {"Json.More.Net": "1.9.0"}, "runtime": {"lib/netstandard2.0/JsonPointer.Net.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.3.0"}}}, "JsonSchema.Net/5.4.2": {"dependencies": {"JetBrains.Annotations": "2021.2.0", "Json.More.Net": "1.9.0", "JsonPointer.Net": "3.0.3"}, "runtime": {"lib/netstandard2.0/JsonSchema.Net.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.4.2.0"}}}, "LLamaSharp/0.23.0": {"dependencies": {"CommunityToolkit.HighPerformance": "8.4.0", "Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Numerics.Tensors": "9.0.3"}, "runtime": {"lib/net8.0/LLamaSharp.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "MediatR/12.4.1": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "********", "fileVersion": "12.4.1.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.224.6804"}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Bcl.Cryptography/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.56604"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/6.0.1": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.Bcl.Cryptography": "8.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.3", "System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}}, "resources": {"lib/net8.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}, "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.2.0.0"}}}, "Microsoft.Extensions.AI/9.3.0-preview.1.25161.3": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Text.Json": "9.0.3", "System.Threading.Channels": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.dll": {"assemblyVersion": "9.3.0.0", "fileVersion": "9.300.25.16103"}}}, "Microsoft.Extensions.AI.Abstractions/9.3.0-preview.1.25161.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "9.3.0.0", "fileVersion": "9.300.25.16103"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Configuration/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Diagnostics/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileSystemGlobbing": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Hosting/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.Configuration.CommandLine": "9.0.3", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.3", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "Microsoft.Extensions.Logging.Console": "9.0.3", "Microsoft.Extensions.Logging.Debug": "9.0.3", "Microsoft.Extensions.Logging.EventLog": "9.0.3", "Microsoft.Extensions.Logging.EventSource": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Http/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Diagnostics": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Console/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Debug/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "System.Diagnostics.EventLog": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Options/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Primitives/9.0.3": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.25161.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.67.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Logging/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Protocols/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.5.0", "System.IdentityModel.Tokens.Jwt": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "Microsoft.IdentityModel.Tokens/7.5.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.5.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "Microsoft.KernelMemory/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.AI.Anthropic": "0.98.250324.1", "Microsoft.KernelMemory.AI.AzureOpenAI": "0.98.250324.1", "Microsoft.KernelMemory.AI.LlamaSharp": "0.98.250324.1", "Microsoft.KernelMemory.AI.Ollama": "0.98.250324.1", "Microsoft.KernelMemory.AI.Onnx": "0.98.250324.1", "Microsoft.KernelMemory.AI.OpenAI": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.Core": "0.98.250324.1", "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel": "0.98.250324.1", "Microsoft.KernelMemory.DocumentStorage.AWSS3": "0.98.250324.1", "Microsoft.KernelMemory.DocumentStorage.AzureBlobs": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.AzureAISearch": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Elasticsearch": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Postgres": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Qdrant": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.Redis": "0.98.250324.1", "Microsoft.KernelMemory.MemoryDb.SQLServer": "0.98.250324.1", "Microsoft.KernelMemory.MongoDbAtlas": "0.98.250324.1", "Microsoft.KernelMemory.Orchestration.AzureQueues": "0.98.250324.1", "Microsoft.KernelMemory.Orchestration.RabbitMQ": "0.98.250324.1", "Microsoft.KernelMemory.Safety.AzureAIContentSafety": "0.98.250324.1", "Microsoft.KernelMemory.SemanticKernelPlugin": "0.98.250324.1", "Microsoft.KernelMemory.WebClient": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.All.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Abstractions/0.98.250324.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.Extensions.Hosting": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "System.Linq.Async": "6.0.1", "System.Memory.Data": "9.0.3", "System.Numerics.Tensors": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Anthropic/0.98.250324.1": {"dependencies": {"Microsoft.Extensions.Http": "9.0.3", "Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Anthropic.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.AzureOpenAI/0.98.250324.1": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.AzureOpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.LlamaSharp/0.98.250324.1": {"dependencies": {"LLamaSharp": "0.23.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.Core": "0.98.250324.1", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.LlamaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Ollama/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "OllamaSharp": "5.1.7"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Ollama.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Onnx/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.ML.OnnxRuntimeGenAI": "0.6.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Onnx.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.OpenAI/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.OpenAI.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.AI.Tiktoken/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.ML.Tokenizers.Data.Cl100kBase": "1.0.2", "Microsoft.ML.Tokenizers.Data.O200kBase": "1.0.2", "Microsoft.ML.Tokenizers.Data.P50kBase": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.AI.Tiktoken.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Chunkers/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Chunkers.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Core/0.98.250324.1": {"dependencies": {"ClosedXML": "0.104.2", "DocumentFormat.OpenXml": "3.3.0", "HtmlAgilityPack": "1.12.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http": "9.0.3", "Microsoft.KernelMemory.AI.Tiktoken": "0.98.250324.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.Chunkers": "0.98.250324.1", "PdfPig": "0.1.10", "Polly.Core": "8.5.2", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel/0.98.250324.1": {"dependencies": {"Azure.AI.FormRecognizer": "4.1.0", "Azure.Identity": "1.13.2", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.DocumentStorage.AWSS3/0.98.250324.1": {"dependencies": {"AWSSDK.S3": "3.7.415.23", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AWSS3.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.DocumentStorage.AzureBlobs/0.98.250324.1": {"dependencies": {"Azure.Identity": "1.13.2", "Azure.Storage.Blobs": "12.24.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.AzureAISearch/0.98.250324.1": {"dependencies": {"Azure.Identity": "1.13.2", "Azure.Search.Documents": "11.6.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Elasticsearch/0.98.250324.1": {"dependencies": {"Elastic.Clients.Elasticsearch": "8.12.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Postgres/0.98.250324.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Pgvector": "0.3.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Postgres.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Qdrant/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Qdrant.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.Redis/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "NRedisStack": "0.13.2", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.Redis.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MemoryDb.SQLServer/0.98.250324.1": {"dependencies": {"Microsoft.Data.SqlClient": "6.0.1", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "System.Runtime.Caching": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MemoryDb.SQLServer.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.MongoDbAtlas/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "MongoDB.Driver": "3.2.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.MongoDbAtlas.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Orchestration.AzureQueues/0.98.250324.1": {"dependencies": {"Azure.Identity": "1.13.2", "Azure.Storage.Queues": "12.22.0", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.AzureQueues.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Orchestration.RabbitMQ/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "RabbitMQ.Client": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Orchestration.RabbitMQ.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.Safety.AzureAIContentSafety/0.98.250324.1": {"dependencies": {"Azure.AI.ContentSafety": "1.0.0", "Azure.Identity": "1.13.2", "Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.SemanticKernelPlugin/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1", "Microsoft.KernelMemory.WebClient": "0.98.250324.1", "Microsoft.SemanticKernel.Abstractions": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.SemanticKernelPlugin.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.KernelMemory.WebClient/0.98.250324.1": {"dependencies": {"Microsoft.KernelMemory.Abstractions": "0.98.250324.1"}, "runtime": {"lib/net8.0/Microsoft.KernelMemory.WebClient.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.ML.OnnxRuntime/1.20.1": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.20.1"}, "runtimeTargets": {"runtimes/android/native/onnxruntime.aar": {"rid": "android", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios/native/onnxruntime.xcframework.zip": {"rid": "ios", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libonnxruntime.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libonnxruntime_providers_shared.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime_providers_shared.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libonnxruntime.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libonnxruntime.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.20.24.1119"}, "runtimes/win-arm64/native/onnxruntime.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.20.24.1119"}, "runtimes/win-arm64/native/onnxruntime_providers_shared.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.20.24.1119"}, "runtimes/win-x64/native/onnxruntime.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.20.24.1119"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onnxruntime.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.20.24.1119"}, "runtimes/win-x86/native/onnxruntime.lib": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onnxruntime_providers_shared.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.20.24.1119"}, "runtimes/win-x86/native/onnxruntime_providers_shared.lib": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.20.1": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.ML.OnnxRuntimeGenAI/0.6.0": {"dependencies": {"Microsoft.ML.OnnxRuntime": "1.20.1", "Microsoft.ML.OnnxRuntimeGenAI.Managed": "0.6.0"}, "runtimeTargets": {"runtimes/android/native/onnxruntime-genai.aar": {"rid": "android", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios/native/onnxruntime-genai.xcframework.zip": {"rid": "ios", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libonnxruntime-genai.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libonnxruntime-genai.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libonnxruntime-genai.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/onnxruntime-genai.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/onnxruntime-genai.lib": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime-genai.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/onnxruntime-genai.lib": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.6.0": {"runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Microsoft.ML.Tokenizers/1.0.2": {"dependencies": {"Google.Protobuf": "3.27.1", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.ML.Tokenizers.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.225.12505"}}}, "Microsoft.ML.Tokenizers.Data.Cl100kBase/1.0.2": {"dependencies": {"Microsoft.ML.Tokenizers": "1.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.Cl100kBase.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.225.12505"}}}, "Microsoft.ML.Tokenizers.Data.O200kBase/1.0.2": {"dependencies": {"Microsoft.ML.Tokenizers": "1.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.O200kBase.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.225.12505"}}}, "Microsoft.ML.Tokenizers.Data.P50kBase/1.0.2": {"dependencies": {"Microsoft.ML.Tokenizers": "1.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.ML.Tokenizers.Data.P50kBase.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.225.12505"}}}, "Microsoft.Net.Compilers.Toolset/4.12.0": {}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.OpenApi/1.6.22": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.22.0", "fileVersion": "1.6.22.0"}}}, "Microsoft.OpenApi.Readers/1.6.22": {"dependencies": {"Microsoft.OpenApi": "1.6.22", "SharpYaml": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.Readers.dll": {"assemblyVersion": "1.6.22.0", "fileVersion": "1.6.22.0"}}}, "Microsoft.SemanticKernel/1.44.0": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Abstractions/1.44.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.VectorData.Abstractions": "9.0.0-preview.1.25161.1", "System.Diagnostics.DiagnosticSource": "9.0.3", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Agents.Abstractions/1.44.0-preview": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "System.Linq.Async": "6.0.1", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Agents.Abstractions.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Agents.Core/1.44.0-preview": {"dependencies": {"Microsoft.SemanticKernel.Agents.Abstractions": "1.44.0-preview", "Microsoft.SemanticKernel.Core": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Agents.Core.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.44.0": {"dependencies": {"Azure.AI.OpenAI": "2.2.0-beta.4", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Connectors.Google/1.44.0-alpha": {"dependencies": {"Microsoft.SemanticKernel.Abstractions": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Google.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Connectors.Ollama/1.44.0-alpha": {"dependencies": {"Microsoft.Extensions.AI": "9.3.0-preview.1.25161.3", "Microsoft.Net.Compilers.Toolset": "4.12.0", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "Microsoft.SemanticKernel.Core": "1.44.0", "OllamaSharp": "5.1.7"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Ollama.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.44.0": {"dependencies": {"Microsoft.SemanticKernel.Core": "1.44.0", "OpenAI": "2.2.0-beta.4"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Core/1.44.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.SemanticKernel.Abstractions": "1.44.0", "System.Numerics.Tensors": "9.0.3", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.SemanticKernel.Plugins.OpenApi/1.40.1": {"dependencies": {"JsonSchema.Net": "5.4.2", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "Microsoft.OpenApi": "1.6.22", "Microsoft.OpenApi.Readers": "1.6.22", "Microsoft.SemanticKernel.Core": "1.44.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Plugins.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "ModelContextProtocol/0.1.0-preview.6": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3", "Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.IO.Pipelines": "9.0.3", "System.Net.ServerSentEvents": "10.0.0-preview.2.25163.2"}, "runtime": {"lib/net8.0/ModelContextProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.Bson/3.2.1": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/net6.0/MongoDB.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.Driver/3.2.1": {"dependencies": {"DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "MongoDB.Bson": "3.2.1", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/net6.0/MongoDB.Driver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NetTopologySuite/2.5.0": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/NetTopologySuite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Npgsql/8.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "8.0.3.0", "fileVersion": "8.0.3.0"}}}, "NRedisStack/0.13.2": {"dependencies": {"NetTopologySuite": "2.5.0", "StackExchange.Redis": "2.8.16"}, "runtime": {"lib/net8.0/NRedisStack.dll": {"assemblyVersion": "0.13.2.0", "fileVersion": "0.13.2.0"}}}, "OllamaSharp/5.1.7": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25161.3"}, "runtime": {"lib/net8.0/OllamaSharp.dll": {"assemblyVersion": "5.1.7.0", "fileVersion": "5.1.7.0"}}}, "OpenAI/2.2.0-beta.4": {"dependencies": {"System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "9.0.3"}, "runtime": {"lib/net8.0/OpenAI.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "OpenTelemetry/1.11.2": {"dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Configuration": "9.0.3", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Api/1.11.2": {"dependencies": {"System.Diagnostics.DiagnosticSource": "9.0.3"}, "runtime": {"lib/net8.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.11.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "OpenTelemetry.Api": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Exporter.Console/1.11.2": {"dependencies": {"OpenTelemetry": "1.11.2", "System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/OpenTelemetry.Exporter.Console.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "OpenTelemetry.Extensions.Hosting/1.11.2": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "OpenTelemetry": "1.11.2"}, "runtime": {"lib/net8.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.11.2.1586"}}}, "PdfPig/0.1.10": {"runtime": {"lib/net8.0/UglyToad.PdfPig.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/UglyToad.PdfPig.DocumentLayoutAnalysis.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/UglyToad.PdfPig.Fonts.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/UglyToad.PdfPig.Package.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/UglyToad.PdfPig.Tokenization.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/UglyToad.PdfPig.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net8.0/UglyToad.PdfPig.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Pgvector/0.3.1": {"dependencies": {"Npgsql": "8.0.3"}, "runtime": {"lib/net6.0/Pgvector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"dependencies": {"System.IO.Pipelines": "9.0.3"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}}, "Polly.Core/8.5.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.5.2.4319"}}}, "RabbitMQ.Client/7.1.2": {"dependencies": {"System.IO.Pipelines": "9.0.3", "System.Threading.RateLimiting": "8.0.0"}, "runtime": {"lib/net8.0/RabbitMQ.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "RBush/4.0.0": {"runtime": {"lib/net8.0/RBush.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpYaml/2.1.1": {"runtime": {"lib/netstandard2.0/SharpYaml.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "StackExchange.Redis/2.8.16": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.16.12844"}}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.4.0-beta.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.3", "System.Memory.Data": "9.0.3"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.400.25.15605"}}}, "System.Configuration.ConfigurationManager/9.0.3": {"dependencies": {"System.Diagnostics.EventLog": "9.0.3", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Diagnostics.DiagnosticSource/9.0.3": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Diagnostics.EventLog/9.0.3": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Tokens": "7.5.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.5.0.50326"}}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.IO.Pipelines/9.0.3": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.35981"}}}, "System.Memory/4.5.5": {}, "System.Memory.Data/9.0.3": {"dependencies": {"System.Text.Json": "9.0.3"}, "runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Net.ServerSentEvents/10.0.0-preview.2.25163.2": {"runtime": {"lib/net8.0/System.Net.ServerSentEvents.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.0.25.16302"}}}, "System.Numerics.Tensors/9.0.3": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.Caching/9.0.3": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.3"}, "runtime": {"lib/net8.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.ProtectedData/9.0.3": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encodings.Web/9.0.3": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Text.Json/9.0.3": {"dependencies": {"System.IO.Pipelines": "9.0.3", "System.Text.Encodings.Web": "9.0.3"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Threading.Channels/9.0.3": {"runtime": {"lib/net8.0/System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Threading.RateLimiting/8.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ProjectApp.Core/1.0.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "FluentValidation.AspNetCore": "11.3.0", "MediatR": "12.4.1", "Newtonsoft.Json": "13.0.3"}, "runtime": {"ProjectApp.Core.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"ProjectApp.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "AWSSDK.Core/3.7.402.24": {"type": "package", "serviceable": true, "sha512": "sha512-xcUx7ntQE7DYcdlL0/IPb4DXPpQfH/OL11M9Swcm6iDPhX1S/Hu0neWVf5gBuRe58Sei0cX24qKjol+mDhcNwA==", "path": "awssdk.core/3.7.402.24", "hashPath": "awssdk.core.3.7.402.24.nupkg.sha512"}, "AWSSDK.S3/3.7.415.23": {"type": "package", "serviceable": true, "sha512": "sha512-ME1KxEwaxO5HMzjJSADVWl1oyfnEjqU3vOkVwot5jJZNQiz9Fhlbv8itpRmcoI23UCkDC3FDzzlrHvSjLewNaQ==", "path": "awssdk.s3/3.7.415.23", "hashPath": "awssdk.s3.3.7.415.23.nupkg.sha512"}, "Azure.AI.ContentSafety/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-62UgYaWYXlieHXnvF6EZjPahgYcuRe0jhsDZu8pMGhvUwkEsgmL4d2ag3Wlk+nYdLbKCE3TIPJmAPI/U/BVYJQ==", "path": "azure.ai.contentsafety/1.0.0", "hashPath": "azure.ai.contentsafety.1.0.0.nupkg.sha512"}, "Azure.AI.FormRecognizer/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R9mEeYFa2+EcCu5dOGOFG07nbELHY/2o6JtgJoxNTo2wtsonLnLwcKzX/sxOnYhpib4TJEBTUX0/ea0lL130Iw==", "path": "azure.ai.formrecognizer/4.1.0", "hashPath": "azure.ai.formrecognizer.4.1.0.nupkg.sha512"}, "Azure.AI.OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-qjCgspdq67x+urifvf7Dkz4tX5HVU3AlF2XUYU/kQBObKQihPsTYSQJ4tiMHEMNjaKRbfHzxnE2vnuhcqUUWCg==", "path": "azure.ai.openai/2.2.0-beta.4", "hashPath": "azure.ai.openai.2.2.0-beta.4.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "path": "azure.identity/1.13.2", "hashPath": "azure.identity.1.13.2.nupkg.sha512"}, "Azure.Search.Documents/11.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-M7WLx3ANLPHymfqb4Nwk4EwcWWRiHqdvnxJ7RH857baAbkEZ3FYVCRJmHgxH+ROpYOTVSx30uJzsa573/cdD8A==", "path": "azure.search.documents/11.6.0", "hashPath": "azure.search.documents.11.6.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-0SWiMtEYcemn5U69BqVPdqGDwcbl+lsF9L3WFPpqk1Db5g+ytr3L3GmUxMbvvdPNuFwTf03kKtWJpW/qW33T8A==", "path": "azure.storage.blobs/12.24.0", "hashPath": "azure.storage.blobs.12.24.0.nupkg.sha512"}, "Azure.Storage.Common/12.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "path": "azure.storage.common/12.23.0", "hashPath": "azure.storage.common.12.23.0.nupkg.sha512"}, "Azure.Storage.Queues/12.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-HPQgOlfH+rJ4CL4V8ePFnsT/KKnvLU35ytxC3fsTTqOazhQ0593C0aPVu258DRN8bQCbx4OpNpjtiO9czDy3VQ==", "path": "azure.storage.queues/12.22.0", "hashPath": "azure.storage.queues.12.22.0.nupkg.sha512"}, "BCrypt.Net-Core/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-hsKiHGjrnlDW05MzuzzlD9gNP8YOW2KbZ+FbvISrilsKJJ76tXKhWey6psZX5sd7DczNpSU2U6ldjPoblk8BLw==", "path": "bcrypt.net-core/1.6.0", "hashPath": "bcrypt.net-core.1.6.0.nupkg.sha512"}, "ClosedXML/0.104.2": {"type": "package", "serviceable": true, "sha512": "sha512-gOkSjQ152MhpKmw70cBkJV+FnaZAWzDwM36luRf/7FlWYnNeH++9XYdGTd0Y4KQlVPkKVxy948M5MMsnsGC4GQ==", "path": "closedxml/0.104.2", "hashPath": "closedxml.0.104.2.nupkg.sha512"}, "ClosedXML.Parser/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "path": "closedxml.parser/1.2.0", "hashPath": "closedxml.parser.1.2.0.nupkg.sha512"}, "CommunityToolkit.HighPerformance/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-flxspiBs0G/0GMp7IK2J2ijV9bTG6hEwFc/z6ekHqB6nwRJ4Ry2yLdx+TkbCUYFCl4XhABkAwomeKbT6zM2Zlg==", "path": "communitytoolkit.highperformance/8.4.0", "hashPath": "communitytoolkit.highperformance.8.4.0.nupkg.sha512"}, "Dapper/2.1.66": {"type": "package", "serviceable": true, "sha512": "sha512-/q77jUgDOS+bzkmk3Vy9SiWMaetTw+NOoPAV0xPBsGVAyljd5S6P+4RUW7R3ZUGGr9lDRyPKgAMj2UAOwvqZYw==", "path": "dapper/2.1.66", "hashPath": "dapper.2.1.66.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DocumentFormat.OpenXml/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JogRPJNiE6kKvbuCqVRX691pPWeGMqdQgjrUwRYkdpfkMmtElfqAgcRR73geYj7OtBeEpstldZXXzJw27LUI9w==", "path": "documentformat.openxml/3.3.0", "hashPath": "documentformat.openxml.3.3.0.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-R5CLzEoeyr7XDB7g3NTxRobcU19agaxVAhGZm+fZUShJGiU4bw8oUgnA2BNFepigJckfFMayOBMAbV3kDXNInA==", "path": "documentformat.openxml.framework/3.3.0", "hashPath": "documentformat.openxml.framework.3.3.0.nupkg.sha512"}, "Elastic.Clients.Elasticsearch/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-DBVRNLq9JPgRn5YtdNy1v2ghV8Zh5GJ0ms1U+jlfKyL8b9a/kp8PpeWoxDFmeFz2lL4OJNadxR8lHSQmskHsfw==", "path": "elastic.clients.elasticsearch/8.12.1", "hashPath": "elastic.clients.elasticsearch.8.12.1.nupkg.sha512"}, "Elastic.Transport/0.4.18": {"type": "package", "serviceable": true, "sha512": "sha512-Q9nGgYxB0r1jTkUf8zWbFKLk5JHliZhVU6vukdMLHLH/1EBKQvHzPahkrnV8KsvGOPFFqFAVOhQvaxpjXuS3TA==", "path": "elastic.transport/0.4.18", "hashPath": "elastic.transport.0.4.18.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "FluentValidation/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-0h1Q5lNOLLyYTWMJmyNoMqhY4CBRvvUWvJP1R4F2CnmmzuWwvB0A8aVmw5+lOuwYnwUwCRrdeMLbc81F38ahNQ==", "path": "fluentvalidation/11.5.1", "hashPath": "fluentvalidation.11.5.1.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-iWM0LS1MDYX06pcjMEQKqHirl2zkjHlNV23mEJSoR1IZI7KQmTa0RcTtGEJpj5+iHvBCfrzP2mYKM4FtRKVb+A==", "path": "fluentvalidation.dependencyinjectionextensions/11.5.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512"}, "Google.Protobuf/3.27.1": {"type": "package", "serviceable": true, "sha512": "sha512-7IVz9TzhYCZ8qY0rPhXUnyJSXYdshUqmmxmTI763XmDDSJJFnyfKH43FFcMJu/CZgBcE98xlFztrKwhzcRkiPg==", "path": "google.protobuf/3.27.1", "hashPath": "google.protobuf.3.27.1.nupkg.sha512"}, "Hangfire/1.8.18": {"type": "package", "serviceable": true, "sha512": "sha512-EY+UqMHTOQAtdjeJf3jlnj8MpENyDPTpA6OHMncucVlkaongZjrx+gCN4bgma7vD3BNHqfQ7irYrfE5p1DOBEQ==", "path": "hangfire/1.8.18", "hashPath": "hangfire.1.8.18.nupkg.sha512"}, "Hangfire.AspNetCore/1.8.18": {"type": "package", "serviceable": true, "sha512": "sha512-5D6Do0qgoAnakvh4KnKwhIoUzFU84Z0sCYMB+Sit+ygkpL1P6JGYDcd/9vDBcfr5K3JqBxD4Zh2IK2LOXuuiaw==", "path": "hangfire.aspnetcore/1.8.18", "hashPath": "hangfire.aspnetcore.1.8.18.nupkg.sha512"}, "Hangfire.Core/1.8.18": {"type": "package", "serviceable": true, "sha512": "sha512-oNAkV8QQoYg5+vM2M024NBk49EhTO2BmKDLuQaKNew23RpH9OUGtKDl1KldBdDJrD8TMFzjhWCArol3igd2i2w==", "path": "hangfire.core/1.8.18", "hashPath": "hangfire.core.1.8.18.nupkg.sha512"}, "Hangfire.NetCore/1.8.18": {"type": "package", "serviceable": true, "sha512": "sha512-3KAV9AZ1nqQHC54qR4buNEEKRmQJfq+lODtZxUk5cdi68lV8+9K2f4H1/mIfDlPpgjPFjEfCobNoi2+TIpKySw==", "path": "hangfire.netcore/1.8.18", "hashPath": "hangfire.netcore.1.8.18.nupkg.sha512"}, "Hangfire.SqlServer/1.8.18": {"type": "package", "serviceable": true, "sha512": "sha512-yBfI2ygYfN/31rOrahfOFHee1mwTrG0ppsmK9awCS0mAr2GEaB9eyYqg/lURgZy8AA8UVJVs5nLHa2hc1pDAVQ==", "path": "hangfire.sqlserver/1.8.18", "hashPath": "hangfire.sqlserver.1.8.18.nupkg.sha512"}, "HtmlAgilityPack/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-VHtVZmfoYhQyA/POvZRLuTpCz1zhzIDrdYRJIRV73e9wKAzjW71biYNOHOWx8MxEX3TE4TWVfx1QDRoZcj2AWw==", "path": "htmlagilitypack/1.12.0", "hashPath": "htmlagilitypack.1.12.0.nupkg.sha512"}, "JetBrains.Annotations/2021.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-kKSyoVfndMriKHLfYGmr0uzQuI4jcc3TKGyww7buJFCYeHb/X0kodYBPL7n9454q7v6ASiRmDgpPGaDGerg/Hg==", "path": "jetbrains.annotations/2021.2.0", "hashPath": "jetbrains.annotations.2021.2.0.nupkg.sha512"}, "Json.More.Net/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-MMjd2dOh32hLbcZg9YyA+7aEH9gu2cMTEAWrQY17in4+aEsPg2NtYTcwgWHJS9Tt2WUx+4iN1mNegR2uiEwsVQ==", "path": "json.more.net/1.9.0", "hashPath": "json.more.net.1.9.0.nupkg.sha512"}, "JsonPointer.Net/3.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mCGQc15lHLp1R2CVhWiipnZurHXm93+LbPPAT/vXQm5PdHt6WQuYLhaEF8VZ+aXL9P2I6bGND6pDTEfqFs6gig==", "path": "jsonpointer.net/3.0.3", "hashPath": "jsonpointer.net.3.0.3.nupkg.sha512"}, "JsonSchema.Net/5.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-NJyauE2/u877WaRCe01OkX7IIAStYrFmoVwAj4nPqaMwxXzu+vdbXB/ZzEYaLh5dVdJfB28N0txTI+RGQWYolA==", "path": "jsonschema.net/5.4.2", "hashPath": "jsonschema.net.5.4.2.nupkg.sha512"}, "LLamaSharp/0.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-mbVBDH9DmZKTLouaFHDUmHPhBw3gY09WrTKprPWKQSL5QBN9Gxbdc60uv/9t5KK7Xi5Z0pgCyA7pFJdR8XwEuw==", "path": "llamasharp/0.23.0", "hashPath": "llamasharp.0.23.0.nupkg.sha512"}, "MediatR/12.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-0tLxCgEC5+r1OCuumR3sWyiVa+BMv3AgiU4+pz8xqTc+2q1WbUEXFOr7Orm96oZ9r9FsldgUtWvB2o7b9jDOaw==", "path": "mediatr/12.4.1", "hashPath": "mediatr.12.4.1.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-7qJkk5k5jabATZZrMIQgpUB9yjDNAAApSqw+8d0FEyK1AJ4j+wv1qOMl2byUr837xbK+MjehtPnQ32yZ5Gtzlw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.2", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-oFFX9Ls8dnNUBCD9yzRzHTY8tqvv+CiX43B8L8DjrM8BqYTAlORYaJf6+KXNtSC2bD1135yV8OxzcZFaluow5w==", "path": "microsoft.bcl.asyncinterfaces/9.0.3", "hashPath": "microsoft.bcl.asyncinterfaces.9.0.3.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y3t/c7C5XHJGFDnohjf1/9SYF3ZOfEU1fkNQuKg/dGf9hN18yrQj2owHITGfNS3+lKJdW6J4vY98jYu57jCO8A==", "path": "microsoft.bcl.cryptography/8.0.0", "hashPath": "microsoft.bcl.cryptography.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "path": "microsoft.data.sqlclient/6.0.1", "hashPath": "microsoft.data.sqlclient.6.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.AI/9.3.0-preview.1.25161.3": {"type": "package", "serviceable": true, "sha512": "sha512-i1LLMJucYogyImy91Fm+eJv+AfsQ3gdpQnDcrsweWg6SWoa/xFlVnqYRtifsHwucRxv2da/JET1RMRiVB6rIug==", "path": "microsoft.extensions.ai/9.3.0-preview.1.25161.3", "hashPath": "microsoft.extensions.ai.9.3.0-preview.1.25161.3.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.3.0-preview.1.25161.3": {"type": "package", "serviceable": true, "sha512": "sha512-1BGoGmGQKwDOkf4I5/Ybt3GL5O+/3JPlZ/AM0uLspHn5DpPb2Wpi9HYKEMjTECFvW+pk+vtFaBYQmNdudl26fw==", "path": "microsoft.extensions.ai.abstractions/9.3.0-preview.1.25161.3", "hashPath": "microsoft.extensions.ai.abstractions.9.3.0-preview.1.25161.3.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-t8b0R6wtqC4o0hJ+oQkLPydw2MMLEoLEpQXCWbzXAm9NBMOngkDZNcvwF6DxbYdL5SlfZJXbYmiOxKZmwHNgNg==", "path": "microsoft.extensions.caching.abstractions/9.0.3", "hashPath": "microsoft.extensions.caching.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "path": "microsoft.extensions.caching.memory/8.0.1", "hashPath": "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-RIEeZxWYm77+OWLwgik7DzSVSONjqkmcbuCb1koZdGAV7BgOUWnLz80VMyHZMw3onrVwFCCMHBBdruBPuQTvkg==", "path": "microsoft.extensions.configuration/9.0.3", "hashPath": "microsoft.extensions.configuration.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-q5qlbm6GRUrle2ZZxy9aqS/wWoc+mRD3JeP6rcpiJTh5XcemYkplAcJKq8lU11ZfPom5lfbZZfnQvDqcUhqD5Q==", "path": "microsoft.extensions.configuration.abstractions/9.0.3", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ad82pYBUSQbd3WIboxsS1HzFdRuHKRa2CpYwie/o6dZAxUjt62yFwjoVdM7Iw2VO5fHV1rJwa7jJZBNZin0E7Q==", "path": "microsoft.extensions.configuration.binder/9.0.3", "hashPath": "microsoft.extensions.configuration.binder.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-rVwz4ml/Jve/QzzUlyTVOKXVZ37op9RK6Ize4uPmJ3S5c2ErExoy816+dslBQ06ZrFq8M9bpnV5LVBuPD1ONHQ==", "path": "microsoft.extensions.configuration.commandline/9.0.3", "hashPath": "microsoft.extensions.configuration.commandline.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-fo84UIa8aSBG3pOtzLsgkj1YkOVfYFy2YWcRTCevHHAkuVsxnYnKBrcW2pyFgqqfQ/rT8K1nmRXHDdQIZ8PDig==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.3", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tBNMSDJ2q7WQK2zwPhHY5I/q95t7sf6dT079mGrNm0yOZF/gM9JvR/LtCb/rwhRmh7A6XMnzv5WbpCh9KLq9EQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.3", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mjkp3ZwynNacZk4uq93I0DyCY48FZmi3yRV0xlfeDuWh44KcDunPXHwt8IWr4kL7cVM6eiFVe6YTJg97KzUAUA==", "path": "microsoft.extensions.configuration.json/9.0.3", "hashPath": "microsoft.extensions.configuration.json.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vwkBQ5jqmfX7nD7CFvB3k1uSeNBKRcYRDvlk3pxJzJfm/cgT4R+hQg5AFXW/1aLKjz0q7brpRocHC5GK2sjvEw==", "path": "microsoft.extensions.configuration.usersecrets/9.0.3", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "path": "microsoft.extensions.dependencyinjection/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-gqhbIq6adm0+/9IlDYmchekoxNkmUTm7rfTG3k4zzoQkjRuD8TQGwL1WnIcTDt4aQ+j+Vu0OQrjI8GlpJQQhIA==", "path": "microsoft.extensions.diagnostics/9.0.3", "hashPath": "microsoft.extensions.diagnostics.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-/fn0Xe8t+3YbMfwyTk4hFirWyAG1pBA5ogVYsrKAuuD2gbqOWhFuSA28auCmS3z8Y2eq3miDIKq4pFVRWA+J6g==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.3", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-umczZ3+QPpzlrW/lkvy+IB0p52+qZ5w++aqx2lTCMOaPKzwcbVdrJgiQ3ajw5QWBp7gChLUiCYkSlWUpfjv24g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.3", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-th2+tQBV5oWjgKhip9GjiIv2AEK3QvfAO3tZcqV3F3dEt5D6Gb411RntCj1+8GS9HaRRSxjSGx/fCrMqIjkb1Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.3", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Rec77KHk4iNpFznHi5/6wF3MlUDcKqg26t8gRYbUm1PSukZ4B6mrXpZsJSNOiwyhhQVkjYbaoZxi5XJgRQ5lFg==", "path": "microsoft.extensions.filesystemglobbing/9.0.3", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ioFXglqFA9uCYcKHI3CLVTO3I75jWIhvVxiZBzGeSPxw7XdhDLh0QvbNFrMTbZk9qqEVQcylblcvcNXnFHYXyA==", "path": "microsoft.extensions.hosting/9.0.3", "hashPath": "microsoft.extensions.hosting.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-rHabYVhQsGYNfgnfnYLqZRx/hLe85i6jW5rnDjA9pjt3x7yjPv8T/EXcgN5T9T38FAVwZRA+RMGUkEHbxvCOBQ==", "path": "microsoft.extensions.hosting.abstractions/9.0.3", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-rwChgI3lPqvUzsCN3egSW/6v4kP9/RQ2QrkZUwyAiHiwEoIB6QbYkATNvUsgjV6nfrekocyciCzy53ZFRuSaHA==", "path": "microsoft.extensions.http/9.0.3", "hashPath": "microsoft.extensions.http.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "path": "microsoft.extensions.logging/9.0.3", "hashPath": "microsoft.extensions.logging.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "path": "microsoft.extensions.logging.abstractions/9.0.3", "hashPath": "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-eVZsaKNyK0g0C1qp0mmn4Q2PiX+bXdkz8+zVkXyVMk8IvoWfmTjLjEq1MQlwt1A22lToANPiUrxPJ7Tt3V5puw==", "path": "microsoft.extensions.logging.configuration/9.0.3", "hashPath": "microsoft.extensions.logging.configuration.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-o9VXLOdpTAro1q7ZThIB3S8OHrRn5pr8cFUCiN85fiwlfAt2DhU4ZIfHy+jCNbf7y7S5Exbr3dlDE8mKNrs0Yg==", "path": "microsoft.extensions.logging.console/9.0.3", "hashPath": "microsoft.extensions.logging.console.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-BlKgvNYjD6mY5GXpMCf9zPAsrovMgW5mzCOT7SpoOSyI1478zldf+7PKvDIscC277z5zjSO3yi/OuIWpnTZmdA==", "path": "microsoft.extensions.logging.debug/9.0.3", "hashPath": "microsoft.extensions.logging.debug.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-/+elZUHGgB3oHKO9St/Ql/qfze9O+UbXj+9FOj1gIshLCFXcPlhpKoI11jE6eIV0kbs1P/EeffJl4KDFyvAiJQ==", "path": "microsoft.extensions.logging.eventlog/9.0.3", "hashPath": "microsoft.extensions.logging.eventlog.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-hgG0EGEHnngQFQNqJ5ungEykqaQ5Tik0Gpkb38pea2a5cR3pWlZR4vuYLDdtTgSiKEKByXz/3wNQ7qAqXamEEA==", "path": "microsoft.extensions.logging.eventsource/9.0.3", "hashPath": "microsoft.extensions.logging.eventsource.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "path": "microsoft.extensions.options/9.0.3", "hashPath": "microsoft.extensions.options.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-PcyYHQglKnWVZHSPaL6v2qnfsIuFw8tSq7cyXHg3OeuDVn/CqmdWUjRiZomCF/Gi+qCi+ksz0lFphg2cNvB8zQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.3", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "path": "microsoft.extensions.primitives/9.0.3", "hashPath": "microsoft.extensions.primitives.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.0.0-preview.1.25161.1": {"type": "package", "serviceable": true, "sha512": "sha512-irdKMpHB9v+S+DpdrYHIRb7ADqA9weRvmcvmoP2jb/x75b4GQGxVbFCcMRcyQaHPg9zOvqnKq8ZZZ4MUfDGujA==", "path": "microsoft.extensions.vectordata.abstractions/9.0.0-preview.1.25161.1", "hashPath": "microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.25161.1.nupkg.sha512"}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "path": "microsoft.identity.client/4.67.2", "hashPath": "microsoft.identity.client.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "path": "microsoft.identity.client.extensions.msal/4.67.2", "hashPath": "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-seOFPaBQh2K683eFujAuDsrO2XbOA+SvxRli+wu7kl+ZymuGQzjmmUKfyFHmDazpPOBnmOX1ZnjX7zFDZHyNIA==", "path": "microsoft.identitymodel.abstractions/7.5.0", "hashPath": "microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-mfyiGptbcH+oYrzAtWWwuV+7MoM0G0si+9owaj6DGWInhq/N/KDj/pWHhq1ShdmBu332gjP+cppjgwBpsOj7Fg==", "path": "microsoft.identitymodel.jsonwebtokens/7.5.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-3BInZEajJvnTDP/YRrmJ3Fyw8XAWWR9jG+3FkhhzRJJYItVL+BEH9qlgxSmtrxp7S7N6TOv+Y+X8BG61viiehQ==", "path": "microsoft.identitymodel.logging/7.5.0", "hashPath": "microsoft.identitymodel.logging.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ugyb0Nm+I+UrHGYg28mL8oCV31xZrOEbs8fQkcShUoKvgk22HroD2odCnqEf56CoAFYTwoDExz8deXzrFC+TyA==", "path": "microsoft.identitymodel.protocols/7.5.0", "hashPath": "microsoft.identitymodel.protocols.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "path": "microsoft.identitymodel.protocols.openidconnect/7.5.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-owe33wqe0ZbwBxM3D90I0XotxNyTdl85jud03d+OrUOJNnTiqnYePwBk3WU9yW0Rk5CYX+sfSim7frmu6jeEzQ==", "path": "microsoft.identitymodel.tokens/7.5.0", "hashPath": "microsoft.identitymodel.tokens.7.5.0.nupkg.sha512"}, "Microsoft.KernelMemory/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-F<PERSON>uaX2J26zH0UfwZ1YIoEy9pJW/RZHocoiTfhcP8cevyh4qRZl4gI3IY7aKUCkwZBhrRpNm22EpLb/qlBEQUQ==", "path": "microsoft.kernelmemory/0.98.250324.1", "hashPath": "microsoft.kernelmemory.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.Abstractions/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-WXvOhjhC8VzA3zAct5g6nBh2DNwabIVekll7a6vteIkpbG/XjoxQAmbrUZEQClTscbA1H8ctg13/zVxZ/afLcQ==", "path": "microsoft.kernelmemory.abstractions/0.98.250324.1", "hashPath": "microsoft.kernelmemory.abstractions.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Anthropic/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-IVVQyhq0FCBEeTUv516HHQv6oZk4yXz4rAVSYkiOai2ApX5BLZRijxuZs8Aus0LLNd2DFSLqrmfddUeJjUpjYA==", "path": "microsoft.kernelmemory.ai.anthropic/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.anthropic.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.AzureOpenAI/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-53EIU2ycYou/pcX1LEdh+2Ey1eXfXk2Nrnj7q4Jrl9SoA/9IwecgHTnccza2aD1kCmYkKG2RT1Jq70JtVCcyyQ==", "path": "microsoft.kernelmemory.ai.azureopenai/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.azureopenai.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.LlamaSharp/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-HoGwb0ybS64oeTFyhiEzPVtkRrxZBFTjf9keL6PircLfJkMEngZQVTklpx+QEAQOrreb/SmdEjgVX6G8xZRYKg==", "path": "microsoft.kernelmemory.ai.llamasharp/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.llamasharp.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Ollama/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-PCKwONAAYosm9n4OlazhMsS/Qe/W7BYxu+8NUCbs6gfWfmdJ2R3zDlxnj//xSBjmlpf6MecWcwgsVQQJx6Cbww==", "path": "microsoft.kernelmemory.ai.ollama/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.ollama.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Onnx/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-YxhEGsnJ/B6W6T3a2KztWNQOGf9jM8MnvXuH2z6XDMLsaGIoOhDqGzDliC57n0jnOoEsjwNp4f2Id89lh6BYJA==", "path": "microsoft.kernelmemory.ai.onnx/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.onnx.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.OpenAI/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-tcRW4FQqnNCJ+o3DgwjWk3z+h0g3cyo82glfqNnippbd6Pg66Zzx9hQPHElB/VtC3GpI5mP8VZfqDMmQzwK/QA==", "path": "microsoft.kernelmemory.ai.openai/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.openai.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.AI.Tiktoken/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-PhVANxgW1ZFE0f2gL6PTwrwGMoJzxdRBFN1aR1l17M/7my4uqOvSyvSEbATF4cuZJ2uDlSitvjZ2fpCf6tKF1A==", "path": "microsoft.kernelmemory.ai.tiktoken/0.98.250324.1", "hashPath": "microsoft.kernelmemory.ai.tiktoken.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.Chunkers/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mm6yOfyHVg89OVExuqIQy9UDY7N5qP5NqqziMj5RrD2VShGNhNZDCgSDzGCTHHA0O5/YAkJKQKAftbL3H5Kpdg==", "path": "microsoft.kernelmemory.chunkers/0.98.250324.1", "hashPath": "microsoft.kernelmemory.chunkers.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.Core/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-nyVP3OfiL5j+Zd7CYOMLI10EvV6C7usO2mKqvo1Daiu0v5QmMmGSP73iPnKSILrzyYQJPgZt81Dy7rYhvK5sow==", "path": "microsoft.kernelmemory.core/0.98.250324.1", "hashPath": "microsoft.kernelmemory.core.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.DataFormats.AzureAIDocIntel/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-F4aKx/5bRQ0EZPkgcUWlFyf4cieblpDNR+JxbODy/UBTAYEultSqXPA/xqV86qAckIK5y6P+7WE5R8LkxX5TGw==", "path": "microsoft.kernelmemory.dataformats.azureaidocintel/0.98.250324.1", "hashPath": "microsoft.kernelmemory.dataformats.azureaidocintel.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.DocumentStorage.AWSS3/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ti9axoH7NBffe2fzurGCbcjGXxRnoZDHEzQee5TfGWFGXyUfa1eGqi8RApa1MKGwJamS6BxPqOjPCuk0gM/egg==", "path": "microsoft.kernelmemory.documentstorage.awss3/0.98.250324.1", "hashPath": "microsoft.kernelmemory.documentstorage.awss3.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.DocumentStorage.AzureBlobs/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-t1EKYjihV+upSaSiD7dbz9wp/QZSgyZGsl9nA6khaHbqJDO/CRF1ysOSy4zOrZ1tsQQ9MknO6C8g/nt/eLinww==", "path": "microsoft.kernelmemory.documentstorage.azureblobs/0.98.250324.1", "hashPath": "microsoft.kernelmemory.documentstorage.azureblobs.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.AzureAISearch/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-FopCjDo/y91Rpce6e1a8nV/OGrsM0bC/QLlv4mONGvtUnw3iYXxjHkqRh6XY5o7VVpZyL2dDn4Sf6+sVzOg9ew==", "path": "microsoft.kernelmemory.memorydb.azureaisearch/0.98.250324.1", "hashPath": "microsoft.kernelmemory.memorydb.azureaisearch.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Elasticsearch/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-IUm1LTUjBoQ2B5LYjnyzYFlrbz39iRZlWYLdTu93BM5m+dDNtmkUveK9DKlKAIn65lhLn8DBHXtQOIsySGT+iw==", "path": "microsoft.kernelmemory.memorydb.elasticsearch/0.98.250324.1", "hashPath": "microsoft.kernelmemory.memorydb.elasticsearch.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Postgres/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-T0Pk/ClOBXp/DL4u/Dnm493atq1WQj1rt2JYgv9QW3OTFXT15uYoysqnwRuVnZY+21Qsz2o+ZiZ3XoeGhSTPug==", "path": "microsoft.kernelmemory.memorydb.postgres/0.98.250324.1", "hashPath": "microsoft.kernelmemory.memorydb.postgres.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Qdrant/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-/ay4qG9w+GoqgMItzW4k3rubsAPwPivyaQK12R1LcLQeTuTQuw66i7U32pur9S5kMKlMwfec4AZpB1hE6Mu1OA==", "path": "microsoft.kernelmemory.memorydb.qdrant/0.98.250324.1", "hashPath": "microsoft.kernelmemory.memorydb.qdrant.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.Redis/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-izCmzcfhHUnLInI/u2sIlZTztsoIkPdNR0PhqYoxLZSVbemusmp+8V7p3EGdhHIKorfFujiHDOeJQ06WfvzbYw==", "path": "microsoft.kernelmemory.memorydb.redis/0.98.250324.1", "hashPath": "microsoft.kernelmemory.memorydb.redis.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MemoryDb.SQLServer/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-Sx0u+S8L6BxbPOhfTF4oz5dNpO4354CN2Z8eRE1Hy/E6aSHK1FpwqTAlH1/tMXMejvxMpfZJFnr7VSXI8qIe/Q==", "path": "microsoft.kernelmemory.memorydb.sqlserver/0.98.250324.1", "hashPath": "microsoft.kernelmemory.memorydb.sqlserver.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.MongoDbAtlas/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-Bp4Zm05srMeT6U8xqvis7O0Zh81Y16y1F/TFOZNB8MnK1uBeqKihELSyU2NOABqMX7fizPkswJMjxzFFcgq7NQ==", "path": "microsoft.kernelmemory.mongodbatlas/0.98.250324.1", "hashPath": "microsoft.kernelmemory.mongodbatlas.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.Orchestration.AzureQueues/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-XkwuqvIjLT80XBDtysFObriYJL5LHQzCLQa+2dB1GpFjgXulUIjFYJqfA+0LJ1f7ZIDkPe4FDGXWXjzprDoc3A==", "path": "microsoft.kernelmemory.orchestration.azurequeues/0.98.250324.1", "hashPath": "microsoft.kernelmemory.orchestration.azurequeues.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.Orchestration.RabbitMQ/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-STTEYUyGe+FRFzqbjQcOJZvls+tt0r8o9D54py/JCHsA8XkkOXnXFlSfVx7CEaN0h5v/cKB0z7JfsG7Keg8nGw==", "path": "microsoft.kernelmemory.orchestration.rabbitmq/0.98.250324.1", "hashPath": "microsoft.kernelmemory.orchestration.rabbitmq.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.Safety.AzureAIContentSafety/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-2o/t7GBo1btTrk1c2PfIcKebq0K9i94zbZW405UJikKGHN+8DTtAY+qRJzU29MduQKkXB9/5lwNUdRvee475sg==", "path": "microsoft.kernelmemory.safety.azureaicontentsafety/0.98.250324.1", "hashPath": "microsoft.kernelmemory.safety.azureaicontentsafety.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.SemanticKernelPlugin/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-M1okykdLSjxB21HtQUgzUm+OS035TsbCsTC67LM1rRTkQPqWvB47dtT0vhDtGuTNqZZmitAzxq4++Rzv8WSIig==", "path": "microsoft.kernelmemory.semantickernelplugin/0.98.250324.1", "hashPath": "microsoft.kernelmemory.semantickernelplugin.0.98.250324.1.nupkg.sha512"}, "Microsoft.KernelMemory.WebClient/0.98.250324.1": {"type": "package", "serviceable": true, "sha512": "sha512-Lh6iGCwc82ty7MMDr2bkiFkMONkl3R0pNEJokuGKx/ndG1V6axBXmQMeetTAtU9w/+jeXH6cZYFUYZIQH4cRoA==", "path": "microsoft.kernelmemory.webclient/0.98.250324.1", "hashPath": "microsoft.kernelmemory.webclient.0.98.250324.1.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime/1.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-NzUD79BC6R7TKROLyB3XCzXTpbNZ1YfOdVbult7hoFvd/xyRecOMiRO5HTzkPXvaFlcvZ91+WNCiroAPzUE7Dw==", "path": "microsoft.ml.onnxruntime/1.20.1", "hashPath": "microsoft.ml.onnxruntime.1.20.1.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Managed/1.20.1": {"type": "package", "serviceable": true, "sha512": "sha512-+EVcEcKdq3gkw6iu8TYl2coJxfPnMi1YQc6eZ80BGNlw6mbBCxNGe6h4udDCwENYWsXFvYjoBc6412t40tl4ew==", "path": "microsoft.ml.onnxruntime.managed/1.20.1", "hashPath": "microsoft.ml.onnxruntime.managed.1.20.1.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI/0.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-nvX9OAKYuUNQqwVFu0Sv43XMesTmUo7kXM71ytWiFthd0ki58cnv4RV7XWloWFbRNK8SqP8oR6H8oQ48nqDbMg==", "path": "microsoft.ml.onnxruntimegenai/0.6.0", "hashPath": "microsoft.ml.onnxruntimegenai.0.6.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-s5LXrWrrcUpMag651gPNHYk9UIb+5xJDq2Ld75iVvlMxg34T+9vYl7MaOx84gp2dfrrTd5FDPCqjp/wO4d5ulw==", "path": "microsoft.ml.onnxruntimegenai.managed/0.6.0", "hashPath": "microsoft.ml.onnxruntimegenai.managed.0.6.0.nupkg.sha512"}, "Microsoft.ML.Tokenizers/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-MefL5+IakSE/CWzx4BQQairgY1fXFBVTPtnn7/01Wngsk2248loViqQ1M5+YB8KBwbi0HjcfoMo86h+cSjiEOA==", "path": "microsoft.ml.tokenizers/1.0.2", "hashPath": "microsoft.ml.tokenizers.1.0.2.nupkg.sha512"}, "Microsoft.ML.Tokenizers.Data.Cl100kBase/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-j7aVyeZMS7AqzHHz1JQ+11U5DHLS9EuqsqQ1n66w5HF78SKJNj4CKgFouU/0T4BJjZFzTBUZYJGg6HMOlTeBiQ==", "path": "microsoft.ml.tokenizers.data.cl100kbase/1.0.2", "hashPath": "microsoft.ml.tokenizers.data.cl100kbase.1.0.2.nupkg.sha512"}, "Microsoft.ML.Tokenizers.Data.O200kBase/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UYLc4tKczait1pwzPEo2E1Pj2bwYvl3VgO3+HPXzrOywF/eV5Bjdhm96nttRbUcW/NxsqWfPo122yeFKy2yvVQ==", "path": "microsoft.ml.tokenizers.data.o200kbase/1.0.2", "hashPath": "microsoft.ml.tokenizers.data.o200kbase.1.0.2.nupkg.sha512"}, "Microsoft.ML.Tokenizers.Data.P50kBase/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Vs+2gpb+pcTC9UTlXCtc7ZQbv4oSXG7wVhuV0hAnCG8v1QNQLKuGeGC/9u8DBDOUcFfN/DNh+29yyOjOfgn47A==", "path": "microsoft.ml.tokenizers.data.p50kbase/1.0.2", "hashPath": "microsoft.ml.tokenizers.data.p50kbase.1.0.2.nupkg.sha512"}, "Microsoft.Net.Compilers.Toolset/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xXDdIgDfcbMr+2bLzVJHg+/oCHwcYs808TlooX0M9+BVCeHITaROsjdbWSJNdyzeMe/K8iB2NeHmaAOUcqkKWw==", "path": "microsoft.net.compilers.toolset/4.12.0", "hashPath": "microsoft.net.compilers.toolset.4.12.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.22": {"type": "package", "serviceable": true, "sha512": "sha512-aBvunmrdu/x+4CaA/UP1Jx4xWGwk4kymhoIRnn2Vp+zi5/KOPQJ9EkSXHRUr01WcGKtYl3Au7XfkPJbU1G2sjQ==", "path": "microsoft.openapi/1.6.22", "hashPath": "microsoft.openapi.1.6.22.nupkg.sha512"}, "Microsoft.OpenApi.Readers/1.6.22": {"type": "package", "serviceable": true, "sha512": "sha512-F4h4yJp2UVVGGbH7KN0DTZnI/ABjS40w2+YgfhlWXfp3em+qlWexTRbisHfcRt42oi2Tf+6hfcD5X2QaaIi9jg==", "path": "microsoft.openapi.readers/1.6.22", "hashPath": "microsoft.openapi.readers.1.6.22.nupkg.sha512"}, "Microsoft.SemanticKernel/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-eTc9ohzRrzNO/JuN9ycRbi7sDyA2XLYWKwt/+SWi9FM3jHINVyNYmWW+kt0jZv2cvuk4+5+zUIq0UqrB5qwx7w==", "path": "microsoft.semantickernel/1.44.0", "hashPath": "microsoft.semantickernel.1.44.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-RVcg+JRSM0yH5PkhMe7h7pEjfEDYX38PAzrlzXFQgZudp+opUBLATG+YGWdeWnKgmDCcEGPccLLflqO1bXGLJg==", "path": "microsoft.semantickernel.abstractions/1.44.0", "hashPath": "microsoft.semantickernel.abstractions.1.44.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Agents.Abstractions/1.44.0-preview": {"type": "package", "serviceable": true, "sha512": "sha512-XQ8of37AVt8jmLbJFni3Yf7RQb5o/0IGsH6aqQQqVF8RLt0llUQoMOsObGc8qafpz9oqXSfp7r5XqQvJz1fNug==", "path": "microsoft.semantickernel.agents.abstractions/1.44.0-preview", "hashPath": "microsoft.semantickernel.agents.abstractions.1.44.0-preview.nupkg.sha512"}, "Microsoft.SemanticKernel.Agents.Core/1.44.0-preview": {"type": "package", "serviceable": true, "sha512": "sha512-LJi5LVE4RjG18FqB/zeeT7LLadbd0PpD0oeV/gO+YNjLaOjUp6zgvrfoS+jJRlxy28ytxJnaClCKas/25psSng==", "path": "microsoft.semantickernel.agents.core/1.44.0-preview", "hashPath": "microsoft.semantickernel.agents.core.1.44.0-preview.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-gHnXqLGzGQ50y4n3Qcv3RUdE0cerOyWf4tekDLeFqjefZUHGy0tofrNfmn1JI8IP0Hk/+djOd4L37hvFXb7UPA==", "path": "microsoft.semantickernel.connectors.azureopenai/1.44.0", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.44.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.Google/1.44.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-C9pHwK2X3Eq75Yq5ERiFseGokBGouOv50tgrKz0gNEmgB1oyyWad1v6jtPdvQq8Ams6s2ldRsydK9HPjvkrxWA==", "path": "microsoft.semantickernel.connectors.google/1.44.0-alpha", "hashPath": "microsoft.semantickernel.connectors.google.1.44.0-alpha.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.Ollama/1.44.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-drE8MVOievNocTdp1obsj9UX04Yn5v/hqJKCT5c3eTwCXjsBi136MAVX47G3tObP5RdAd4kAUpY1PY1ZHufopA==", "path": "microsoft.semantickernel.connectors.ollama/1.44.0-alpha", "hashPath": "microsoft.semantickernel.connectors.ollama.1.44.0-alpha.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-gnAHF2yBTE3QnZfJRwH8t+wLmUTeiDVFJdrRu4aK/RojHvn8CSrpshYOM1YRC2vQH2JQh3U9/zT/gibOE6y/2A==", "path": "microsoft.semantickernel.connectors.openai/1.44.0", "hashPath": "microsoft.semantickernel.connectors.openai.1.44.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-WHB5C+TBbZOjX3nJ21lS3oRokeKcIi2shSrSo95B1f/UDQyjh6K8p03eNWJMILcUWo4pNe53K/8Qh7uB5CbOQA==", "path": "microsoft.semantickernel.core/1.44.0", "hashPath": "microsoft.semantickernel.core.1.44.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Plugins.OpenApi/1.40.1": {"type": "package", "serviceable": true, "sha512": "sha512-vLu3fipqnIaEgQLEj0Kum30P5nqir2uDHNOrL0vhE86GySLp8922GZsjnrQ/KOzivxwjWoeFvxKQrc9l8gLN7A==", "path": "microsoft.semantickernel.plugins.openapi/1.40.1", "hashPath": "microsoft.semantickernel.plugins.openapi.1.40.1.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "ModelContextProtocol/0.1.0-preview.6": {"type": "package", "serviceable": true, "sha512": "sha512-gq6mQYvtaGC8lhWHBS4X5Ck53+HNWZPiqO7hOEOFRRLO30OlrXX9I+Uz9ShvfTAnjwvugV3TfMH2UbpBXusBtw==", "path": "modelcontextprotocol/0.1.0-preview.6", "hashPath": "modelcontextprotocol.0.1.0-preview.6.nupkg.sha512"}, "MongoDB.Bson/3.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-45w3BJU3sVejUaOVLAxQiEdWkNH+iyxMs3lDh4l2lUFbOb/s4uB++UoQY/3TCQrupQyx1/y7yzC5I76o4h7fFw==", "path": "mongodb.bson/3.2.1", "hashPath": "mongodb.bson.3.2.1.nupkg.sha512"}, "MongoDB.Driver/3.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-ynT4Mc0ilT+DSQBbobfORqCWqm5UdHIX3Nc59oQH3XbPi43jKiqf7SK8NLBmQsB6RJBJPR+WdIeNLtzyLj3U3g==", "path": "mongodb.driver/3.2.1", "hashPath": "mongodb.driver.3.2.1.nupkg.sha512"}, "NetTopologySuite/2.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-5/+2O2ADomEdUn09mlSigACdqvAf0m/pVPGtIPEPQWnyrVykYY0NlfXLIdkMgi41kvH9kNrPqYaFBTZtHYH7Xw==", "path": "nettopologysuite/2.5.0", "hashPath": "nettopologysuite.2.5.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6WEmzsQJCZAlUG1pThKg/RmeF6V+I0DmBBBE/8YzpRtEzhyZzKcK7ulMANDm5CkxrALBEC8H+5plxHWtIL7xnA==", "path": "npgsql/8.0.3", "hashPath": "npgsql.8.0.3.nupkg.sha512"}, "NRedisStack/0.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-tgtzv5D3HrC9frmCzd/va5xJldMhw3mZ4j56nQZGJOjnhfKjaR3dNHT04l9PSrMJZkHu4laKcvNMki9q28wAAA==", "path": "nredisstack/0.13.2", "hashPath": "nredisstack.0.13.2.nupkg.sha512"}, "OllamaSharp/5.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-khbyvzY8Yna0TGZL5J8S8ZGnIq+XNtot+Tg6FF4bTDGPFSa16P+VNdAcL0OUaFYQG2+iD30o3covWQ6F0MCSBQ==", "path": "ollamasharp/5.1.7", "hashPath": "ollamasharp.5.1.7.nupkg.sha512"}, "OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-JZ4/mlVXLaXDIZuC4Ddu0KCAA23z4Ax1AQTS26mpJRuSShjXik7DU8a3basY3ddD51W04F7jeX5eAXamKA6rHw==", "path": "openai/2.2.0-beta.4", "hashPath": "openai.2.2.0-beta.4.nupkg.sha512"}, "OpenTelemetry/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-FwonkaCVW8M9DLTHmAeJ+znsQCeOVvF4vSBworyq6f55RJB62LFmK7h7SG2aNERTknxP5RoGSwGOBPcVEgC07w==", "path": "opentelemetry/1.11.2", "hashPath": "opentelemetry.1.11.2.nupkg.sha512"}, "OpenTelemetry.Api/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-jgSd/FvtxPPc6nLaZnFj+bulHM2iQjy+NBCY5MbQjH6vkW/SfcXD9NMP3pKCmdF+SbZpgL+EoLQc+PmcnYYLlA==", "path": "opentelemetry.api/1.11.2", "hashPath": "opentelemetry.api.1.11.2.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-Y1aag4WT9f3rF8jQWwub5DsFVXpM/5NQsfYg6lmsNQrtJ6TcRqQu2PubcHXeIX2N6TA7XF3ffQAgeJklsSLeoQ==", "path": "opentelemetry.api.providerbuilderextensions/1.11.2", "hashPath": "opentelemetry.api.providerbuilderextensions.1.11.2.nupkg.sha512"}, "OpenTelemetry.Exporter.Console/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-d0XCfZiLsxufXz4b6SnVoQFL5j4nCq6AOUypYZKiSj1eqVS+MgApkuPA0JkIrEduVgLh70DdBbdFGfloD00Atw==", "path": "opentelemetry.exporter.console/1.11.2", "hashPath": "opentelemetry.exporter.console.1.11.2.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.11.2": {"type": "package", "serviceable": true, "sha512": "sha512-X0SZcZM9nv7+/WreH3q5McgxeaLBwN3ohsH/R58uAKeiuieqDxoAVFyQSQaRkpkrqIZSTTab6NHDQXglIreG0Q==", "path": "opentelemetry.extensions.hosting/1.11.2", "hashPath": "opentelemetry.extensions.hosting.1.11.2.nupkg.sha512"}, "PdfPig/0.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-9baMcYq7yL0QuaI0ZGq24EB5HY6oVa8ZhspEgYfo4gXpTLqk8RMIMlxtWr3mwuxguheIdscIFH2TN77y5y4L8g==", "path": "pdfpig/0.1.10", "hashPath": "pdfpig.0.1.10.nupkg.sha512"}, "Pgvector/0.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-XeuF5F5krjjv9/Mlt9vtl5Sd7B6ps82XhsA98qP5G6KAjL2bDPr+nvXTTXrJoKiwdE+R5VXoq1t0hWFkgbMyZw==", "path": "pgvector/0.3.1", "hashPath": "pgvector.0.3.1.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "Polly.Core/8.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-1MJKdxv4zwDmiWvYvVN24DsrWUfgQ4F83voH8bhbtLMdPuGy8CfTUzsgQhvyrl1a7hrM6f/ydwLVdVUI0xooUw==", "path": "polly.core/8.5.2", "hashPath": "polly.core.8.5.2.nupkg.sha512"}, "RabbitMQ.Client/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-y3c6ulgULScWthHw5PLM1ShHRLhxg0vCtzX/hh61gRgNecL3ZC3WoBW2HYHoXOVRqTl99Br9E7CZEytGZEsCyQ==", "path": "rabbitmq.client/7.1.2", "hashPath": "rabbitmq.client.7.1.2.nupkg.sha512"}, "RBush/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-j3GeRxxLUQdc+UrZnvythdQxi3bd8ayn87VDjfGXrvfodF550n9wR6SgQvpo+YiAv3GJezsu6lK0l47rRqnbdg==", "path": "rbush/4.0.0", "hashPath": "rbush.4.0.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SharpYaml/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-BISoFuW2AwZYXxrZGaBnedo21BvrdgC4kkWd6QYrOdhOGSsZB0RSqcBw09l9caUE1g3sykJoRfSbtSzZS6tYig==", "path": "sharpyaml/2.1.1", "hashPath": "sharpyaml.2.1.1.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "StackExchange.Redis/2.8.16": {"type": "package", "serviceable": true, "sha512": "sha512-WaoulkOqOC9jHepca3JZKFTqndCWab5uYS7qCzmiQDlrTkFaDN7eLSlEfHycBxipRnQY9ppZM7QSsWAwUEGblw==", "path": "stackexchange.redis/2.8.16", "hashPath": "stackexchange.redis.2.8.16.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.4.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZR0fKC94VS4P80vmxjk7l13/jPBXV0GMoE4jQfkYk8m2YV+dlw8jSC+b6eAfyBz0u+soN4CjhT3OdOC5KHaXxg==", "path": "system.clientmodel/1.4.0-beta.1", "hashPath": "system.clientmodel.1.4.0-beta.1.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-0kw80ykL/iFhNtFbPpjxIavmCk7/l6MqQwNSNhEoSNuI9C82ZZ251thzQZ/btHut21Y2G9M+amQY1K5bhaAjKg==", "path": "system.configuration.configurationmanager/9.0.3", "hashPath": "system.configuration.configurationmanager.9.0.3.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cBA+28xDW33tSiGht/H8xvr8lnaCrgJ7EdO348AfSGsX4PPJUOULKxny/cc9DVNGExaCrtqagsnm5M2mkWIZ+g==", "path": "system.diagnostics.diagnosticsource/9.0.3", "hashPath": "system.diagnostics.diagnosticsource.9.0.3.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-0nDJBZ06DVdTG2vvCZ4XjazLVaFawdT0pnji23ISX8I8fEOlRJyzH2I0kWiAbCtFwry2Zir4qE4l/GStLATfFw==", "path": "system.diagnostics.eventlog/9.0.3", "hashPath": "system.diagnostics.eventlog.9.0.3.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0TtrWOfoPdyYSlvOGaU9F1QR+qrbgJ/4eiEsQkIz7YQKIKkGXQldXukn6cYG9OahSq5UVMvyAIObECpH6Wglg==", "path": "system.identitymodel.tokens.jwt/7.5.0", "hashPath": "system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-aP1Qh9llcEmo0qN+VKvVDHFMe5Cqpfb1VjhBO7rjmxCXtLs3IfVSOiNqqLBZ/4Qbcr4J0SDdJq9S7EKAGpnwEA==", "path": "system.io.pipelines/9.0.3", "hashPath": "system.io.pipelines.9.0.3.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-QH23aqk1Cr1oSP9zEbjsJ60M7nbYOSEQLXszzxK12VXjEOXasnI8pnF7WeME66+z8OoecHfIL8iGxCRxjFQXFQ==", "path": "system.memory.data/9.0.3", "hashPath": "system.memory.data.9.0.3.nupkg.sha512"}, "System.Net.ServerSentEvents/10.0.0-preview.2.25163.2": {"type": "package", "serviceable": true, "sha512": "sha512-XHyvtQSgco0Sv0kz9yNBv93k3QOoAVzIVd5XbQoTqjV9sqkzWHsToNknyxtNjcXQwb+O9TfzSlNobsBWwnKD3Q==", "path": "system.net.serversentevents/10.0.0-preview.2.25163.2", "hashPath": "system.net.serversentevents.10.0.0-preview.2.25163.2.nupkg.sha512"}, "System.Numerics.Tensors/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-XnOUXX5va9ZtSIFuMrXeaJ0RPImEYXvJvhMQoAYAqppGgazuoI2inkMp77F1j8exEiHDj7omD7dPeqh+Nd1ZYA==", "path": "system.numerics.tensors/9.0.3", "hashPath": "system.numerics.tensors.9.0.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.Caching/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mp4QRjXcuakbQJon7ZZOS/dUn61bWJzX7GSNkxngKwpa7Mt2CD/Lb2cX4kWniALEM0vzDpavsybq0XQvlqpgGg==", "path": "system.runtime.caching/9.0.3", "hashPath": "system.runtime.caching.9.0.3.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-4PAwR9l3rhAESfymptisnO2KWpHmiTnKxovnREqmiY4BEakXD2ahQU4/NO0vzEarMw8RGzwpmWoiHwvL/waHfQ==", "path": "system.security.cryptography.protecteddata/9.0.3", "hashPath": "system.security.cryptography.protecteddata.9.0.3.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-5L+iI4fBMtGwt4FHLQh40/rgdbxnw6lHaLkR3gbaHG97TohzUv+z/oP03drsTR1lKCLhOkp40cFnHYOQLtpT5A==", "path": "system.text.encodings.web/9.0.3", "hashPath": "system.text.encodings.web.9.0.3.nupkg.sha512"}, "System.Text.Json/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-r2JRkLjsYrq5Dpo7+y3Wa73OfirZPdVhxiTJWwZ+oJM7FOAe0LkM3GlH+pgkNRdd1G1kwUbmRCdmh4uoaWwu1g==", "path": "system.text.json/9.0.3", "hashPath": "system.text.json.9.0.3.nupkg.sha512"}, "System.Threading.Channels/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Ao0iegVONKYVw0eWxJv0ArtMVfkFjgyyYKtUXru6xX5H95flSZWW3QCavD4PAgwpc0ETP38kGHaYbPzSE7sw2w==", "path": "system.threading.channels/9.0.3", "hashPath": "system.threading.channels.9.0.3.nupkg.sha512"}, "System.Threading.RateLimiting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7mu9v0QDv66ar3DpGSZHg9NuNcxDaaAcnMULuZlaTpP9+hwXhrxNGsF5GmLkSHxFdb5bBc1TzeujsRgTrPWi+Q==", "path": "system.threading.ratelimiting/8.0.0", "hashPath": "system.threading.ratelimiting.8.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "ProjectApp.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}