﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using ProjectApp.Core.Dtos;

namespace ProjectApp.Core.Repositories
{
    public interface IFileRepository
    {
        Task<ResponseMessage> UploadFiles(List<IFormFile> files, string source = null);
        Task<(byte[] FileBytes, string MimeType, bool IsError, string Message)> GetFile(string fileName);
        Task<string> GetDescription(string fileName);
        Task<string> GetAIAnalysis(string fileName);
        Task<ResponseMessage> DeleteFiles(List<string> fileNames);
        Task<List<FileDto>> GetAllFiles(string source = null);
        Task<ResponseMessage> UpdateAIAnalysis(string fileName, string aiAnalysis);
        Task<ResponseMessage> UpdateFileAIAnalysis(string fileName, string aiAnalysis);
        Task<FileDto> GetFileDto(string fileName);
    }
}
