﻿using Dapper;
using Microsoft.SemanticKernel;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class DocumentPlugin(IDbConnection _dbConnection)
    {

        [KernelFunction("get_document_memories")]
        [Description("Gets document memories by document IDs")]
        public async Task<List<MatchedDocument>> GetDocumentMemoriesAsync(List<string> documentIds)
        {
            if (documentIds == null || !documentIds.Any())
                return new List<MatchedDocument>();

            string sqlQuery = "SELECT Title, ExtractedContent, Files, Id FROM [dbo].[DocsData] WHERE Id IN @Ids";
            var documents = await _dbConnection.QueryAsync<MatchedDocument>(sqlQuery, new { Ids = documentIds });
            return documents.ToList();
        }
    }
}
