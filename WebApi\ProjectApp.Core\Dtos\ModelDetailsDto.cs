﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Dtos
{
    public class ModelDetailsDto
    {
        public Guid Id { get; set; }
        public string ModelName { get; set; }
        public string ModelProvider { get; set; }
        public bool IsActive { get; set; }
        public bool IsCustom { get; set; }
        public Guid? ApiCredentialsId { get; set; }
        public List<string> AgentNames { get; set; } = new List<string>();
    }
    public class ModelDetailsNameDto
    {
        public string ModelName { get; set; }
    }
    public class EmbeddingModelDto
    {
        public string ModelName { get; set; }
        public bool IsEmbeddingActive { get; set; }
        public string Provider { get; set; }
    }
    public class EmbeddingModelAndKey
    {
        public string ModelName { get; set; }
        public string APIKey { get; set; }
    }
}
