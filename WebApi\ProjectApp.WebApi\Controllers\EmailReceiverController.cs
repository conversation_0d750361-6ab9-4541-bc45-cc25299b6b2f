using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Services;
using Hangfire;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EmailReceiverController : ControllerBase
    {
        private readonly IEmailReceiverService _emailReceiverService;
        private readonly ILogger<EmailReceiverController> _logger;
        private readonly EmailProcessingJob _emailProcessingJob;

        public EmailReceiverController(
            IEmailReceiverService emailReceiverService,
            ILogger<EmailReceiverController> logger,
            EmailProcessingJob emailProcessingJob)
        {
            _emailReceiverService = emailReceiverService;
            _logger = logger;
            _emailProcessingJob = emailProcessingJob;
        }

        [HttpGet("test-connection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                var isConnected = await _emailReceiverService.TestConnectionAsync();
                return Ok(new { success = isConnected, message = isConnected ? "Email connection successful" : "Email connection failed" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing email connection");
                return StatusCode(500, new { success = false, message = "Internal server error" });
            }
        }

        [HttpPost("trigger-job")]
        public IActionResult TriggerJob()
        {
            try
            {
                // Use a direct enqueue with the job instance
                BackgroundJob.Enqueue(() => _emailProcessingJob.ProcessNewEmailsJob());
                return Ok(new { success = true, message = "Email processing job triggered successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering email processing job");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpPost("process-now")]
        public async Task<IActionResult> ProcessNow()
        {
            try
            {
                await _emailProcessingJob.ProcessNewEmailsJob();
                return Ok(new { success = true, message = "Emails processed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing emails directly");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpPost("restart-recurring-job")]
        public IActionResult RestartRecurringJob()
        {
            try
            {
                // Remove any existing email processing jobs
                RecurringJob.RemoveIfExists("ProcessEmails");
                RecurringJob.RemoveIfExists("ProcessNewEmails");
                RecurringJob.RemoveIfExists("EmailProcessing");                // Create new recurring job - every 5 minutes
                RecurringJob.AddOrUpdate(
                    "ProcessEmails",
                    () => _emailProcessingJob.ProcessNewEmailsJob(),
                    "*/5 * * * *"
                );

                return Ok(new { success = true, message = "Email processing recurring job restarted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restarting recurring job");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }
    }
}
