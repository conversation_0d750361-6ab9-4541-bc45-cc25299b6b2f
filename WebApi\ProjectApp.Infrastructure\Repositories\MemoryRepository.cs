﻿using AutoMapper;
using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class MemoryRepository : IMemoryRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly IExtractEmailFromAccessor _extractEmail;
        private readonly IMapper _mapper;
        private readonly AIService _aiService;
        public MemoryRepository(IDbConnection dbConnection, IExtractEmailFromAccessor extractEmail, IMapper mapper, AIService aiService)
        {
            _dbConnection = dbConnection;
            _extractEmail = extractEmail;
            _mapper = mapper;
            _aiService = aiService;
            
        }

        public async Task<Memory> CreateOrEditMemory(MemoryDto memoryDto)
        {
            var memory = new Memory();
            if (memoryDto.Id != Guid.Empty)
            {
                var updateQuery = "UPDATE Memory SET Content = @Content, ExtractedContent = @ExtractedContent  WHERE Id = @Id";

                var extractedContent = await ExtractContent(memoryDto.Content);
                memory = _mapper.Map<Memory>(memoryDto);
                memory.ExtractedContent = extractedContent;
                await _dbConnection.ExecuteAsync(updateQuery, memory);
                await _aiService.DeleteSingleMemory(memory.Id.ToString(), "Memory");
            }
            else
            {
                memory = _mapper.Map<Memory>(memoryDto);
                var extractedContent = await ExtractContent(memoryDto.Content);
                memory.ExtractedContent = extractedContent;
                memory.Id = Guid.NewGuid();
                memory.Email = _extractEmail.GetEmail();
                memory.CreatedAt = DateTime.Now;

                var insertQuery = "INSERT INTO Memory (Id, Content, Email, CreatedAt, ExtractedContent) VALUES (@Id, @Content, @Email, @CreatedAt, @ExtractedContent);";
                await _dbConnection.ExecuteScalarAsync<Guid>(insertQuery, memory);
            }

            
            var memoryTags = new List<MemoryTag>
                {
                   new MemoryTag
                   {
                       Name = "Email",
                       Value = _extractEmail.GetEmail()
                   }
                };
            await _aiService.AddMemory(memory.ExtractedContent, memory.Id.ToString(), "Memory", memoryTags);
            return memory;
        }

        public async Task<Memory> DeleteMemory(Guid id)
        {
            var existingMemory = await _dbConnection.QueryFirstOrDefaultAsync<Memory>("SELECT * FROM Memory WHERE Id = @Id", new { Id = id });

            if (existingMemory != null)
            {
                var deleteQuery = "DELETE FROM Memory WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(deleteQuery, new { Id = id });
                await _aiService.DeleteSingleMemory(id.ToString(), "Memory");
                return existingMemory;
            }
            else
            {
                throw new Exception("Memory not found");
            }
        }

        public async Task<List<Memory>> GetAllMemories()
        {
            var email = _extractEmail.GetEmail();
            var memories = await _dbConnection.QueryAsync<Memory>("SELECT * FROM Memory WHERE Email = @Email", new { Email = email });
            return memories.ToList();
        }

        public async Task<Memory> GetMemoryById(Guid id)
        {
            var memory = await _dbConnection.QueryFirstOrDefaultAsync<Memory>("SELECT * FROM Memory WHERE Id = @Id", new { Id = id });
            if (memory != null)
            {
                return memory;
            }
            else
            {
                throw new Exception("Memory not found");
            }
        }
        public async Task<string> ExtractContent(string content)
        {
            var extractedTextBuilder = new System.Text.StringBuilder();

            using (var jsonDoc = JsonDocument.Parse(content))
            {
                void Traverse(JsonElement element)
                {
                    if (element.ValueKind == JsonValueKind.Object)
                    {
                        // Handle link blocks specifically
                        if (element.TryGetProperty("type", out var typeProp) && typeProp.GetString() == "link")
                        {
                            if (element.TryGetProperty("data", out var dataProp))
                            {
                                // Extract link URL
                                if (dataProp.TryGetProperty("link", out var linkProp))
                                {
                                    extractedTextBuilder.AppendLine(linkProp.GetString());
                                }

                                // Extract link metadata
                                if (dataProp.TryGetProperty("meta", out var metaProp))
                                {
                                    if (metaProp.TryGetProperty("title", out var titleProp))
                                    {
                                        extractedTextBuilder.AppendLine(titleProp.GetString());
                                    }
                                    if (metaProp.TryGetProperty("description", out var descProp))
                                    {
                                        extractedTextBuilder.AppendLine(descProp.GetString());
                                    }
                                }
                            }
                            return;
                        }

                        foreach (var property in element.EnumerateObject())
                        {
                            if (property.Name == "text" || property.Name == "content")
                            {
                                if (property.Value.ValueKind == JsonValueKind.String)
                                {
                                    extractedTextBuilder.AppendLine(property.Value.GetString());
                                }
                            }
                            Traverse(property.Value);
                        }
                    }
                    else if (element.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var item in element.EnumerateArray())
                        {
                            Traverse(item);
                        }
                    }
                }

                Traverse(jsonDoc.RootElement);
            }

            return extractedTextBuilder.ToString();
        }
    }
}
