using Dapper;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class EmailProcessingResultRepository : IEmailProcessingResultRepository
    {
        private readonly IDbConnection _dbConnection;

        public EmailProcessingResultRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<EmailProcessingResult>> GetAllByUserEmailAsync(string userEmail)
        {
            var sql = @"SELECT * FROM EmailProcessingResults 
                       WHERE UserEmail = @UserEmail 
                       ORDER BY ProcessedAt DESC";
            return await _dbConnection.QueryAsync<EmailProcessingResult>(sql, new { UserEmail = userEmail });
        }

        public async Task<IEnumerable<EmailProcessingResult>> GetRecentResultsAsync(string userEmail, int count = 50)
        {
            var sql = @"SELECT TOP(@Count) * FROM EmailProcessingResults 
                       WHERE UserEmail = @UserEmail 
                       ORDER BY ProcessedAt DESC";
            return await _dbConnection.QueryAsync<EmailProcessingResult>(sql, new { UserEmail = userEmail, Count = count });
        }

        public async Task<EmailProcessingResult> CreateAsync(EmailProcessingResult result)
        {
            var sql = @"INSERT INTO EmailProcessingResults 
                       (UserEmail, SenderEmail, EmailSubject, EmailContent, AgentName, AgentResponse, ProcessedAt, MessageId) 
                       VALUES 
                       (@UserEmail, @SenderEmail, @EmailSubject, @EmailContent, @AgentName, @AgentResponse, @ProcessedAt, @MessageId);
                       SELECT CAST(SCOPE_IDENTITY() as int)";
            
            var id = await _dbConnection.QuerySingleAsync<int>(sql, result);
            result.Id = id;
            return result;
        }

        public async Task DeleteOldResultsAsync(DateTime olderThan)
        {
            var sql = "DELETE FROM EmailProcessingResults WHERE ProcessedAt < @OlderThan";
            await _dbConnection.ExecuteAsync(sql, new { OlderThan = olderThan });
        }
    }
}
