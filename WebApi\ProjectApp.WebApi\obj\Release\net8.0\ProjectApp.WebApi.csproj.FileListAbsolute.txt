C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\appsettings.Development.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\appsettings.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\libman.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.staticwebassets.runtime.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.exe
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.deps.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.runtimeconfig.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.WebApi.pdb
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.AI.ContentSafety.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.AI.FormRecognizer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.AI.OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.Identity.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.Search.Documents.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.Storage.Blobs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.Storage.Common.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Azure.Storage.Queues.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\BCrypt.Net-Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ClosedXML.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ClosedXML.Parser.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\CommunityToolkit.HighPerformance.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Dapper.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\DnsClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\DocumentFormat.OpenXml.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\DocumentFormat.OpenXml.Framework.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Elastic.Clients.Elasticsearch.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Elastic.Transport.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ExcelNumberFormat.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\FluentValidation.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Google.Protobuf.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Hangfire.AspNetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Hangfire.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Hangfire.NetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Hangfire.SqlServer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\HtmlAgilityPack.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\JetBrains.Annotations.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Json.More.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\JsonPointer.Net.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\JsonSchema.Net.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\LLamaSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\MediatR.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\MediatR.Contracts.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Bcl.Cryptography.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Bcl.HashCode.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.AI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.AI.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Http.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.EventLog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Logging.EventSource.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Extensions.VectorData.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.All.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.Anthropic.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.AzureOpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.LlamaSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.Ollama.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.Onnx.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.AI.Tiktoken.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Chunkers.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.DocumentStorage.AWSS3.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Postgres.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.MemoryDb.Qdrant.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.MemoryDb.Redis.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.MemoryDb.SQLServer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.MongoDbAtlas.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Orchestration.AzureQueues.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Orchestration.RabbitMQ.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.SemanticKernelPlugin.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.KernelMemory.WebClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.ML.OnnxRuntimeGenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.ML.Tokenizers.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.ML.Tokenizers.Data.Cl100kBase.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.ML.Tokenizers.Data.O200kBase.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.ML.Tokenizers.Data.P50kBase.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.OpenApi.Readers.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Agents.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Agents.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Connectors.Google.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Connectors.Ollama.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Connectors.OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SemanticKernel.Plugins.OpenApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ModelContextProtocol.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\NetTopologySuite.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Npgsql.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\NRedisStack.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OllamaSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OpenTelemetry.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.DocumentLayoutAnalysis.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.Fonts.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.Package.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.Tokenization.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.Tokens.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\UglyToad.PdfPig.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Pgvector.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Polly.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\RBush.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Serilog.Sinks.MSSqlServer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\SharpCompress.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\SharpYaml.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Snappier.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\StackExchange.Redis.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.ClientModel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.IO.Hashing.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.IO.Packaging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Linq.Async.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Memory.Data.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Net.ServerSentEvents.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Numerics.Tensors.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\System.Threading.Channels.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ca\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\de\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\es\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\fa\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\fr\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\nb\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\nl\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\pt-BR\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\pt-PT\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\pt\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\sv\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\tr-TR\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\zh-TW\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\zh\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\cs\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\de\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\es\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\fr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\it\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ja\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ko\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\pl\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\pt-BR\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ru\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\tr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\zh-Hans\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\zh-Hant\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\android\native\onnxruntime.aar
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\ios\native\onnxruntime.xcframework.zip
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\linux-arm64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\linux-x64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\osx-arm64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\osx-x64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x86\native\onnxruntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x86\native\onnxruntime.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\android\native\onnxruntime-genai.aar
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\ios\native\onnxruntime-genai.xcframework.zip
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\linux-x64\native\libonnxruntime-genai.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\osx-arm64\native\libonnxruntime-genai.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\osx-x64\native\libonnxruntime-genai.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-arm64\native\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win-x64\native\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.Messages.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win\lib\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\win\lib\net8.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.Infrastructure.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.Core.pdb
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\bin\Release\net8.0\ProjectApp.Infrastructure.pdb
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.AssemblyInfo.cs
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.sourcelink.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\scopedcss\bundle\ProjectApp.WebApi.styles.css
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets\msbuild.ProjectApp.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets\msbuild.ProjectApp.WebApi.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets\msbuild.build.ProjectApp.WebApi.props
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.ProjectApp.WebApi.props
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.ProjectApp.WebApi.props
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectA.3301ED16.Up2Date
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\refint\ProjectApp.WebApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.pdb
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ProjectApp.WebApi.genruntimeconfig.cache
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\ref\ProjectApp.WebApi.dll
