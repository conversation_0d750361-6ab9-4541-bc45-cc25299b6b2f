﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugin-details.3az2yg0702.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugin-details.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3az2yg0702"},{"Name":"integrity","Value":"sha256-G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I="},{"Name":"label","Value":"_content/ProjectApp.WebApi/plugin-details.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9832"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugin-details.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugin-details.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9832"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugins.367d1cpt86.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugins.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"367d1cpt86"},{"Name":"integrity","Value":"sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="},{"Name":"label","Value":"_content/ProjectApp.WebApi/plugins.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23350"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugins.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugins.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23350"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image.536fkm048a.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image.jpeg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:44:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:44:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.536fkm048a.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_1.jpeg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:48:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:48:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_2.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:30:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:30:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_3.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:38:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:38:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_4.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:43:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:43:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:28:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:28:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_1.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 09:34:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 09:34:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_2.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_2.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 01:54:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_2.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 01:54:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_3.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_3.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 01:58:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_3.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 01:58:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_4.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_4.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:02:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_4.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:02:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_5.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_5.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:49:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_5.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:49:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_6.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_6.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 08:51:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_6.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 08:51:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/landingPage.929y9semqo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\landingPage.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"929y9semqo"},{"Name":"integrity","Value":"sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/landingPage.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"292001"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 24 Mar 2025 19:56:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/landingPage.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\landingPage.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"292001"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 24 Mar 2025 19:56:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:53:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault.x27hb5tlzi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x27hb5tlzi"},{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/maxresdefault.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:53:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault_1.x27hb5tlzi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x27hb5tlzi"},{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/maxresdefault_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 02:30:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 02:30:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:20:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_1.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:20:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_2.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:23:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_2.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_2.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:23:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_3.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:26:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_3.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_3.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:26:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_4.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 06:57:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_4.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_4.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 06:57:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_5.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 02:39:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_5.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_5.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 02:39:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_6.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:35:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_6.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_6.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:35:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_7.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_7.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 05:09:55 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_7.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_7.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_7.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 05:09:55 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:15:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:15:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg.hk1hg0l2za.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hk1hg0l2za"},{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/robot leg.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:14:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:14:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg_1.hk1hg0l2za.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hk1hg0l2za"},{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/robot leg_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:18:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:18:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:30:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:30:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_1.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:44:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:44:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_2.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe_2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:46:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:46:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_3.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe_3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:50:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:50:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/wwwroot.vp75h618bc.zip">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\wwwroot.zip'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vp75h618bc"},{"Name":"integrity","Value":"sha256-rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM="},{"Name":"label","Value":"_content/ProjectApp.WebApi/wwwroot.zip"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6260"},{"Name":"Content-Type","Value":"application/x-zip-compressed"},{"Name":"ETag","Value":"\u0022rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM=\u0022"},{"Name":"Last-Modified","Value":"Fri, 11 Apr 2025 06:19:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/wwwroot.zip">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\wwwroot.zip'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6260"},{"Name":"Content-Type","Value":"application/x-zip-compressed"},{"Name":"ETag","Value":"\u0022rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM=\u0022"},{"Name":"Last-Modified","Value":"Fri, 11 Apr 2025 06:19:14 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>