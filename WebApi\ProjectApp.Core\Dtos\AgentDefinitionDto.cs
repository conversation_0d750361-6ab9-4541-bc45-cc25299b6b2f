﻿using System.Collections.Generic;

namespace ProjectApp.Core.Dtos;

public class AgentDefinitionDto
{
    public string AgentName { get; set; }
    public string Instructions { get; set; }
    public string UserInstructions { get; set; }
    public string ModelName { get; set; }
    public string Workspace { get; set; }
    public string[] Tools { get; set; }
    public Dictionary<string, object> Arguments { get; set; } = new Dictionary<string, object>();
}

public class AgentNameDto
{
    public string AgentName { get; set; }
}
