using System;

namespace ProjectApp.Core.Models
{
    public class EmailProcessingResult
    {
        public int Id { get; set; }
        public string UserEmail { get; set; } // Admin email (receiver)
        public string SenderEmail { get; set; } // Original sender of the email
        public string EmailSubject { get; set; }
        public string EmailContent { get; set; } // The content sent to the agent
        public string AgentName { get; set; } // Which agent processed this
        public string AgentResponse { get; set; } // AI response
        public DateTime ProcessedAt { get; set; } = DateTime.Now;
        public string MessageId { get; set; } // Email message ID for tracking
    }
}
