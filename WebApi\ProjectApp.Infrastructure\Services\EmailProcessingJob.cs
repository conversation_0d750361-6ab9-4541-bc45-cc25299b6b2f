using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.Services
{
    /// <summary>
    /// Hangfire job for processing emails every 3 minutes
    /// </summary>
    public class EmailProcessingJob
    {
        private readonly ILogger<EmailProcessingJob> _logger;
        private readonly IEmailReceiverService _emailReceiverService;
        private readonly EmailProcessingService _emailProcessingService;
        private readonly EmailReceiverSettings _settings;

        public EmailProcessingJob(
            ILogger<EmailProcessingJob> logger,
            IEmailReceiverService emailReceiverService,
            EmailProcessingService emailProcessingService,
            IOptions<EmailReceiverSettings> settings)
        {
            _logger = logger;
            _emailReceiverService = emailReceiverService;
            _emailProcessingService = emailProcessingService;
            _settings = settings.Value;
        }

        /// <summary>
        /// Hangfire job method to check and process new emails
        /// </summary>
        public async Task ProcessNewEmailsJob()
        {
            try
            {
                if (!_settings.IsEnabled)
                {
                    _logger.LogDebug("Email receiver is disabled, skipping email processing");
                    return;
                }

                _logger.LogInformation("Starting scheduled email processing job");

                // Get new emails
                var newEmails = await _emailReceiverService.GetNewEmailsAsync();
                
                if (newEmails.Count == 0)
                {
                    _logger.LogDebug("No new emails found");
                    return;
                }

                _logger.LogInformation($"Found {newEmails.Count} new emails to process");

                // Process each email
                foreach (var email in newEmails)
                {
                    try
                    {
                        _logger.LogInformation($"Processing email from {email.From} with subject: {email.Subject}");
                        
                        // Process the email - extract content, select agent, generate response
                        await _emailProcessingService.ProcessEmailAsync(email);
                        
                        _logger.LogInformation($"Successfully processed email from {email.From}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to process email from {email.From}: {ex.Message}");
                        // Continue processing other emails even if one fails
                    }
                }

                _logger.LogInformation($"Completed email processing job - processed {newEmails.Count} emails");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in email processing job: {ex.Message}");
            }
        }
    }
}
