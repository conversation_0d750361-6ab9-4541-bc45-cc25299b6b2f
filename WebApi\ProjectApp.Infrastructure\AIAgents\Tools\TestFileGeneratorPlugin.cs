using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Microsoft.SemanticKernel;

namespace ProjectApp.Infrastructure.AIAgents.Tools;

public class TestFileGeneratorPlugin
{
    private readonly string _uploadFolder;

    public TestFileGeneratorPlugin()
    {
        // Mimic FileRepository: wwwroot/uploads under the main WebApi project
        var baseDir = AppDomain.CurrentDomain.BaseDirectory;
        var webApiRoot = baseDir;
        // Traverse up to find the WebApi project root (bin/Debug/net8.0/ -> ProjectApp.WebApi)
        for (int i = 0; i < 3; i++)
        {
            webApiRoot = Directory.GetParent(webApiRoot)?.FullName ?? webApiRoot;
        }
        var webRoot = Path.Combine(webApiRoot, "wwwroot");
        _uploadFolder = Path.Combine(webRoot, "uploads");
        if (!Directory.Exists(_uploadFolder))
        {
            Directory.CreateDirectory(_uploadFolder);
        }
    }

    [KernelFunction("generate_invoice_file")]
    [Description("Generates an invoice file in wwwroot/uploads based on provided details and returns the file name.")]
    public string GenerateInvoiceFile(
        [Description("Invoice number")] string invoiceNumber,
        [Description("Customer name")] string customerName,
        [Description("Invoice date (yyyy-MM-dd)")] string invoiceDate,
        [Description("List of item descriptions")] List<string> items,
        [Description("List of item amounts")] List<decimal> amounts,
        [Description("Total amount")] decimal totalAmount)
    {
        if (string.IsNullOrWhiteSpace(invoiceNumber) || string.IsNullOrWhiteSpace(customerName) ||
            string.IsNullOrWhiteSpace(invoiceDate) || items == null || amounts == null ||
            items.Count != amounts.Count || items.Count == 0)
            throw new ArgumentException("All invoice details are required and items/amounts must match.");

        var lines = new List<string>
        {
            $"Invoice Number: {invoiceNumber}",
            $"Customer Name: {customerName}",
            $"Invoice Date: {invoiceDate}",
            "Items:"
        };

        for (int i = 0; i < items.Count; i++)
        {
            lines.Add($"  - {items[i]}: {amounts[i]:C}");
        }

        lines.Add($"Total Amount: {totalAmount:C}");
        var content = string.Join(Environment.NewLine, lines);

        // Create a unique file name (same as FileRepository logic)
        var extension = ".txt";
        var newFileName = $"invoice_{invoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}";
        var filePath = Path.Combine(_uploadFolder, newFileName + extension);
        int index = 1;
        while (File.Exists(filePath))
        {
            filePath = Path.Combine(_uploadFolder, $"{newFileName}_{index}{extension}");
            index++;
        }

        File.WriteAllText(filePath, content);

        // Return just the file name (like FileRepository)
        return Path.GetFileName(filePath);
    }
}
