﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Models
{
    public class DailyInsightAgent
    {
        public int Id { get; set; }
        public string UserEmail { get; set; }
        public string AgentName { get; set; }
        public string Prompt { get; set; }
        public string Title { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastRunAt { get; set; }
        public string LastResponse { get; set; }
    }
}
