using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Core.Dtos;
using System.Text;
using Microsoft.SemanticKernel.ChatCompletion;

namespace ProjectApp.Infrastructure.Services
{    public class EmailProcessingService
    {
        private readonly ILogger<EmailProcessingService> _logger;
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;
        private readonly IAgentChatHistoryRepository _agentChatHistoryRepository;
        private readonly IEmailProcessingResultRepository _emailProcessingResultRepository;
        private readonly AIService _aiService;
        private readonly IChatCompletionService _chatCompletionService;
        private readonly EmailReceiverSettings _emailSettings;

        public EmailProcessingService(
            ILogger<EmailProcessingService> logger,
            IAgentDefinitionRepository agentDefinitionRepository,
            IAgentChatHistoryRepository agentChatHistoryRepository,
            IEmailProcessingResultRepository emailProcessingResultRepository,
            AIService aiService,
            IChatCompletionService chatCompletionService,
            IOptions<EmailReceiverSettings> emailSettings)
        {
            _logger = logger;
            _agentDefinitionRepository = agentDefinitionRepository;
            _agentChatHistoryRepository = agentChatHistoryRepository;
            _emailProcessingResultRepository = emailProcessingResultRepository;
            _aiService = aiService;
            _chatCompletionService = chatCompletionService;
            _emailSettings = emailSettings.Value;
        }

        /// <summary>
        /// One function to process email: extract best agent and call agent chat using admin email
        /// </summary>
        public async Task ProcessEmailAsync(ReceivedEmail receivedEmail)
        {
            try
            {
                _logger.LogInformation($"Processing email from {receivedEmail.From} with subject: {receivedEmail.Subject}");

                // Get all available agents
                var allAgents = await _agentDefinitionRepository.GetAllAsync();
                if (!allAgents.Any())
                {
                    _logger.LogWarning("No agents found in the system. Cannot process email.");
                    return;
                }

                // Extract best agent name using AI
                var bestAgentName = await ExtractBestAgentAsync(receivedEmail, allAgents);
                if (string.IsNullOrEmpty(bestAgentName))
                {
                    _logger.LogWarning($"Could not determine appropriate agent for email from {receivedEmail.From}");
                    return;
                }   
                
                // Prepare email content for agent
                // var emailContent = $@"I received an email that needs processing:
                //     From: {receivedEmail.From}
                //     Subject: {receivedEmail.Subject}
                //     Received: {receivedEmail.ReceivedDate}
                //     Content: {receivedEmail.Body ?? receivedEmail.HtmlBody}
                //     {(receivedEmail.Attachments.Any() ? $"Attachments: {string.Join(", ", receivedEmail.Attachments)}" : "")}

                //     Please analyze this email and provide an appropriate response or action.";

                // Prepare email content for agent - send as natural content, not as "email processing"
                var emailContent = receivedEmail.Body ?? receivedEmail.HtmlBody ?? "";
                
                // If there's a subject, include it naturally
                if (!string.IsNullOrEmpty(receivedEmail.Subject))
                {
                    emailContent = $"{receivedEmail.Subject}\n\n{emailContent}";
                }
                
                // If content is still empty, use subject as the message
                if (string.IsNullOrWhiteSpace(emailContent))
                {
                    emailContent = receivedEmail.Subject ?? "Hello";
                }

                // Call agent chat using admin email (not sender email)
                var response = new StringBuilder();
                await foreach (var message in _aiService.CallAgent(bestAgentName, emailContent.Trim()))
                {
                    if (!message.IsError && !string.IsNullOrEmpty(message.Message))
                    {
                        response.Append(message.Message);
                    }
                }                // Save to agent chat history using ADMIN EMAIL (not sender email)
                await _agentChatHistoryRepository.SaveAgentChatMessage(
                    emailContent.Trim(), 
                    bestAgentName, 
                    response.ToString(), 
                    new List<ChatSource>(), 
                    _emailSettings.Email // Use admin email from settings
                );

                // ALSO save to Email Processing Results for Daily Insights panel
                var emailResult = new EmailProcessingResult
                {
                    UserEmail = _emailSettings.Email, // Admin email (receiver)
                    SenderEmail = receivedEmail.From,
                    EmailSubject = receivedEmail.Subject ?? "",
                    EmailContent = emailContent.Trim(),
                    AgentName = bestAgentName,
                    AgentResponse = response.ToString(),
                    ProcessedAt = DateTime.Now,
                    MessageId = receivedEmail.MessageId
                };
                
                await _emailProcessingResultRepository.CreateAsync(emailResult);

                _logger.LogInformation($"Successfully processed email from {receivedEmail.From} using agent {bestAgentName}. Content: '{emailContent.Trim().Substring(0, Math.Min(50, emailContent.Trim().Length))}...'");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing email from {receivedEmail.From}: {ex.Message}");            }
        }

        /// <summary>
        /// Extract best agent name using AI based on email content
        /// </summary>
        private async Task<string> ExtractBestAgentAsync(ReceivedEmail email, IEnumerable<AgentDefinition> availableAgents)
        {
            try
            {
                var agentList = availableAgents.Select(a => new
                {
                    AgentName = a.AgentName,
                    Description = a.UserInstructions ?? a.Instructions ?? "No description available"
                }).ToList();
                
                // Build a context description including the available agents
                var agentsDescription = string.Join("\n", agentList.Select(a => $"- {a.AgentName}: {a.Description}"));
                
                var emailContent = $@"
I need to select the best agent to handle this email:

Subject: {email.Subject}
From: {email.From}
Content: {email.Body ?? email.HtmlBody}

Available Agents:
{agentsDescription}

Please analyze this email and select the best agent to handle it.
";

                // Use the AgentSelector to determine the best agent for this request
                var response = await _aiService.CallAgentManually("AgentSelector", emailContent.Trim());
                var selectedAgentName = response?.Message?.Trim();
                
                var matchingAgent = availableAgents.FirstOrDefault(a => 
                    a.AgentName.Equals(selectedAgentName, StringComparison.OrdinalIgnoreCase));
                
                if (matchingAgent != null)
                {
                    _logger.LogInformation($"Selected agent '{matchingAgent.AgentName}' for email processing");
                    return matchingAgent.AgentName;
                }

                // Fallback to first agent
                var fallbackAgent = availableAgents.First().AgentName;
                _logger.LogWarning($"Agent selection failed, using fallback agent: {fallbackAgent}");
                return fallbackAgent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting agent, using first available agent");
                return availableAgents.First().AgentName;
            }
        }
    }
}
