using Serilog;
using Microsoft.IdentityModel.Tokens;
using ProjectApp.Core;
using ProjectApp.Infrastructure;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Hangfire;
using ProjectApp.Infrastructure.Services;
using Microsoft.AspNetCore.SignalR;
using ProjectApp.WebApi.Hubs;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using ProjectApp.Infrastructure.Repositories;
using ProjectApp.Core.Dtos;
using Microsoft.OpenApi.Models;

try
{
    string DefaultCorsPolicyName = "DefaultCorsPolicyName";
    var builder = WebApplication.CreateBuilder(args);

    //Add services to the container.
    builder.Services.AddSerilog(options =>
    {
        options.ReadFrom.Configuration(builder.Configuration);
    });

    builder.Configuration.AddJsonFile("appsettings.json");

    builder.Services.AddCoreServices();
    builder.Services.AddInfrastructureServices(builder.Configuration);

    var corsOrigins = builder.Configuration.GetValue<string>("CorsOrigins")?.Split(',');
    builder.Services.AddCors(options =>
    {
        options.AddPolicy(DefaultCorsPolicyName, builder =>
        {
            builder.WithOrigins(corsOrigins)
                .AllowCredentials()
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
    });

    builder.Services.AddControllers();
    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo { Title = "Project App API", Version = "v1" });

        // Define the JWT Bearer Auth Scheme
        c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        // Add the security requirement to all operations
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                new string[] {}
            }
        });
    });

    builder.Services.AddHttpContextAccessor();

    builder.Services.Configure<OpenAIOptions>(builder.Configuration.GetSection("OpenAI"));
    builder.Services.AddScoped<VectorSearchExample>();

    builder.Services.AddHangfire(config => config.UseSqlServerStorage(builder.Configuration.GetConnectionString("AdminConnection")));
    builder.Services.AddHangfireServer();
    builder.Services.AddScoped<IEmbeddingConfigRepository, EmbeddingConfigRepository>();

    // Add SignalR services
    builder.Services.AddSignalR();    var app = builder.Build();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", "Project App API v1");
            c.RoutePrefix = "swagger";
        });
    }

    // Configure Hangfire Dashboard and Jobs
    app.UseHangfireDashboard("/hangfire");

    // Configure the recurring job for agent logs
    RecurringJob.AddOrUpdate<AgentLogProcessor>(
        "ProcessAgentLogs",
        processor => processor.ProcessPendingLogs(),
        "* * * * *"  // Runs every minute
    );

    // Configure the recurring job for daily insights sync
    var jobId = "SyncDailyInsights";
    var cronExpression = "0 0 * * *";  // Runs daily at 12:00 AM (midnight)

    // Use the instance method directly with a descriptive name
    RecurringJob.AddOrUpdate<DailyInsightService>(
        jobId,
        service => service.ProcessDailyInsightsJob(),
        cronExpression,
        TimeZoneInfo.Local // Use local server time zone
    );    Console.WriteLine($"Scheduled daily insights sync job to run at: {cronExpression} ({TimeZoneInfo.Local.DisplayName})");    // Configure email processing job - runs every 5 minutes if enabled
    var emailConfig = builder.Configuration.GetSection("EmailReceiver");
    var isEmailEnabled = emailConfig.GetValue<bool>("IsEnabled");
    
    if (isEmailEnabled)
    {
        // Remove any existing email processing jobs
        RecurringJob.RemoveIfExists("ProcessEmails");
        RecurringJob.RemoveIfExists("ProcessNewEmails");
        RecurringJob.RemoveIfExists("EmailProcessing");
        
        // Create new recurring job every 5 minutes
        RecurringJob.AddOrUpdate<EmailProcessingJob>(
            "ProcessEmails",
            service => service.ProcessNewEmailsJob(),
            "*/5 * * * *" // Every 5 minutes
        );
        
        Console.WriteLine("Email processing job scheduled to run every 5 minutes");
    }
    else
    {
        Console.WriteLine("Email processing is disabled in settings");
    }

    // Serve static files from wwwroot
    app.UseStaticFiles();

    app.UseHttpsRedirection();

    // Important: UseAuthentication must come before UseAuthorization
    app.UseAuthentication();
    app.UseAuthorization();

    app.MapControllers();

    app.UseCors(DefaultCorsPolicyName); //Enable CORS!

    app.MapHub<ChatHub>("/chathub");

    app.Run();
}
catch (Exception ex)
{
    if (ex.GetType().Name == "StopTheHostException")
    {
        throw;
    }
    Log.Fatal(ex, "Unhandled exception");
}
finally
{
    Log.Information("Shut down complete");
    Log.CloseAndFlush();
}
public static class StringExtenstion
{
    public static string RemovePostFix(this string str, params string[] postFixes)
    {
        if (str == null)
        {
            return null;
        }

        if (string.IsNullOrEmpty(str))
        {
            return string.Empty;
        }

        if (postFixes.IsNullOrEmpty())
        {
            return str;
        }

        foreach (string text in postFixes)
        {
            if (str.EndsWith(text))
            {
                return str.Left(str.Length - text.Length);
            }
        }

        return str;
    }

    public static string Left(this string str, int len)
    {
        if (str == null)
        {
            throw new ArgumentNullException("str");
        }

        if (str.Length < len)
        {
            throw new ArgumentException("len argument can not be bigger than given string's length!");
        }

        return str.Substring(0, len);
    }
}
