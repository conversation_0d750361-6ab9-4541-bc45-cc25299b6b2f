﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Http;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using System.Data;
using Microsoft.AspNetCore.Hosting;
using DocumentFormat.OpenXml.Office.SpreadSheetML.Y2023.MsForms;
using Microsoft.AspNetCore.SignalR;

namespace ProjectApp.Infrastructure.Repositories
{
    public class FileRepository : IFileRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;
        private readonly string[] _allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".txt", ".pdf" };
        private readonly string _uploadFolder;
        private readonly AIService _aiService;

        public FileRepository(IDbConnection dbConnection, IAgentDefinitionRepository agentDefinitionRepository, IWebHostEnvironment webHostEnvironment, AIService aiService)
        {
            _dbConnection = dbConnection;
            _agentDefinitionRepository =_agentDefinitionRepository;
            _aiService = aiService;

            // Set the upload folder to wwwroot/uploads
            _uploadFolder = Path.Combine(webHostEnvironment.WebRootPath, "uploads");

            // Ensure the directory exists
            if (!Directory.Exists(_uploadFolder))
            {
                Directory.CreateDirectory(_uploadFolder);
            }
        }

        public async Task<ResponseMessage> UploadFiles(List<IFormFile> files, string source = null)
        {
            var responseMessage = new ResponseMessage();

            if (files == null || files.Count == 0)
            {
                return new ResponseMessage { IsError = true, Message = "No files uploaded." };
            }

            if (files.Any(file => !_allowedExtensions.Contains(Path.GetExtension(file.FileName).ToLowerInvariant())))
            {
                return new ResponseMessage { IsError = true, Message = "One or more file types are not allowed. No files were uploaded." };
            }

            var uploadedFiles = new List<string>();

            foreach (var file in files)
            {
                try
                {
                    var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                    var baseFileName = Path.GetFileNameWithoutExtension(file.FileName);
                    var finalFileName = baseFileName + extension;
                    var filePath = Path.Combine(_uploadFolder, finalFileName);
                    int index = 1;

                    // Ensure unique file name
                    while (File.Exists(filePath))
                    {
                        finalFileName = $"{baseFileName}_{index}{extension}";
                        filePath = Path.Combine(_uploadFolder, finalFileName);
                        index++;
                    }

                    // Create the directory if it doesn't exist
                    Directory.CreateDirectory(_uploadFolder);

                    // Save the uploaded file to disk
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }

                    uploadedFiles.Add(finalFileName);

                    // Call the AI service with the actual saved file name
                    var response = await _aiService.CallAgentManually(
                        "FileTextExtractionAgent",
                        $"Extract text from this file: {finalFileName}"
                    );

                    // Insert metadata into the database
                    var query = "INSERT INTO Files (FileName, Description, Source) VALUES (@FileName, @Description, @Source)";
                    await _dbConnection.ExecuteAsync(query, new
                    {
                        FileName = finalFileName,
                        Description = response.Message,
                        Source = source
                    });
                }
                catch (Exception ex)
                {
                    return new ResponseMessage
                    {
                        IsError = true,
                        Message = $"Internal server error: {ex.Message}"
                    };
                }
            }

            return new ResponseMessage { IsError = false, Message = string.Join(", ", uploadedFiles) };
        }

        public async Task<(byte[] FileBytes, string MimeType, bool IsError, string Message)> GetFile(string fileName)
        {
            var filePath = Path.Combine(_uploadFolder, fileName);

            if (!File.Exists(filePath))
                return (null, null, true, "File not found.");

            var fileBytes = await File.ReadAllBytesAsync(filePath);
            var fileExtension = Path.GetExtension(fileName).ToLowerInvariant();
            var mimeType = fileExtension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".txt" => "text/plain",
                ".pdf" => "application/pdf",
                _ => "application/octet-stream"
            };

            return (fileBytes, mimeType, false, null);
        }

        public async Task<string> GetDescription(string fileName)
        {
            var query = "SELECT Description FROM Files WHERE FileName = @FileName";
            var description = await _dbConnection.QuerySingleOrDefaultAsync<string>(query, new { FileName = fileName });
            if (description == null)
                return null;

            return description;
        }

        public async Task<string> GetAIAnalysis(string fileName)
        {
            var query = "SELECT AIAnalysis FROM Files WHERE FileName = @FileName";
            var aiAnalysis = await _dbConnection.QuerySingleOrDefaultAsync<string>(query, new { FileName = fileName });
            if (aiAnalysis == null)
                return null;

            return aiAnalysis;
        }

        public async Task<ResponseMessage> UpdateAIAnalysis(string fileName, string aiAnalysis)
        {
            try
            {
                var query = "UPDATE Files SET AIAnalysis = @AIAnalysis WHERE FileName = @FileName";
                await _dbConnection.ExecuteAsync(query, new { FileName = fileName, AIAnalysis = aiAnalysis });
                return new ResponseMessage { IsError = false, Message = "AI analysis updated successfully" };
            }
            catch (Exception ex)
            {
                return new ResponseMessage { IsError = true, Message = $"Error updating AI analysis: {ex.Message}" };
            }
        }


        public async Task<ResponseMessage> UpdateFileAIAnalysis(string fileName, string aiAnalysis)
        {
            try
            {
                // Update the database
                var updateQuery = "UPDATE Files SET AIAnalysis = @AIAnalysis WHERE FileName = @FileName";
                await _dbConnection.ExecuteAsync(updateQuery, new { FileName = fileName, AIAnalysis = aiAnalysis });

                return new ResponseMessage { IsError = false, Message = "File AI analysis updated successfully" };
            }
            catch (Exception ex)
            {
                return new ResponseMessage { IsError = true, Message = $"Error updating file AI analysis: {ex.Message}" };
            }
        }

        public async Task<ResponseMessage> DeleteFiles(List<string> fileNames)
        {
            var responseMessage = new ResponseMessage();
            var deletedFiles = new List<string>();
            var errors = new List<string>();

            foreach (var fileName in fileNames)
            {
                try
                {
                    var filePath = Path.Combine(_uploadFolder, fileName);
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);

                        var deleteQuery = "DELETE FROM Files WHERE FileName = @FileName";
                        await _dbConnection.ExecuteAsync(deleteQuery, new { FileName = fileName });

                        deletedFiles.Add(fileName);
                    }
                    else
                    {
                        errors.Add($"File '{fileName}' not found.");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"Error deleting file '{fileName}': {ex.Message}");
                }
            }

            if (errors.Any())
            {
                responseMessage.IsError = true;
                responseMessage.Message = string.Join("; ", errors);
            }
            else
            {
                responseMessage.IsError = false;
                responseMessage.Message = $"Files deleted successfully: {string.Join(", ", deletedFiles)}";
            }

            return responseMessage;
        }

        public async Task<List<FileDto>> GetAllFiles(string source = null)
        {
            var query = "SELECT * FROM Files";
            if (!string.IsNullOrEmpty(source))
            {
                query += " WHERE Source = @Source";
            }

            var files = await _dbConnection.QueryAsync<FileDto>(query, new { Source = source });
            return files.ToList();
        }

        public async Task<FileDto> GetFileDto(string fileName)
        {
            var query = "SELECT * FROM Files WHERE FileName = @FileName";
            var files = await _dbConnection.QueryAsync<FileDto>(query, new { FileName = fileName });
            return files.FirstOrDefault() ?? new FileDto();
        }
    }
}
