using System;
using System.Collections.Generic;
using System.Data;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure;
using ProjectApp.Infrastructure.Repositories;
using ProjectApp.Infrastructure.Services;
using ProjectApp.WebApi.Hubs;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AgentChatController : ControllerBase
    {
        private readonly IAgentChatHistoryRepository _repository;
        private readonly IDbConnection _dbConnection;
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly IExtractEmailFromAccessor _extractEmail;
        private readonly AIService _aiService;
        private readonly IChatSourceService _chatSourceService;
        private readonly ILogger<AgentChatController> _logger;

        public AgentChatController(
            IAgentChatHistoryRepository repository, 
            IDbConnection dbConnection, 
            IHubContext<ChatHub> hubContext, 
            IExtractEmailFromAccessor extractEmail, 
            AIService aiService, 
            IChatSourceService chatSourceService,
            ILogger<AgentChatController> logger)
        {
            _repository = repository;
            _dbConnection = dbConnection;
            _hubContext = hubContext;
            _extractEmail = extractEmail;
            _aiService = aiService;
            _chatSourceService = chatSourceService;
            _logger = logger;
        }

        [HttpGet("histories")]
        public async Task<ActionResult<AgentChatConversationDto>> GetAllHistories([FromQuery] string agentName)
        {
            var histories = await _repository.GetHistoryAsync(agentName);
            return Ok(histories);
        }

        [HttpGet("GetHistoriesPaginated")]
        public async Task<ActionResult<PaginatedAgentChatConversationDto>> GetHistoriesPaginated(
            [FromQuery] string agentName, 
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 5)
        {
            if (pageSize > 50) pageSize = 50; // Limit max page size
            if (pageNumber < 1) pageNumber = 1; // Ensure minimum page number
            
            var histories = await _repository.GetHistoryPaginatedAsync(agentName, pageNumber, pageSize);
            return Ok(histories);
        }

        [HttpGet("AgentContainingChat")]
        public async Task<ActionResult<ResponseMessageList>> GetAllAgentsWithChatHistory()
        {
            var userEmail = _extractEmail.GetEmail();
            var response = await _repository.GetAllAgentsWithChatHistoryAsync(userEmail);
            
            if (response.IsError)
            {
                return BadRequest(response);
            }
            
            return Ok(response);
        }

        //[HttpPost("response")]
        //public async Task<ActionResult<AgentChatConversationDto>> GenerateResponse(AgentChatRequestDto createDto)
        //{

        //    var response = await _repository.GenerateResponse(createDto);
        //    return Ok(response);
        //}

        [HttpPost("SendAgentMessage")]
        public async Task<ActionResult<AgentChatHistoryDto>> SendAgentMessage(AgentChatRequestDto request)
        {
            try
            {
                var userEmail = _extractEmail.GetEmail();
                var agentInfo = await GetAgentInformationAsync(request.AgentName);
                
                if (agentInfo == null)
                    return BadRequest($"Agent '{request.AgentName}' not found.");

                var contextRequest = new ChatContextRequest
                {
                    Question = request.Question,
                    UserEmail = userEmail,
                    AgentName = request.AgentName,
                    ChatType = "Agent",
                    FileNames = request.FileNames
                };

                var chatContext = await BuildChatContextAsync(contextRequest);
                var responseText = await GenerateAIResponseAsync(chatContext, request.AgentName, agentInfo);
                var chatSources = ProcessChatSources();

                var response = await _repository.SaveAgentChatMessage(request.Question, request.AgentName, responseText, chatSources);
                return Ok(response);            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }        
        [HttpPost("AgentChatRegenerate")]
        public async Task<ActionResult<AgentChatResponse>> AgentChatRegenerate([FromQuery] Guid id, [FromQuery] string agentName)
        {
            try
            {
                var userEmail = _extractEmail.GetEmail();
                var agentInfo = await GetAgentInformationAsync(agentName);
                
                if (agentInfo == null)
                    return BadRequest($"Agent '{agentName}' not found.");

                var existingChat = await _repository.GetHistoryById(id);
                if (existingChat == null)
                    return NotFound("Chat not found or access denied");

                if (existingChat.Responses.Count == 0)
                    return BadRequest("No previous responses found to regenerate.");

                // Build regeneration context for agent chat
                var contextRequest = new ChatContextRequest
                {
                    Question = existingChat.Question,
                    UserEmail = userEmail,
                    AgentName = agentName,
                    ChatType = "Agent"
                };

                var chatContext = await BuildChatContextAsync(contextRequest);
                
                // Add previous responses for regeneration context
                var previousResponses = string.Join("\n", existingChat.Responses.Select((response, index) => $"{index + 1}. {response.ResponseText}"));
                var regenerationPrompt = $@"{chatContext}

Previous Responses to this question:
{previousResponses}

Please generate a new, improved response to the original question. Consider the context while offering new insights or a different approach.";

                // STREAMING FIX: Use streaming for regenerate
                var responseText = await GenerateAIResponseAsync(regenerationPrompt, agentName, agentInfo);
                var chatSources = ProcessChatSources();

                var createResponseDto = new CreateAgentChatResponseDto
                {
                    HistoryId = id,
                    ResponseText = responseText,
                    ChatSource = JsonSerializer.Serialize(chatSources)
                };

                var chatResponse = await _repository.CreateResponseWithMemoryUpdateAsync(
                    createResponseDto, existingChat.Question, agentName, userEmail);

                return Ok(chatResponse);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("WorkspaceChatRegenerate")]
        public async Task<ActionResult<AgentChatResponse>> WorkspaceChatRegenerate([FromQuery] Guid id, [FromQuery] string workspaceName)
        {
            try
            {
                var userEmail = _extractEmail.GetEmail();
                
                var existingChat = await _repository.GetHistoryById(id);
                if (existingChat == null)
                    return NotFound("Chat not found or access denied");

                if (existingChat.Responses.Count == 0)
                    return BadRequest("No previous responses found to regenerate.");

                // Get the agent that was used for the original response
                var originalAgentName = await _repository.GetOriginalAgentNameForHistory(id);
                if (string.IsNullOrEmpty(originalAgentName))
                {
                    // Fallback: get any agent from the workspace
                    var workspaceAgents = await _repository.GetWorkspaceAgentsAsync(workspaceName);
                    if (!workspaceAgents.Any())
                        return BadRequest($"No agents found in workspace '{workspaceName}'");
                    
                    originalAgentName = workspaceAgents.First();
                }

                var agentInfo = await GetAgentInformationAsync(originalAgentName);
                if (agentInfo == null)
                    return BadRequest($"Agent '{originalAgentName}' not found.");

                // Build regeneration context for workspace chat
                var contextRequest = new ChatContextRequest
                {
                    Question = existingChat.Question,
                    UserEmail = userEmail,
                    AgentName = originalAgentName,
                    ChatType = "Workspace",
                    WorkspaceName = workspaceName
                };

                var chatContext = await BuildChatContextAsync(contextRequest);
                
                // Add previous responses for regeneration context
                var previousResponses = string.Join("\n", existingChat.Responses.Select((response, index) => $"{index + 1}. {response.ResponseText}"));
                var regenerationPrompt = $@"{chatContext}

Previous Responses to this question:
{previousResponses}

Please generate a new, improved response to the original question. Consider the workspace context while offering new insights or a different approach.";

                // STREAMING FIX: Use streaming for regenerate
                var responseText = await GenerateAIResponseAsync(regenerationPrompt, originalAgentName, agentInfo, workspaceName);
                var chatSources = ProcessChatSources();

                var createResponseDto = new CreateAgentChatResponseDto
                {
                    HistoryId = id,
                    ResponseText = responseText,
                    ChatSource = JsonSerializer.Serialize(chatSources)
                };

                var chatResponse = await _repository.CreateWorkspaceResponseWithMemoryUpdateAsync(
                    createResponseDto, existingChat.Question, workspaceName, userEmail);

                return Ok(chatResponse);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        #region Workspace Chat Methods        
        [HttpPost("workspace-chat")]
        public async Task<ActionResult<AgentChatResponse>> CreateWorkspaceChat([FromBody] WorkspaceChatRequestDto request)
        {
            try
            {
                var userEmail = _extractEmail.GetEmail();

                // Get and validate workspace agents
                var workspaceAgents = await _repository.GetWorkspaceAgentsAsync(request.WorkspaceName);
                if (!workspaceAgents.Any())
                    return BadRequest($"No agents found in workspace '{request.WorkspaceName}'");

                string selectedAgentName;

                try
                {
                    // Get full agent definitions for the workspace agents
                    var availableAgents = new List<AgentDefinition>();
                    foreach (var agentName in workspaceAgents)
                    {
                        var agent = await GetAgentInformationAsync(agentName);
                        if (agent != null)
                        {
                            availableAgents.Add(agent);
                        }
                    }

                    if (availableAgents.Any())
                    {
                        // Format the message with agent options for the selector
                        var agentList = availableAgents.Select(a => new
                        {
                            AgentName = a.AgentName,
                            Description = a.UserInstructions ?? a.Instructions ?? "No description available"
                        }).ToList();
                    
                        var agentsDescription = string.Join("\n", agentList.Select(a => $"- {a.AgentName}: {a.Description}"));
                    
                        // Create context for agent selection
                        var selectionContext = $@"
                            I need to select the best agent to handle this workspace request:

                            Workspace: {request.WorkspaceName}
                            Message: {request.Question}

                            Available Agents:
                            {agentsDescription}

                            Please analyze this message and select the best agent to handle it.
                            ";

                        // Use AgentSelector to determine the best agent for this request
                        var agentResponse = await _aiService.CallAgentManually("AgentSelector", selectionContext.Trim());
                        if (agentResponse != null && !string.IsNullOrEmpty(agentResponse.Message))
                        {
                            var chosenAgentName = agentResponse.Message.Trim();
                            
                            // Find the matching agent
                            var matchingAgent = availableAgents.FirstOrDefault(a => 
                                a.AgentName.Equals(chosenAgentName, StringComparison.OrdinalIgnoreCase));
                            
                            if (matchingAgent != null)
                            {
                                selectedAgentName = matchingAgent.AgentName;
                                _logger.LogInformation($"Selected agent '{selectedAgentName}' for workspace chat in '{request.WorkspaceName}'");
                            }
                            else
                            {
                                // Use preferred agent or first available if agent selector returns invalid agent
                                selectedAgentName = request.PreferredAgentName ?? workspaceAgents.First();
                                _logger.LogWarning($"Agent selector returned invalid agent. Using fallback: {selectedAgentName}");
                            }
                        }
                        else
                        {
                            // Use preferred agent or first available if agent selector fails
                            selectedAgentName = request.PreferredAgentName ?? workspaceAgents.First();
                            _logger.LogWarning($"Agent selector failed. Using fallback: {selectedAgentName}");
                        }
                    }
                    else
                    {
                        // No valid agent definitions found, use preferred or first
                        selectedAgentName = request.PreferredAgentName ?? workspaceAgents.First();
                    }
                }
                catch (Exception ex)
                {
                    // If anything goes wrong in agent selection, use the preferred agent or first available
                    _logger.LogError(ex, "Error in agent selection");
                    selectedAgentName = request.PreferredAgentName ?? workspaceAgents.First();
                }

                var agentInfo = await GetAgentInformationAsync(selectedAgentName);
                
                if (agentInfo == null)
                    return BadRequest($"Agent '{selectedAgentName}' not found in workspace '{request.WorkspaceName}'.");

                // Create workspace chat history
                var history = await _repository.CreateWorkspaceChatHistoryAsync(
                    request.Question, request.WorkspaceName, selectedAgentName, userEmail);

                var contextRequest = new ChatContextRequest
                {
                    Question = request.Question,
                    UserEmail = userEmail,
                    AgentName = selectedAgentName,
                    ChatType = "Workspace",
                    WorkspaceName = request.WorkspaceName
                };

                var chatContext = await BuildChatContextAsync(contextRequest);
                var responseText = await GenerateAIResponseAsync(chatContext, selectedAgentName, agentInfo, request.WorkspaceName);
                var chatSources = ProcessChatSources();                var createResponseDto = new CreateAgentChatResponseDto
                {
                    HistoryId = history.Id,
                    ResponseText = responseText,
                    ChatSource = JsonSerializer.Serialize(chatSources)
                };

                var response = await _repository.CreateWorkspaceResponseWithMemoryUpdateAsync(
                    createResponseDto, request.Question, request.WorkspaceName, userEmail);

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }        
        
        [HttpGet("workspace-histories")]
        public async Task<ActionResult<AgentChatConversationDto>> GetWorkspaceHistories([FromQuery] string workspaceName)
        {
            var userEmail = _extractEmail.GetEmail();
            var histories = await _repository.GetWorkspaceHistoryAsync(workspaceName, userEmail);
            return Ok(histories);
        }        
        [HttpGet("workspace-histories-paginated")]
        public async Task<ActionResult<PaginatedAgentChatConversationDto>> GetWorkspaceHistoriesPaginated(
            [FromQuery] string workspaceName, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 5)
        {
            var userEmail = _extractEmail.GetEmail();
            var histories = await _repository.GetWorkspaceHistoryPaginatedAsync(workspaceName, userEmail, pageNumber, pageSize);
            return Ok(histories);
        }

        [HttpGet("all-workspaces")]
        public async Task<ActionResult<ResponseMessageList>> GetAllWorkspacesWithHistory()
        {
            var userEmail = _extractEmail.GetEmail();
            var workspaces = await _repository.GetAllWorkspacesWithChatHistoryAsync(userEmail);
            return Ok(workspaces);
                                                                                                                                                                 }        
        [HttpGet("workspace-agents")]
        public async Task<ActionResult<List<string>>> GetWorkspaceAgents(string workspaceName)
        {
            var agents = await _repository.GetWorkspaceAgentsAsync(workspaceName);
            return Ok(agents);
        }

        #endregion

        #region Helper Classes and Methods

        private class ChatContextRequest
        {
            public string Question { get; set; } = string.Empty;
            public string UserEmail { get; set; } = string.Empty;
            public string AgentName { get; set; } = string.Empty;
            public string ChatType { get; set; } = string.Empty; // "Agent" or "Workspace"
            public string? WorkspaceName { get; set; }
            public List<string>? FileNames { get; set; }
        }

        /// <summary>
        /// Get agent information with caching and validation
        /// </summary>
        private async Task<AgentDefinition> GetAgentInformationAsync(string agentName)
        {
            return await _dbConnection.QueryFirstOrDefaultAsync<AgentDefinition>(
                "SELECT * FROM AgentDefinitions WHERE AgentName = @AgentName",
                new { AgentName = agentName });
        }

        /// <summary>
        /// Build comprehensive chat context for both agent and workspace chats
        /// </summary>
        private async Task<string> BuildChatContextAsync(ChatContextRequest request)
        {
            var contextParts = new List<string> { $"Question: {request.Question}" };

            // Add user memory  
            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag { Name = "Email", Value = request.UserEmail }
            };
            var userMemory = await _aiService.SearchMemory(request.Question, "Memory", memoryTags, 0.2);
            if (userMemory.Any())
            {
                var formattedUserMemory = string.Join(" ", userMemory.Select((memory, index) => $"{index + 1}. {memory}"));
                contextParts.Add($"User Memory: {formattedUserMemory}");
            }

            // Add conversation context based on chat type  
            if (request.ChatType == "Agent" || request.ChatType == "Workspace")
            {
                var formattedContext = await GetFormattedContextAsync(request);
                if (!string.IsNullOrEmpty(formattedContext))
                {
                    contextParts.Add(formattedContext);
                }
            }

            // Add file information if provided  
            if (request.FileNames?.Any() == true)
            {
                contextParts.Add($"Uploaded Files: {string.Join(", ", request.FileNames)}");
            }

            return string.Join("\n", contextParts);
        }

        private async Task<string> GetFormattedContextAsync(ChatContextRequest request)
        {
            var contextParts = new List<string>();

            if (request.ChatType == "Agent")
            {
                var recentContext = await _repository.GetFormattedConversationContext(request.UserEmail, request.AgentName, 2);
                if (recentContext.Any())
                {
                    var formattedRecentContext = string.Join("\n\n", recentContext.Select((context, index) => $"{index + 1}. {context}"));
                    contextParts.Add($"Recent Conversation:\n{formattedRecentContext}");
                }
            }
            else if (request.ChatType == "Workspace")
            {
                var workspaceContext = await _repository.GetFormattedWorkspaceConversationContext(request.UserEmail, request.WorkspaceName, 3);
                if (workspaceContext.Any())
                {
                    var formattedWorkspaceContext = string.Join("\n\n", workspaceContext.Select((context, index) => $"{index + 1}. {context}"));
                    contextParts.Add($"Workspace Conversation History:\n{formattedWorkspaceContext}");
                }
            }

            var chatHistory = await GetIntelligentHistoryAsync(request.Question, request.UserEmail, request.AgentName, request.ChatType);
            if (chatHistory.Any())
            {
                var formattedChatHistory = string.Join("\n", chatHistory.Select((history, index) => $"{index + 1}. {history}"));
                contextParts.Add($"Relevant Chat History:\n{formattedChatHistory}");
            }

            return string.Join("\n", contextParts);
        }

        /// <summary>
        /// Generate AI response with unified logic for both chat types
        /// </summary>
        private async Task<string> GenerateAIResponseAsync(string context, string agentName, AgentDefinition agentInfo, string workspace = null)
        {
            var agentResponse = new StringBuilder();
            var workspaceToUse = workspace ?? agentInfo.Workspace;

            await foreach (var message in _aiService.GetResponse(context, agentName, agentInfo?.ModelName, workspaceToUse, null))
            {
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                agentResponse.Append(message.Message);
            }            // Send completion signal when streaming is done
            await _hubContext.Clients.All.SendAsync("ReceiveComplete", new ResponseMessage
            {
                Message = "",  // Empty message for completion
                IsError = false
            });

            return agentResponse.ToString();
        }

        /// <summary>
        /// Process chat sources with unified logic
        /// </summary>
        private List<ChatSource> ProcessChatSources()
        {
            var chatSources = _chatSourceService.GenerateChatSources();
            _chatSourceService.ClearTrackedInvocations();
            return chatSources;
        }

        #endregion

        /// <summary>
        /// AI-powered intelligent chat history retrieval that understands conversation context
        /// </summary>
        private async Task<List<string>> GetIntelligentHistoryAsync(string currentQuestion, string userEmail, string identifier, string chatType)
        {
            try
            {
                // Get recent conversation context
                var recentConversations = await _repository.GetRecentConversationContext(userEmail, chatType, identifier, 5);

                if (!recentConversations.Any())
                {
                    // No recent context, use simple search
                    return await SearchChatHistoryAsync(currentQuestion, userEmail, identifier, chatType, 0.4);
                }
                var agentName = identifier;
                if (chatType == "Workspace")
                {
                    // For workspace chats, use the first agent in the workspace as context
                    var workspaceAgents = await _repository.GetWorkspaceAgentsAsync(identifier);
                    if (workspaceAgents.Any())
                    {
                        agentName = workspaceAgents.First();
                    }
                }
                // Use AI to analyze conversation context and determine search strategy
                var contextAnalysis = await AnalyzeConversationContextAsync(currentQuestion, recentConversations, agentName);

                // Generate intelligent search query based on context
                var searchQuery = await GenerateContextualSearchQueryAsync(currentQuestion, recentConversations, contextAnalysis, agentName);

                // Determine optimal search parameters based on analysis
                var relevanceThreshold = DetermineRelevanceThreshold(contextAnalysis);

                // Perform intelligent search
                return await SearchChatHistoryAsync(currentQuestion, userEmail, identifier, chatType, 0.4);
            }
            catch (Exception ex)
            {
                // Fallback to simple search on error
                return await SearchChatHistoryAsync(currentQuestion, userEmail, identifier, chatType, 0.4);
            }
        }

        /// <summary>
        /// Use AI to analyze conversation context and understand the type of interaction
        /// </summary>
        private async Task<string> AnalyzeConversationContextAsync(string currentQuestion, List<AgentChatHistoryDto> recentConversations, string agentName)
        {
            var analysisPrompt = $@"
Analyze this conversation context and determine the relationship between the current message and recent conversation:

Recent Conversation:
{string.Join("\n\n", recentConversations.Take(3).Select(c => $"User: {c.Question}\nAssistant: {(c.Responses.Any() ? c.Responses.First().ResponseText?.Substring(0, Math.Min(300, c.Responses.First().ResponseText.Length)) + "..." : "No response")}"))}

Current Message: {currentQuestion}

Respond with ONE of these categories:
- NEW_TOPIC: This is a completely new topic/question unrelated to previous conversation
- FOLLOW_UP: This is following up on the previous response (like answering a question the AI asked or providing requested information)
- CONTINUATION: This is continuing the same topic with more details or refinements
- CLARIFICATION: This is asking for clarification about something mentioned in previous responses

Only respond with the category name, nothing else.";

            var response = new StringBuilder();
            await foreach (var message in _aiService.GetResponse(analysisPrompt, agentName, null, null, null))
            {
                response.Append(message.Message);
            }

            return response.ToString().Trim().ToUpper();
        }

        /// <summary>
        /// Generate intelligent search query based on conversation context
        /// </summary>
        private async Task<string> GenerateContextualSearchQueryAsync(string currentQuestion, List<AgentChatHistoryDto> recentConversations, string contextType, string agentName)
        {
            if (contextType == "NEW_TOPIC")
            {
                return currentQuestion; // For new topics, just use the current question
            }

            // For follow-ups, continuations, and clarifications, build contextual query
            var queryPrompt = $@"
Create a search query that combines the current message with relevant context from recent conversation.

Recent Context:
{string.Join("\n\n", recentConversations.Take(2).Select(c => $"Q: {c.Question}\nA: {(c.Responses.Any() ? c.Responses.First().ResponseText?.Substring(0, Math.Min(200, c.Responses.First().ResponseText.Length)) + "..." : "No response")}"))}

Current Message: {currentQuestion}
Context Type: {contextType}

Generate a search query that will find relevant conversation history. Include both the current message and necessary context from previous questions and answers.
Return only the search query, nothing else.";

            var response = new StringBuilder();
            await foreach (var message in _aiService.GetResponse(queryPrompt, agentName, null, null, null))
            {
                response.Append(message.Message);
            }

            return response.ToString().Trim();
        }

        /// <summary>
        /// Determine optimal relevance threshold based on context analysis
        /// </summary>
        private double DetermineRelevanceThreshold(string contextType)
        {
            return contextType switch
            {
                "NEW_TOPIC" => 0.4,        // Higher threshold for new topics
                "FOLLOW_UP" => 0.2,        // Lower threshold for follow-ups to catch more context
                "CONTINUATION" => 0.3,     // Medium threshold for continuations
                "CLARIFICATION" => 0.25,   // Lower threshold for clarifications
                _ => 0.3                   // Default threshold
            };
        }

        /// <summary>
        /// Perform the actual chat history search
        /// </summary>
        private async Task<List<string>> SearchChatHistoryAsync(string searchQuery, string userEmail, string identifier, string chatType, double relevanceThreshold)
        {
            var chatHistoryTags = new List<MemoryTag>
           {
               new MemoryTag { Name = "UserEmail", Value = userEmail }
           };

            if (chatType == "Agent")
            {
                chatHistoryTags.Add(new MemoryTag { Name = "AgentName", Value = identifier });
            }
            else if (chatType == "Workspace")
            {
                chatHistoryTags.Add(new MemoryTag { Name = "WorkspaceName", Value = identifier });
            }

            return await _aiService.SearchMemory(searchQuery,
                chatType == "Agent" ? "chatHistory" : "workspaceChatHistory",
                chatHistoryTags,
                relevanceThreshold);
        }
    }
}
