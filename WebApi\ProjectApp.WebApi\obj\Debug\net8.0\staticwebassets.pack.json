{"Files": [{"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\.gitkeep", "PackagePath": "staticwebassets\\.gitkeep"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\agent-chat-test.html", "PackagePath": "staticwebassets\\agent-chat-test.html"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "PackagePath": "staticwebassets\\plugin-details.html"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "PackagePath": "staticwebassets\\plugins.html"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "PackagePath": "staticwebassets\\uploads\\Invoice-F66D817D-0001.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "PackagePath": "staticwebassets\\uploads\\Invoice-F66D817D-0001_1.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "PackagePath": "staticwebassets\\uploads\\Invoice-F66D817D-0001_2.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "PackagePath": "staticwebassets\\uploads\\Invoice-F66D817D-0001_4.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "PackagePath": "staticwebassets\\uploads\\Invoice-F66D817D-0001_5.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "PackagePath": "staticwebassets\\uploads\\Invoice-F66D817D-0001_6.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_1.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_2.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_3.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_4.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_5.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_6.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt-2252-1522_7.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_amardeep.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt_amardeep.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt_shyam.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt_shyam_1.pdf", "PackagePath": "staticwebassets\\uploads\\Receipt_shyam_1.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\apexmakerclub5.png", "PackagePath": "staticwebassets\\uploads\\apexmakerclub5.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "PackagePath": "staticwebassets\\uploads\\image.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "PackagePath": "staticwebassets\\uploads\\image_1.jpeg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "PackagePath": "staticwebassets\\uploads\\image_1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "PackagePath": "staticwebassets\\uploads\\image_2.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "PackagePath": "staticwebassets\\uploads\\image_3.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "PackagePath": "staticwebassets\\uploads\\image_4.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "PackagePath": "staticwebassets\\uploads\\landingPage.png"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "PackagePath": "staticwebassets\\uploads\\maxresdefault.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "PackagePath": "staticwebassets\\uploads\\maxresdefault_1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.csv", "PackagePath": "staticwebassets\\uploads\\name.csv"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\name.xlsx", "PackagePath": "staticwebassets\\uploads\\name.xlsx"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1.pdf", "PackagePath": "staticwebassets\\uploads\\receipt_1.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_1.pdf", "PackagePath": "staticwebassets\\uploads\\receipt_1_1.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_2.pdf", "PackagePath": "staticwebassets\\uploads\\receipt_1_2.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_3.pdf", "PackagePath": "staticwebassets\\uploads\\receipt_1_3.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\receipt_1_4.pdf", "PackagePath": "staticwebassets\\uploads\\receipt_1_4.pdf"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "PackagePath": "staticwebassets\\uploads\\robot leg.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "PackagePath": "staticwebassets\\uploads\\robot leg_1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.docx", "PackagePath": "staticwebassets\\uploads\\test.docx"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\test.txt", "PackagePath": "staticwebassets\\uploads\\test.txt"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\text.jpg", "PackagePath": "staticwebassets\\uploads\\text.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "PackagePath": "staticwebassets\\uploads\\vibe.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "PackagePath": "staticwebassets\\uploads\\vibe_1.jpg"}, {"Id": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "PackagePath": "staticwebassets\\uploads\\vibe_2.jpg"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.ProjectApp.WebApi.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.ProjectApp.WebApi.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.ProjectApp.WebApi.props", "PackagePath": "build\\ProjectApp.WebApi.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.ProjectApp.WebApi.props", "PackagePath": "buildMultiTargeting\\ProjectApp.WebApi.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.ProjectApp.WebApi.props", "PackagePath": "buildTransitive\\ProjectApp.WebApi.props"}], "ElementsToRemove": []}