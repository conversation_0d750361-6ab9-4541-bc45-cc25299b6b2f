using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Dapper;
using System.Data;
using AutoMapper;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Repositories
{    public class AgentChatHistoryRepository : IAgentChatHistoryRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly IMapper _mapper;
        private readonly IExtractEmailFromAccessor _extractEmail;
        private readonly AIService _aiService;
        private readonly IWorkspaceRepository _workspaceRepository;

        public AgentChatHistoryRepository(IDbConnection dbConnection, IMapper mapper, IExtractEmailFromAccessor extractEmail, AIService aiService, IWorkspaceRepository workspaceRepository)
        {
            _dbConnection = dbConnection;
            _mapper = mapper;
            _extractEmail = extractEmail;
            _aiService = aiService;
            _workspaceRepository = workspaceRepository;
        }

        #region Private Helper Methods

        private void EnsureConnectionOpen()
        {
            if (_dbConnection.State != ConnectionState.Open)
                _dbConnection.Open();
        }

        private async Task<AgentChatConversationDto> GetChatHistoryAsync(string chatType, string identifier, string userEmail = null)
        {
            EnsureConnectionOpen();

            userEmail ??= _extractEmail.GetEmail();

            var whereClause = chatType == "Agent" 
                ? "h.AgentName = @Identifier" 
                : "h.WorkspaceName = @Identifier";            var sql = $@"
                SELECT 
                    h.Id as HistoryId, h.Question, h.AgentName, h.WorkspaceName, h.Timestamp as HistoryTimestamp,
                    r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
                FROM AgentChatHistories h
                LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
                WHERE h.UserEmail = @UserEmail
                    AND {whereClause}
                    AND h.ChatType = @ChatType
                ORDER BY h.Timestamp ASC";

            var parameters = new { UserEmail = userEmail, Identifier = identifier, ChatType = chatType };
            var results = await _dbConnection.QueryAsync(sql, parameters);

            return MapToConversationDto(results, chatType, identifier);
        }

        private async Task<PaginatedAgentChatConversationDto> GetChatHistoryPaginatedAsync(
            string chatType, string identifier, string userEmail, int pageNumber, int pageSize)
        {
            EnsureConnectionOpen();

            var whereClause = chatType == "Agent" 
                ? "h.AgentName = @Identifier" 
                : "h.WorkspaceName = @Identifier";

            // Get total count
            var countSql = $@"
                SELECT COUNT(DISTINCT h.Id)
                FROM AgentChatHistories h
                WHERE h.UserEmail = @UserEmail
                    AND {whereClause}
                    AND h.ChatType = @ChatType";

            var countParameters = new { UserEmail = userEmail, Identifier = identifier, ChatType = chatType };
            var totalCount = await _dbConnection.QuerySingleAsync<int>(countSql, countParameters);

            // Get paginated results
            var offset = (pageNumber - 1) * pageSize;            var sql = $@"
                SELECT 
                    h.Id as HistoryId, h.Question, h.AgentName, h.WorkspaceName, h.Timestamp as HistoryTimestamp,
                    r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
                FROM AgentChatHistories h
                LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
                WHERE h.UserEmail = @UserEmail
                    AND {whereClause}
                    AND h.ChatType = @ChatType
                ORDER BY h.Timestamp ASC
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";

            var parameters = new { 
                UserEmail = userEmail, 
                Identifier = identifier,
                ChatType = chatType,
                Offset = offset,
                PageSize = pageSize
            };

            var results = await _dbConnection.QueryAsync(sql, parameters);
            var conversation = MapToConversationDto(results, chatType, identifier);

            return new PaginatedAgentChatConversationDto
            {
                AgentName = conversation.AgentName,
                Histories = conversation.Histories,
                HasMore = (offset + pageSize) < totalCount,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        private AgentChatConversationDto MapToConversationDto(dynamic results, string chatType, string identifier)
        {
            var conversation = new AgentChatConversationDto
            {
                AgentName = chatType == "Agent" ? identifier : $"Workspace: {identifier}",
                Histories = new List<AgentChatHistoryDto>()
            };

            var historiesDict = new Dictionary<Guid, AgentChatHistoryDto>();

            foreach (var row in results)
            {
                // Update agent name if needed for agent chat
                if (chatType == "Agent" && conversation.AgentName == null && row.AgentName != null)
                {
                    conversation.AgentName = row.AgentName.ToString();
                }

                var historyId = (Guid)row.HistoryId;

                if (!historiesDict.TryGetValue(historyId, out var historyDto))
                {
                    historyDto = new AgentChatHistoryDto
                    {
                        Id = historyId,
                        Question = row.Question,
                        Timestamp = row.HistoryTimestamp,
                        Responses = new List<AgentChatResponseDto>()
                    };

                    historiesDict.Add(historyId, historyDto);
                    conversation.Histories.Add(historyDto);
                }

                if (row.ResponseId != null)
                {
                    var response = new AgentChatResponseDto
                    {
                        Id = (Guid)row.ResponseId,
                        ResponseText = row.ResponseText,
                        ChatSource = row.ChatSource,
                        Timestamp = row.ResponseTimestamp
                    };

                    historyDto.Responses.Add(response);
                }
            }

            return conversation;
        }

        private async Task<List<string>> GetFormattedConversationContextAsync(
            string chatType, string identifier, string userEmail, int maxMessages = 3)
        {
            EnsureConnectionOpen();

            var whereClause = chatType == "Agent" 
                ? "h.AgentName = @Identifier" 
                : "h.WorkspaceName = @Identifier";

            var sql = $@"
                SELECT TOP (@MaxMessages)
                    h.Question, 
                    r.ResponseText
                FROM AgentChatHistories h
                LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
                WHERE h.UserEmail = @UserEmail 
                    AND {whereClause}
                    AND h.ChatType = @ChatType
                    AND r.ResponseText IS NOT NULL
                ORDER BY h.Timestamp DESC";

            var results = await _dbConnection.QueryAsync(sql, new { 
                UserEmail = userEmail, 
                Identifier = identifier, 
                ChatType = chatType,
                MaxMessages = maxMessages 
            });

            var context = new List<string>();
            foreach (var row in results.Reverse()) // Reverse to get chronological order
            {
                context.Add($"User: {row.Question}");
                if (!string.IsNullOrEmpty(row.ResponseText))
                {
                    context.Add($"Assistant: {row.ResponseText}");
                }
            }

            return context;
        }

        private async Task<AgentChatResponse> CreateResponseWithMemoryAsync(
            CreateAgentChatResponseDto createDto, string question, string chatType, 
            string agentName, string workspaceName, string userEmail)
        {
            EnsureConnectionOpen();            var response = new AgentChatResponse
            {
                Id = Guid.NewGuid(),
                HistoryId = createDto.HistoryId,
                ResponseText = createDto.ResponseText,
                ChatSource = createDto.ChatSource,
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatResponses (Id, HistoryId, ResponseText, ChatSource, Timestamp) 
                       VALUES (@Id, @HistoryId, @ResponseText, @ChatSource, @Timestamp)";

            await _dbConnection.ExecuteAsync(sql, response);

            // Update memory based on chat type
            if (chatType == "Agent")
            {
                var memoryTags = new List<MemoryTag>
                {
                    new MemoryTag { Name = "AgentName", Value = agentName },
                    new MemoryTag { Name = "UserEmail", Value = userEmail }
                };

                var conversationContext = $"User Question: {question}\n\nAgent Response: {createDto.ResponseText}";
                await _aiService.AddMemory(conversationContext, response.Id.ToString(), "chatHistory", memoryTags);
            }           
            else // Workspace
            {
                var memoryTags = new List<MemoryTag>
                {
                    new MemoryTag { Name = "WorkspaceName", Value = workspaceName },
                    new MemoryTag { Name = "UserEmail", Value = userEmail }
                };

                var conversationContext = $"User Question: {question}\n\nAgent Response: {createDto.ResponseText}";
                await _aiService.AddMemory(conversationContext, response.Id.ToString(), "WorkspaceChatHistory", memoryTags);
            }

            return response;
        }

        #endregion

        #region Agent Chat Methods

        public async Task<AgentChatHistory> CreateHistoryAsync(AgentChatRequestDto createDto, string userEmail)
        {
            EnsureConnectionOpen();

            var history = new AgentChatHistory
            {
                Id = Guid.NewGuid(),
                UserEmail = userEmail,
                Question = createDto.Question,
                AgentName = createDto.AgentName,
                WorkspaceName = null, // Agent chat doesn't have workspace
                ChatType = "Agent",
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatHistories (Id, UserEmail, Question, AgentName, WorkspaceName, ChatType, Timestamp) 
                       VALUES (@Id, @UserEmail, @Question, @AgentName, @WorkspaceName, @ChatType, @Timestamp)";

            await _dbConnection.ExecuteAsync(sql, history);
            return history;
        }

        public async Task<AgentChatConversationDto> GetHistoryAsync(string agentName)
        {
            return await GetChatHistoryAsync("Agent", agentName);
        }

        public async Task<PaginatedAgentChatConversationDto> GetHistoryPaginatedAsync(string agentName, int pageNumber = 1, int pageSize = 5)
        {
            var userEmail = _extractEmail.GetEmail();
            return await GetChatHistoryPaginatedAsync("Agent", agentName, userEmail, pageNumber, pageSize);
        }

        public async Task<List<string>> GetFormattedConversationContext(string userEmail, string agentName, int maxMessages = 3)
        {
            return await GetFormattedConversationContextAsync("Agent", agentName, userEmail, maxMessages);
        }

        public async Task<AgentChatResponse> CreateResponseWithMemoryUpdateAsync(CreateAgentChatResponseDto createDto, string question, string agentName, string userEmail)
        {
            return await CreateResponseWithMemoryAsync(createDto, question, "Agent", agentName, null, userEmail);
        }

        #endregion

        #region Workspace Chat Methods

        public async Task<List<string>> GetWorkspaceAgentsAsync(string workspaceName)
        {
            EnsureConnectionOpen();

            var sql = @"SELECT DISTINCT AgentName FROM AgentDefinitions WHERE Workspace = @WorkspaceName";
            var agents = await _dbConnection.QueryAsync<string>(sql, new { WorkspaceName = workspaceName });
            return agents.ToList();
        }

        public async Task<AgentChatHistory> CreateWorkspaceChatHistoryAsync(string question, string workspaceName, string selectedAgentName, string userEmail)
        {
            EnsureConnectionOpen();

            var history = new AgentChatHistory
            {
                Id = Guid.NewGuid(),
                UserEmail = userEmail,
                Question = question,
                AgentName = selectedAgentName,
                WorkspaceName = workspaceName,
                ChatType = "Workspace",
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatHistories (Id, UserEmail, Question, AgentName, WorkspaceName, ChatType, Timestamp) 
                       VALUES (@Id, @UserEmail, @Question, @AgentName, @WorkspaceName, @ChatType, @Timestamp)";

            await _dbConnection.ExecuteAsync(sql, history);
            return history;
        }

        public async Task<AgentChatConversationDto> GetWorkspaceHistoryAsync(string workspaceName, string userEmail)
        {
            return await GetChatHistoryAsync("Workspace", workspaceName, userEmail);
        }

        public async Task<PaginatedAgentChatConversationDto> GetWorkspaceHistoryPaginatedAsync(string workspaceName, string userEmail, int pageNumber = 1, int pageSize = 5)
        {
            return await GetChatHistoryPaginatedAsync("Workspace", workspaceName, userEmail, pageNumber, pageSize);
        }

        public async Task<List<string>> GetFormattedWorkspaceConversationContext(string userEmail, string workspaceName, int maxMessages = 3)
        {
            return await GetFormattedConversationContextAsync("Workspace", workspaceName, userEmail, maxMessages);
        }

        public async Task<AgentChatResponse> CreateWorkspaceResponseWithMemoryUpdateAsync(CreateAgentChatResponseDto createDto, string question, string workspaceName, string userEmail)
        {
            return await CreateResponseWithMemoryAsync(createDto, question, "Workspace", null, workspaceName, userEmail);
        }

        #endregion

        #region Shared Methods

        public async Task<string> GetOriginalAgentNameForHistory(Guid historyId)
        {
            EnsureConnectionOpen();

            var sql = @"
                SELECT AgentName 
                FROM AgentChatHistories 
                WHERE Id = @HistoryId";

            var agentName = await _dbConnection.QuerySingleOrDefaultAsync<string>(sql, new { HistoryId = historyId });
            return agentName;
        }

        public async Task<AgentChatHistoryDto> GetHistoryById(Guid id)
        {
            EnsureConnectionOpen();

            var userEmail = _extractEmail.GetEmail();

            var sql = @"
               SELECT 
                   h.Id as HistoryId, h.Question, h.AgentName, h.Timestamp as HistoryTimestamp,
                   r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp
               FROM AgentChatHistories h
               LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId
               WHERE h.Id = @Id AND h.UserEmail = @UserEmail
               ORDER BY r.Timestamp ASC";

            var parameters = new { Id = id, UserEmail = userEmail };

            var historyDto = new AgentChatHistoryDto
            {
                Id = id,
                Responses = new List<AgentChatResponseDto>()
            };

            try
            {
                var results = await _dbConnection.QueryAsync(sql, parameters);

                foreach (var row in results)
                {
                    // Populate history details  
                    if (historyDto.Question == null)
                    {
                        historyDto.Question = row.Question;
                        historyDto.Timestamp = row.HistoryTimestamp;
                    }

                    // Add response if it exists  
                    if (row.ResponseId != null)
                    {
                        var response = new AgentChatResponseDto
                        {
                            Id = (Guid)row.ResponseId,
                            ResponseText = row.ResponseText,
                            ChatSource = row.ChatSource,
                            Timestamp = row.ResponseTimestamp
                        };

                        historyDto.Responses.Add(response);
                    }
                }

                return historyDto;
            }
            catch (Exception ex)
            {
                // Log exception if you have a logger  
                throw;
            }
        }

        public async Task<AgentChatResponse> CreateResponseAsync(CreateAgentChatResponseDto createDto)
        {
            EnsureConnectionOpen();

            var response = new AgentChatResponse
            {
                Id = Guid.NewGuid(),
                HistoryId = createDto.HistoryId,
                ResponseText = createDto.ResponseText,
                ChatSource = createDto.ChatSource,
                Timestamp = DateTime.Now
            };

            var sql = @"INSERT INTO AgentChatResponses (Id, HistoryId, ResponseText, ChatSource, Timestamp)
                    VALUES (@Id, @HistoryId, @ResponseText, @ChatSource, @Timestamp)";            await _dbConnection.ExecuteAsync(sql, response);
            return response;
        }        
        public async Task<AgentChatHistory> SaveAgentChatMessage(string question, string agentName, string responseText, List<ChatSource> chatSource)
        {
            EnsureConnectionOpen();

            var userEmail = _extractEmail.GetEmail();

            return await SaveAgentChatMessage(question, agentName, responseText, chatSource, userEmail);
        }

        public async Task<AgentChatHistory> SaveAgentChatMessage(string question, string agentName, string responseText, List<ChatSource> chatSource, string userEmail)
        {
            EnsureConnectionOpen();

            // Create history object and save it
            var history = await CreateHistoryAsync(
                new AgentChatRequestDto { Question = question, AgentName = agentName },
                userEmail
            );

            // Create response object and save it
            var response = await CreateResponseAsync(
                new CreateAgentChatResponseDto
                {
                    HistoryId = history.Id,
                    ResponseText = responseText,
                    ChatSource = JsonSerializer.Serialize(chatSource)
                }
            );

            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag { Name = "AgentName", Value = agentName },
                new MemoryTag { Name = "UserEmail", Value = userEmail }
            };

            // Store complete conversation context in memory (both question and response)
            var conversationContext = $"User Question: {question}\n\nAgent Response: {responseText}";
            await _aiService.AddMemory(conversationContext, history.Id.ToString(), "chatHistory", memoryTags);

            // Return the history object
            return history;
        }

        public async Task<List<AgentChatHistoryDto>> GetRecentConversationContext(string userEmail, string chatType, string identifier, int maxMessages = 5)
        {
            EnsureConnectionOpen();

            var whereClause = chatType == "Agent"
                ? "h.AgentName = @Identifier"
                : "h.WorkspaceName = @Identifier";            var sql = $@"  
               SELECT TOP (@MaxMessages)  
                   h.Id as HistoryId, h.Question, h.AgentName, h.Timestamp as HistoryTimestamp,  
                   r.Id as ResponseId, r.ResponseText, r.ChatSource, r.Timestamp as ResponseTimestamp  
               FROM AgentChatHistories h  
               LEFT JOIN AgentChatResponses r ON h.Id = r.HistoryId  
               WHERE h.UserEmail = @UserEmail AND {whereClause} AND h.ChatType = @ChatType  
               ORDER BY h.Timestamp DESC";

            var parameters = new { UserEmail = userEmail, Identifier = identifier, ChatType = chatType, MaxMessages = maxMessages };
            var results = await _dbConnection.QueryAsync(sql, parameters);

            var historiesDict = new Dictionary<Guid, AgentChatHistoryDto>();

            foreach (var row in results)
            {
                var historyId = (Guid)row.HistoryId;

                if (!historiesDict.TryGetValue(historyId, out var historyDto))
                {
                    historyDto = new AgentChatHistoryDto
                    {
                        Id = historyId,
                        Question = row.Question,
                        Timestamp = row.HistoryTimestamp,
                        Responses = new List<AgentChatResponseDto>()
                    };
                    historiesDict.Add(historyId, historyDto);
                }

                if (row.ResponseId != null)
                {
                    var response = new AgentChatResponseDto
                    {
                        Id = (Guid)row.ResponseId,
                        ResponseText = row.ResponseText,
                        ChatSource = row.ChatSource,
                        Timestamp = row.ResponseTimestamp
                    };
                    historyDto.Responses.Add(response);
                }
            }

            return historiesDict.Values.OrderByDescending(h => h.Timestamp).ToList();
        }

        public async Task<ResponseMessageList> GetAllAgentsWithChatHistoryAsync(string userEmail)
        {
            try
            {
                EnsureConnectionOpen();                var sql = @"
                SELECT h.AgentName 
                FROM AgentChatHistories h
                WHERE h.UserEmail = @UserEmail 
                AND h.AgentName IS NOT NULL 
                AND h.AgentName != ''
                AND h.ChatType = 'Agent'
                GROUP BY h.AgentName
                ORDER BY MAX(h.Timestamp) DESC";

                var agentNames = await _dbConnection.QueryAsync<string>(sql, new { UserEmail = userEmail });

                return new ResponseMessageList
                {
                    IsError = false,
                    Message = agentNames.ToList()
                };
            }
            catch (Exception ex)
            {
                return new ResponseMessageList
                {                    IsError = true,
                    Message = new List<string> { $"Error retrieving agents: {ex.Message}" }
                };
            }
        }        public async Task<ResponseMessageList> GetAllWorkspacesWithChatHistoryAsync(string userEmail)
        {
            try
            {
                EnsureConnectionOpen();

                // Get all workspaces
                var allWorkspaces = await _workspaceRepository.GetWorkspacesForUser();
                
                // Get workspaces with chat history ordered by latest chat
                var chatHistorySql = @"
                    SELECT h.WorkspaceName 
                    FROM AgentChatHistories h
                    WHERE h.UserEmail = @UserEmail 
                      AND h.ChatType = 'Workspace'
                      AND h.WorkspaceName IS NOT NULL
                    GROUP BY h.WorkspaceName
                    ORDER BY MAX(h.Timestamp) DESC";

                var workspacesWithChat = await _dbConnection.QueryAsync<string>(chatHistorySql, new { UserEmail = userEmail });
                var workspacesWithChatList = workspacesWithChat.ToList();

                // Combine: workspaces with chat history first, then all other workspaces
                var allWorkspaceNames = allWorkspaces.Select(w => w.Title).ToList();
                var workspacesWithoutChat = allWorkspaceNames.Except(workspacesWithChatList).OrderBy(w => w).ToList();
                
                var finalList = workspacesWithChatList.Concat(workspacesWithoutChat).ToList();

                return new ResponseMessageList
                {
                    IsError = false,
                    Message = finalList
                };
            }
            catch (Exception ex)
            {
                return new ResponseMessageList
                {
                    IsError = true,
                    Message = new List<string> { $"Error retrieving workspaces: {ex.Message}" }
                };
            }
        }

        #endregion
    }
}
