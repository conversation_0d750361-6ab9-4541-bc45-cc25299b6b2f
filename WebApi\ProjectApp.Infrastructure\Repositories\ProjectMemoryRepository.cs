﻿using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ProjectMemoryRepository(IDbConnection _dbConnection, AIService _aiService): IProjectMemoryRepository
    {
        public async Task<ProjectMemory> SaveProjectMemoryAsync(ProjectMemory projectMemory)
        {
            if (projectMemory.Id == 0)
            {
                projectMemory.Status = "Created";
                var sqlQuery = "INSERT INTO ProjectMemories (Workspace, ProjectDescription, Status, ProjectCategory) " +
                                "VALUES (@Workspace, @ProjectDescription, @Status, @ProjectCategory); " +
                                "SELECT CAST(SCOPE_IDENTITY() as int)";
                var id = await _dbConnection.QuerySingleAsync<int>(sqlQuery, projectMemory);
                projectMemory.Id = id;
            }
            else
            {
                projectMemory.Status = "Updated";
                var sqlQuery = "UPDATE ProjectMemories SET Workspace = @Workspace, ProjectDescription = @ProjectDescription, " +
                                "Status = @Status, ProjectCategory = @ProjectCategory WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(sqlQuery, projectMemory);
            }
            await _aiService.UpdateProjectMemory(projectMemory);
            return projectMemory;
        }

        public async Task<ProjectMemory> GetProjectMemoryByIdAsync(int id)
        {
            
            var sqlQuery = "SELECT * FROM ProjectMemories WHERE Id = @Id";
            var projectMemory = await _dbConnection.QuerySingleOrDefaultAsync<ProjectMemory>(sqlQuery, new { Id = id });
            return projectMemory;
        }

        public async Task<List<ProjectMemory>> GetAllProjectMemoriesAsync(string workspace)
        {
            var sqlQuery = "SELECT * FROM ProjectMemories WHERE Workspace = @Workspace";
            var projectMemories = await _dbConnection.QueryAsync<ProjectMemory>(sqlQuery, new { Workspace = workspace });
            return projectMemories.ToList();
        }

        public async Task DeleteProjectMemoryAsync(int id)
        {
            var existingProjectMemory = await GetProjectMemoryByIdAsync(id);
            if (existingProjectMemory == null)
            {
                throw new Exception("Project memory not found");
            }
            existingProjectMemory.Status = "Deleted";
            var sqlQuery = "DELETE FROM ProjectMemories WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(sqlQuery, new { Id = id });
            await _aiService.UpdateProjectMemory(existingProjectMemory);
        }
    }
}
