﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ProjectApp.Core.Dtos
{
    public class CreateDocsDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string WorkspaceName { get; set; }
        public string FilesToAdd { get; set; }
        public List<string> FilesToDelete { get; set; }
    }

    public class ViewDocsDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string WorkspaceName { get; set; }
        public List<Files> Files { get; set; }
        public bool IsFavorite { get; set; }
        public DateTime? LastOpenedAt { get; set; }
    }

    public class Files
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string Description { get; set; }
    }

    public class UpdateFavoriteDto
    {
        public int Id { get; set; }
        public bool IsFavorite { get; set; }
    }

    public class TrackDocumentOpenDto
    {
        public int Id { get; set; }
    }
}
