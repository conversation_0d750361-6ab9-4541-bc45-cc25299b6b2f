using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class SqlConnectionRepository : ISqlConnectionRepository
    {
        private readonly IConfiguration _configuration;

        public SqlConnectionRepository(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task<SqlConnectionInfoList> GetAllConnectionsAsync()
        {
            var result = new SqlConnectionInfoList();
            var connectionStrings = _configuration.GetSection("ConnectionStrings").GetChildren();

            foreach (var connection in connectionStrings)
            {
                result.Connections.Add(new SqlConnectionInfo
                {
                    Name = connection.Key,
                    ConnectionString = connection.Value
                });
            }

            return await Task.FromResult(result);
        }

        public async Task<ResponseMessage> TestConnectionAsync(string connectionString)
        {
            var response = new ResponseMessage();

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    response.IsError = false;
                    response.Message = "Connection successful!";
                }
            }
            catch (Exception ex)
            {
                response.IsError = true;
                response.Message = $"Connection failed: {ex.Message}";
            }

            return response;
        }

        public async Task<SqlQueryResponse> ExecuteSqlQueryAsync(SqlQueryRequest queryRequest)
        {
            var response = new SqlQueryResponse();

            try
            {
                using (var connection = new SqlConnection(queryRequest.ConnectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SqlCommand(queryRequest.SqlQuery, connection))
                    {
                        // Check if the query is a SELECT query
                        var isSelectQuery = queryRequest.SqlQuery.TrimStart().ToLower().StartsWith("select");

                        if (isSelectQuery)
                        {
                            // Execute reader for SELECT queries to get data
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                // Convert reader to DataTable
                                var dataTable = new DataTable();
                                dataTable.Load(reader);

                                response.Data = dataTable;
                                response.RowsAffected = dataTable.Rows.Count;
                                response.IsSuccess = true;
                                response.Message = $"Query executed successfully. {dataTable.Rows.Count} rows returned.";
                            }
                        }
                        else
                        {
                            // Execute non-query for INSERT, UPDATE, DELETE, etc.
                            var rowsAffected = await command.ExecuteNonQueryAsync();
                            response.RowsAffected = rowsAffected;
                            response.IsSuccess = true;
                            response.Message = $"Query executed successfully. {rowsAffected} rows affected.";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                response.IsSuccess = false;
                response.Message = $"Query execution failed: {ex.Message}";
                response.Data = null;
            }

            return response;
        }
    }
}
