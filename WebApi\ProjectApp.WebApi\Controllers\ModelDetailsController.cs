﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Authorization;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ModelDetailsController : ControllerBase
    {
        private readonly IModelDetailsRepository _modelDetailsRepository;
        private readonly EmbeddingConfigurationProvider _configProvider;

        public ModelDetailsController(IModelDetailsRepository modelDetailsRepository, EmbeddingConfigurationProvider configProvider)
        {
            _modelDetailsRepository = modelDetailsRepository;
            _configProvider = configProvider;
        }

        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<ModelDetailsDto>>> GetAll()
        {
            var models = await _modelDetailsRepository.GetAll();
            return Ok(models);
        }
        
        [HttpGet("GetAllActiveModel")]
        [AllowAnonymous]
        public async Task<ActionResult<List<ModelDetailsNameDto>>> GetAllActiveModel()
        {
            var models = await _modelDetailsRepository.GetAllActiveModel();
            return Ok(models);
        }

        [HttpGet("GetByModelName/{modelName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ModelDetailsDto>> GetByProvider(string modelName)
        {
            var models = await _modelDetailsRepository.GetByModelNameAsync(modelName);
            return Ok(models);
        }
        [HttpPut("UpdateIsActive")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> UpdateIsActive(string modelName, bool isActive)
        {
            var response = await _modelDetailsRepository.UpdateIsActive(modelName, isActive);
            if (response.IsError)
            {
                return BadRequest(response.Message);
            }
            return Ok(response);
        }


        [HttpGet]
        [Route("IsEmbeddingActive")]
        [Authorize(Roles = "Admin")]
        public async Task<bool> IsEmbeddingActive()
        {
            var embeddingModels = await _modelDetailsRepository.GetAllEmbeddingModels();
            return embeddingModels.Any();
        }

        [HttpGet]
        [Route("GetAllEmbedding")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<EmbeddingModelDto>>> GetEmbedding()
        {
            var embeddingModels = await _modelDetailsRepository.GetAllEmbeddingModels();
            return Ok(embeddingModels.ToList());
        }

        [HttpPost]
        [Route("SetEmbeddingToTrue")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> SetEmbeddingToTrue(string modelName)
        {
            var response = await _modelDetailsRepository.SetEmbeddingToTrue(modelName);
            if (response.IsError)
            {
                return BadRequest(response);
            }
            return Ok(response);
        }

        [HttpGet("current")]
        [Authorize(Roles = "Admin")]
        public ActionResult<EmbeddingConfigurationProvider> GetCurrentlyLoadedConfiguration()
        {
            var activeConfig = new EmbeddingConfigurationProvider
            {
                EmbeddingModelId = _configProvider.EmbeddingModelId,
                ApiKey = _configProvider.ApiKey
            };
            if (string.IsNullOrEmpty(activeConfig.EmbeddingModelId) || string.IsNullOrEmpty(activeConfig.ApiKey))
            {
                return NotFound(new { message = "No active embedding configuration found" });
            }
            return Ok(activeConfig); // Return the currently loaded configuration
        }

        [HttpGet("GetByApiCredentialsId/{apiCredentialsId}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<ModelDetailsDto>>> GetByApiCredentialsIdAsync(Guid apiCredentialsId)
        {
            var models = await _modelDetailsRepository.GetByApiCredentialsIdAsync(apiCredentialsId);
            return Ok(models);
        }

    }
}
