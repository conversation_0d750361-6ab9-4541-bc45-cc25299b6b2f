﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Infrastructure;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using ProjectApp.WebApi.Hubs;
using System.Text.Json;
using System.Text;
using ProjectApp.Infrastructure.AIServices;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AiController : ControllerBase
    {
        private readonly AIService _aiService;
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly IChatHistoryRepository _chatHistoryRepository;
        private readonly BlogService _blogService;

        public AiController(AIService aiService, IHubContext<ChatHub> hubContext, IChatHistoryRepository chatHistoryRepository, BlogService blogService)
        {
            _aiService = aiService;
            _hubContext = hubContext;
            _chatHistoryRepository = chatHistoryRepository;
            _blogService = blogService;
        }

        [HttpPost("GetAiAnswer")]
        public async Task<ActionResult<ResponseMessage>> GetAiAnswer([FromBody] AiRequest request)
        {
            var fullMessage = "";
            await foreach (var message in _aiService.GetAIAnswer(request.Question, request.Index, request.SessionId, request.Tags))
            {
                fullMessage += message.Message;
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
            }
            return Ok(new ResponseMessage { IsError = false, Message = fullMessage });
        }


        [HttpPost("AddProjectMemory")]
        public async Task<IActionResult> AddProjectMemory()
        {
            await _aiService.AddProjectMemory();
            return Ok();
        }

        [HttpPost("AddMemory")]
        public async Task<IActionResult> AddMemory(AddDataRequest request)
        {
            var result = await _aiService.AddMemory(request.Data, request.DocumentId, request.Index, request.Tags);
            return Ok(result);
        }

        [HttpDelete("DeleteMemory")]
        public async Task<IActionResult> DeleteMemory(string documentId, string index)
        {
            await _aiService.DeleteSingleMemory(documentId, index);
            return Ok();
        }

        [HttpDelete("DeleteMemoryIndex")]
        public async Task<IActionResult> DeleteMemoryIndex(string index)
        {
            await _aiService.DeleteIndexAsync(index);
            return Ok();
        }


        //[HttpPost("SummarizeProject")]
        //public async Task<IActionResult> SummarizeProject(string subject, string description, List<string> fileNames)
        //{
        //    var result = await _aiService.SummarizeProject(subject, description, fileNames);
        //    return Ok(result);
        //}

        [HttpPost("CallAgent")]
        public async Task<ActionResult<ResponseMessage>> CallAgent(string agentName, string question)
        {
            var fullMessage = new StringBuilder();
            await foreach (var message in _aiService.CallAgent(agentName, question))
            {
                if (!string.IsNullOrEmpty(message.Message))
                {
                    fullMessage.Append(message.Message);
                }
                Console.WriteLine(message.Message);
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
            }

            return new ResponseMessage { IsError = false, Message = fullMessage.ToString() };
        }

        //[HttpPost("GenerateProjectTasks")]
        //public async Task<IActionResult> GenerateProjectTasks(string subject, string description, int workspaceId, DateTime startDate, DateTime completionDate, List<string> fileNames)
        //{
        //    var result = await _aiService.GenerateProjectTasks(subject, description, workspaceId, startDate, completionDate, fileNames);
        //    return Ok(result);
        //}

        //[HttpPost("GenerateWorkspace")]
        //public async Task<ActionResult<SuggestWorkspaceResDto>> GenerateWorkspace(string subject, string description, List<string> fileNames)
        //{
        //    var result = await _aiService.GenerateWorkspace(subject, description, fileNames);
        //    return Ok(result);
        //}

        [HttpPost("GenerateEmailTemplate")]
        public async Task<IActionResult> GenerateEmailTemplate(string purpose, Dictionary<string, string> parameters)
        {
            var result = await _aiService.GenerateEmailTemplate(purpose, parameters);
            return Ok(result);
        }
        //[HttpPost("GenerateCompletionDate")]
        //public async Task<IActionResult> GenerateCompletionDate(string subject, string description, string priority, DateTime startDate)
        //{
        //    var result = await _aiService.GenerateCompletionDate(subject, description, priority, startDate);
        //    return Ok(result);
        //}

        [HttpPost("AnalyzeImage")]
        public async Task<IActionResult> AnalyzeImageAndGenerateDescription(IFormFile imageFile)
        {
            if (imageFile == null || imageFile.Length == 0)
                return BadRequest("No image file provided");

            try
            {
                using var memoryStream = new MemoryStream();
                await imageFile.CopyToAsync(memoryStream);
                var imageBytes = memoryStream.ToArray();

                // Determine file type based on extension
                var extension = Path.GetExtension(imageFile.FileName).ToLowerInvariant();
                string fileType = extension switch
                {
                    ".pdf" => "application/pdf",
                    ".jpg" or ".jpeg" => "image/jpeg",
                    ".png" => "image/png",
                    ".txt" => "text/plain",
                    _ => "application/octet-stream"
                };

                var result = await _aiService.AnalyzeImageAndGenerateDescription(imageBytes, fileType);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error processing file: {ex.Message}");
            }
        }

        [HttpGet("GetChatHistory/{sessionId}")]
        public async Task<ActionResult<List<ChatHistoryModel>>> GetChatHistory(string sessionId)
        {
            var history = await _chatHistoryRepository.GetChatHistoryBySessionId(sessionId);
            return Ok(history);
        }

        [HttpGet("GetPluginClassNames")]
        public async Task<ActionResult<ResponseMessageList>> GetPluginClassNames()
        {
            var result = await _aiService.GetPluginClassNames();
            return Ok(result);
        }

        [HttpPost("GenerateBlog")]
        public async Task<ActionResult<ResponseMessage>> GenerateBlog([FromBody] BlogRequest request)
        {
            try
            {
                var blogContent = await _blogService.GenerateBlog(request);
                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = blogContent.ToString()
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage
                {
                    IsError = true,
                    Message = ex.Message
                });
            }
        }
    }
    public class AiRequest
    {
        public string Question { get; set; }
        public string Index { get; set; }
        public string SessionId { get; set; }
        public List<MemoryTag> Tags { get; set; }
    }

}
