<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plugin Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs" defer></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8" x-data="pluginDetails()">
        <div class="mb-6">
            <a href="plugins.html" class="text-blue-600 hover:text-blue-800 transition">
                &larr; Back to All Plugins
            </a>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg overflow-hidden" x-show="plugin">
            <div class="bg-blue-600 p-6">
                <h1 class="text-3xl font-bold text-white" x-text="plugin.pluginName"></h1>
                <div class="mt-2 flex items-center">
                    <span class="px-3 py-1 text-sm rounded-full" 
                          :class="{
                              'bg-green-500': plugin.type === 'OpenAPI',
                              'bg-purple-500': plugin.type === 'MCP',
                              'bg-blue-500': plugin.type === 'Custom'
                          }" 
                          x-text="plugin.type"></span>
                </div>
            </div>
            
            <div class="p-6">
                <div class="mb-6" x-show="(plugin.type === 'OpenAPI' || plugin.type === 'MCP') && plugin.url">
                    <h2 class="text-xl font-semibold text-gray-800 mb-2">API Source</h2>
                    <div class="flex items-center bg-gray-50 rounded-lg p-3 border border-gray-200">
                        <div class="flex-grow font-mono text-sm overflow-auto break-all">
                            <a :href="plugin.url" target="_blank" class="text-blue-600 hover:underline" x-text="plugin.url"></a>
                        </div>
                        <button 
                            x-show="plugin.type === 'OpenAPI'"
                            @click="resyncOpenApiPlugin()"
                            class="ml-4 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium">
                            Re-sync API
                        </button>
                    </div>
                </div>
                
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Functions</h2>
                    <div class="space-y-4">
                        <template x-for="(func, index) in formatFunctions(plugin.functions)" :key="index">
                            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                <div class="font-mono text-lg text-blue-600 font-semibold mb-2" x-text="func.name"></div>
                                <div class="text-gray-700" x-text="func.description"></div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 pt-4 text-sm text-gray-600">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="font-medium">Created:</span> 
                            <span x-text="formatDate(plugin.createdDate)"></span>
                        </div>
                        <div x-show="plugin.lastModifiedDate">
                            <span class="font-medium">Last Modified:</span> 
                            <span x-text="formatDate(plugin.lastModifiedDate)"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button 
                    @click="deletePlugin()"
                    class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded shadow transition">
                    Delete Plugin
                </button>
            </div>
        </div>
        
        <div x-show="!plugin && !loading" class="bg-white rounded-lg shadow p-8 text-center">
            <p class="text-gray-600 mb-4">Plugin not found or an error occurred.</p>
            <a href="plugins.html" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded">
                Return to Plugin List
            </a>
        </div>
        
        <div x-show="loading" class="bg-white rounded-lg shadow p-8 text-center">
            <p class="text-gray-600">Loading plugin details...</p>
        </div>
    </div>

    <script>
        function pluginDetails() {
            return {
                plugin: null,
                loading: true,
                
                init() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const pluginName = urlParams.get('name');
                    
                    if (pluginName) {
                        this.loadPlugin(pluginName);
                    } else {
                        this.loading = false;
                    }
                },
                
                loadPlugin(pluginName) {
                    this.loading = true;
                    
                    fetch(`/api/Plugin/GetByName/${encodeURIComponent(pluginName)}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Plugin not found');
                            }
                            return response.json();
                        })
                        .then(data => {
                            this.plugin = data;
                            document.title = `${data.pluginName} - Plugin Details`;
                        })
                        .catch(error => {
                            console.error('Error loading plugin:', error);
                        })
                        .finally(() => {
                            this.loading = false;
                        });
                },
                
                formatFunctions(functionsStr) {
                    if (!functionsStr) return [];
                    
                    return functionsStr.split('\n').map(func => {
                        const parts = func.split(' - ');
                        return {
                            name: parts[0],
                            description: parts.length > 1 ? parts[1] : 'No description'
                        };
                    });
                },
                
                formatDate(dateStr) {
                    if (!dateStr) return '';
                    return new Date(dateStr).toLocaleString();
                },
                
                resyncOpenApiPlugin() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const pluginName = urlParams.get('name');
                    
                    if (!pluginName) return;
                    
                    this.loading = true;
                    
                    fetch(`/api/Plugin/ResyncOpenApiPlugin/${encodeURIComponent(pluginName)}`, {
                        method: 'POST'
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to re-sync plugin');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (!data.isError) {
                            // Reload the plugin data
                            this.loadPlugin(pluginName);
                            alert('OpenAPI plugin successfully re-synced!');
                        } else {
                            alert(`Error: ${data.message}`);
                        }
                    })
                    .catch(error => {
                        console.error('Error re-syncing plugin:', error);
                        alert(`Error re-syncing plugin: ${error.message}`);
                    })
                    .finally(() => {
                        this.loading = false;
                    });
                },

                deletePlugin() {
                    if (!confirm('Are you sure you want to delete this plugin?')) return;
                    
                    const urlParams = new URLSearchParams(window.location.search);
                    const pluginName = urlParams.get('name');
                    
                    if (!pluginName) return;
                    
                    this.loading = true;
                    
                    fetch(`/api/Plugin/Delete/${encodeURIComponent(pluginName)}`, {
                        method: 'DELETE'
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to delete plugin');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (!data.isError) {
                            window.location.href = 'plugins.html';
                        } else {
                            alert(`Error: ${data.message}`);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting plugin:', error);
                        alert(`Error deleting plugin: ${error.message}`);
                    })
                    .finally(() => {
                        this.loading = false;
                    });
                }
            };
        }
    </script>
</body>
</html> 