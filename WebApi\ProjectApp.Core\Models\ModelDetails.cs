﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Models
{
    public class ModelDetails
    {
        public Guid Id { get; set; }
        public string ModelName { get; set; }
        public string ModelProvider { get; set; }
        public bool IsActive { get; set; }
        public bool IsEmbeddingActive { get; set; }
        public bool IsCustom { get; set; }
        public Guid? ApiCredentialsId { get; set; }
    }
}
