﻿using AutoMapper;
using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositiories;
using ProjectApp.Core.Repositories;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class CommentRepository(IDbConnection _dbConnection, IMapper _mapper, IExtractEmailFromAccessor _extractEmail, IUserAccountRepository _userAccount) : ICommentRepository
    {
        public async Task<List<Comment>> GetAllByProjectId(int projectId)
        {
            var comments = await _dbConnection.QueryAsync<Comment>("SELECT * FROM Comments WHERE ProjectId = @ProjectId", new { ProjectId = projectId });
            return comments.ToList();
        }

        public async Task<Comment> AddComment(CommentDto request)
        {
            var email = _extractEmail.GetEmail();
            var user = await _userAccount.IsUser(email);

            if (user != null)
            {
                var comment = _mapper.Map<Comment>(request);
                comment.TimeStamp = DateTime.Now;
                comment.Email = email;

                var insertQuery = "INSERT INTO Comments (ProjectId, Message, TimeStamp, Email) OUTPUT INSERTED.Id VALUES (@ProjectId, @Message, @TimeStamp, @Email)";
                comment.Id = await _dbConnection.ExecuteScalarAsync<int>(insertQuery, comment);

                return comment;
            }
            else
            {
                throw new Exception("User not found");
            }
        }
    }
}
