﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IDailyInsightAgentRepository
    {
        Task<IEnumerable<DailyInsightAgent>> GetAllByUserEmailAsync(string userEmail);
        Task<DailyInsightAgent> GetByIdAsync(int id);
        Task<DailyInsightAgent> CreateAsync(DailyInsightAgentDto dto, string userEmail);
        Task<bool> DeleteAsync(int id);
        Task<bool> UpdateLastRunAsync(int id, string response);
        Task<IEnumerable<DailyInsightAgent>> GetAllAgentsForSyncAsync();
    }
}
