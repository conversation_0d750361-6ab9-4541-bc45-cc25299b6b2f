﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ProjectApp.WebApi.Controllers
{
    //[Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ChatModelController : ControllerBase
    {
        private readonly IChatModelRepository _repository;
        private readonly IMapper _mapper;

        public ChatModelController(
            IChatModelRepository repository,
            IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<List<ChatModel>>> GetAll()
        {
            var models = await _repository.GetAllAsync();
            return Ok(models);
        }

        [HttpGet("GetByModelId/{modelId}")]
        public async Task<ActionResult<ChatModel>> GetByModelId(string modelId)
        {
            var model = await _repository.GetByModelIdAsync(modelId);
            if (model == null)
                return NotFound(new ResponseMessage { IsError = true, Message = $"Chat model with ID {modelId} not found" });

            return Ok(model);
        }

        [HttpPost("CreateOrUpdate")]
        public async Task<ActionResult<ChatModel>> CreateOrUpdate([FromBody] CreateChatModelDto createDto)
        {
            var result = await _repository.CreateOrUpdate(createDto);
            return Ok(result);
        }

        [HttpDelete("Delete/{modelId}")]
        public async Task<ActionResult<ResponseMessage>> DeleteByName(string modelId)
        {
            try
            {
                var deleted = await _repository.DeleteAsync(modelId);
                if (!deleted)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Chat model with name {modelId} not found." });
                }
                return Ok(new ResponseMessage { IsError = false, Message = "Chat model deleted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }
    }
}
