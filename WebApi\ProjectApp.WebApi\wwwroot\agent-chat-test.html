<!DOCTYPE html>
<html>
<head>
    <title>Agent Chat SignalR Test</title>
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .chat-messages { 
            border: 1px solid #ccc; 
            height: 400px; 
            overflow-y: scroll; 
            padding: 10px; 
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .message { 
            margin: 5px 0; 
            padding: 8px;
            border-radius: 5px;
        }
        .user-message { 
            background-color: #e3f2fd; 
            text-align: right;
        }
        .agent-message { 
            background-color: #f1f8e9; 
            text-align: left;
        }
        .input-group { 
            margin: 10px 0; 
        }
        label { 
            display: inline-block; 
            width: 100px; 
            font-weight: bold;
        }
        input, select, textarea { 
            width: 300px; 
            padding: 5px; 
            margin: 5px;
        }
        button { 
            padding: 10px 20px; 
            margin: 5px; 
            cursor: pointer;
        }
        .status { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Agent Chat SignalR Test</h1>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="input-group">
            <label>User Email:</label>
            <input type="email" id="userEmail" value="<EMAIL>" />
        </div>
        
        <div class="input-group">
            <label>Agent:</label>
            <select id="agentName">
                <option value="WeatherAgent">Weather Agent</option>
                <option value="CodeAgent">Code Agent</option>
                <option value="GeneralAgent">General Agent</option>
                <option value="CustomAgent">Custom Agent</option>
            </select>
        </div>
        
        <div class="input-group">
            <label>Question:</label>
            <textarea id="question" rows="3" placeholder="Type your question here..."></textarea>
        </div>
        
        <div>
            <button onclick="connect()">Connect to SignalR</button>
            <button onclick="disconnect()">Disconnect</button>
            <button onclick="sendMessage()">Send Message</button>
            <button onclick="clearChat()">Clear Chat</button>
        </div>
        
        <div id="messages" class="chat-messages"></div>
        
        <div>
            <h3>Agent Conversation History</h3>
            <button onclick="loadAgentHistory()">Load History</button>
            <button onclick="loadUserAgents()">Load User Agents</button>
        </div>
        
        <div id="historyMessages" class="chat-messages"></div>
    </div>

    <script>
        let connection = null;
        
        async function connect() {
            try {
                const baseUrl = window.location.origin.replace(/:\d+/, ':7257'); // Adjust port as needed
                connection = new signalR.HubConnectionBuilder()
                    .withUrl(`${baseUrl}/chathub`)
                    .build();

                connection.on("ReceiveAgentResponse", function (response) {
                    addMessage(`Agent: ${response.responseText}`, 'agent-message');
                    console.log('Received response:', response);
                });

                await connection.start();
                document.getElementById('status').textContent = 'Connected';
                document.getElementById('status').className = 'status connected';
                
                // Join user group
                const userEmail = document.getElementById('userEmail').value;
                await connection.invoke('JoinUserGroup', userEmail);
                addMessage(`Connected and joined group for: ${userEmail}`, 'system-message');
                
            } catch (err) {
                console.error('Connection failed:', err);
                document.getElementById('status').textContent = 'Connection Failed';
                document.getElementById('status').className = 'status disconnected';
            }
        }

        async function disconnect() {
            if (connection) {
                await connection.stop();
                document.getElementById('status').textContent = 'Disconnected';
                document.getElementById('status').className = 'status disconnected';
                connection = null;
            }
        }

        async function sendMessage() {
            const userEmail = document.getElementById('userEmail').value;
            const agentName = document.getElementById('agentName').value;
            const question = document.getElementById('question').value;
            
            if (!question.trim()) {
                alert('Please enter a question');
                return;
            }
            
            addMessage(`You: ${question}`, 'user-message');
            
            try {
                const baseUrl = window.location.origin.replace(/:\d+/, ':7257'); // Adjust port as needed
                const response = await fetch(`${baseUrl}/api/AgentChat/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    },
                    body: JSON.stringify({
                        userEmail: userEmail,
                        agentName: agentName,
                        question: question
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('Chat sent successfully:', result);
                    // Clear the question input
                    document.getElementById('question').value = '';
                } else {
                    const error = await response.text();
                    addMessage(`Error: ${error}`, 'error-message');
                }
            } catch (err) {
                console.error('Error sending message:', err);
                addMessage(`Error: ${err.message}`, 'error-message');
            }
        }

        async function loadAgentHistory() {
            const agentName = document.getElementById('agentName').value;
            try {
                const baseUrl = window.location.origin.replace(/:\d+/, ':7257');
                const response = await fetch(`${baseUrl}/api/AgentChat/agent/${agentName}/conversation?pageSize=10&pageNumber=1`, {
                    headers: {
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    displayHistory(result.data);
                } else {
                    addHistoryMessage(`Error loading history: ${response.statusText}`);
                }
            } catch (err) {
                addHistoryMessage(`Error: ${err.message}`);
            }
        }

        async function loadUserAgents() {
            try {
                const baseUrl = window.location.origin.replace(/:\d+/, ':7257');
                const response = await fetch(`${baseUrl}/api/AgentChat/user-agents`, {
                    headers: {
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addHistoryMessage(`Your agents: ${result.data.join(', ')}`);
                } else {
                    addHistoryMessage(`Error loading agents: ${response.statusText}`);
                }
            } catch (err) {
                addHistoryMessage(`Error: ${err.message}`);
            }
        }

        function displayHistory(conversation) {
            const historyDiv = document.getElementById('historyMessages');
            historyDiv.innerHTML = `<h4>History for ${conversation.agentName}:</h4>`;
            
            conversation.conversations.forEach(conv => {
                addHistoryMessage(`Q: ${conv.question}`);
                conv.responses.forEach(resp => {
                    addHistoryMessage(`A: ${resp.responseText}`);
                });
                addHistoryMessage('---');
            });
        }

        function addMessage(message, className) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${className}`;
            messageDiv.innerHTML = `<small>${new Date().toLocaleTimeString()}</small><br>${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function addHistoryMessage(message) {
            const historyDiv = document.getElementById('historyMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.textContent = message;
            historyDiv.appendChild(messageDiv);
            historyDiv.scrollTop = historyDiv.scrollHeight;
        }

        function clearChat() {
            document.getElementById('messages').innerHTML = '';
        }

        // Auto-connect on page load
        window.onload = function() {
            // Uncomment to auto-connect
            // connect();
        };
    </script>
</body>
</html>
