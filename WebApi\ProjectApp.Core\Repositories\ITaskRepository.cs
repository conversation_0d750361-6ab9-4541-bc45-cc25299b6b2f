﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;


namespace ProjectApp.Core.Repositories
{
    public interface ITaskRepository
    {
        Task<List<ProjectTask>> GetAllProjectTasks();
        Task<List<ProjectTask>> GetByProjectId(int projectId);
        Task<ProjectTask> GetTaskById(int id);
        Task<ProjectTask> CreateOrUpdate(ProjectTask request);
        Task<ProjectTask> UpdateTaskStatus(int taskId, string isCompleted);
        Task<ProjectTask> Delete(int id);
        Task<List<ProjectTask>> GetTodayProjectTasks();
        Task<List<ProjectTask>> GetUpcomingProjectTasks();
        Task<List<ProjectTask>> GetPastDueProjectTasks();
        Task<List<ProjectTask>> GetProjectTasksByWorkspacesId( int workspacesId);

        Task CreateProjectTasksFromAi(List<TaskDateDto> tasksDateDtos, int projectId, string assignedEmails);
    }
}
