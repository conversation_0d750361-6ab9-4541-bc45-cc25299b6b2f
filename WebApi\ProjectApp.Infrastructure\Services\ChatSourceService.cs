using System;
using System.Collections.Generic;
using System.Linq;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Infrastructure.Services
{
    public interface IChatSourceService
    {
        void TrackFunctionInvocation(string pluginName, string pluginDescription, string functionName, string description);
        List<ChatSource> GenerateChatSources();
        void ClearTrackedInvocations();
    }

    public class ChatSourceService : IChatSourceService
    {
        private readonly List<(string PluginName, string pluginDescription, string FunctionName, string description)> _functionInvocations = new();

        public void TrackFunctionInvocation(string pluginName, string pluginDescription, string functionName, string description)
        {
            _functionInvocations.Add((pluginName, pluginDescription, functionName, description));
        }

        public List<ChatSource> GenerateChatSources()
        {
            if (!_functionInvocations.Any())
            {
                return new List<ChatSource>();
            }

            var chatSources = new List<ChatSource>();

            // Create a plugin source
            var pluginSource = new ChatSource
            {
                Source = "Plugin",
                ChatSourceDescriptions = _functionInvocations
                    .Select(f => new ChatSourceDescription
                    {
                        Title = $"{f.PluginName} | {f.FunctionName}",
                        Description = f.description,
                        URL = $"/settings/plugins/{f.PluginName}"
                    })
                    .ToList()
            };

            chatSources.Add(pluginSource);

            return chatSources;
        }

        public void ClearTrackedInvocations()
        {
            _functionInvocations.Clear();
        }
    }
}
