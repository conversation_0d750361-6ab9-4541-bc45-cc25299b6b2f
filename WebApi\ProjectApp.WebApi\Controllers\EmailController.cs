using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Models;
using ProjectApp.Core.Dtos;
using ProjectApp.Infrastructure.Services;
using System.Threading.Tasks;

namespace ProjectApp.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EmailController : ControllerBase
    {
        private readonly EmailService _emailService;

        public EmailController(EmailService emailService)
        {
            _emailService = emailService;
        }

        [HttpPost("send-project-creation-email")]
        public async Task<IActionResult> SendProjectCreationEmail([FromBody] Project project)
        {
            if (project == null)
            {
                return BadRequest("Project data is required.");
            }

            await _emailService.SendProjectCreationEmail(project);
            return Ok("Project creation email sent successfully.");
        }

        [HttpPost("send-daily-project-updates")]
        public async Task<IActionResult> SendDailyProjectUpdates()
        {
            await _emailService.SendDailyProjectUpdates();
            return Ok("Daily project updates sent successfully.");
        }

        [HttpPost("SendCustomEmail")]
        public async Task<IActionResult> SendCustomEmail([FromBody] SendEmailDto emailDto)
        {
            if (emailDto == null)
            {
                return BadRequest("Email data is required.");
            }

            await _emailService.SendEmailAsync(emailDto.To, emailDto.Cc, emailDto.Subject, emailDto.HtmlBody);
            return Ok("Custom email sent successfully.");
        }
    }
}
