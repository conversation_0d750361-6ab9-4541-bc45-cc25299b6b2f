namespace ProjectApp.Core.Models
{
    public class ChatMessage
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public string UserEmail { get; set; }
        public string WorkspaceName { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public bool IsPinned { get; set; }
        public bool IsFavorite { get; set; }
        public bool IsArchived { get; set; }
    }
} 