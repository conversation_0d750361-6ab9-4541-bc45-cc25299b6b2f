using System;
using System.IO;
using System.Text;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using UglyToad.PdfPig;
using UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor;
using Microsoft.SemanticKernel.ChatCompletion;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;
using Microsoft.SemanticKernel;

namespace ProjectApp.Infrastructure.Services
{
    public class DocumentTextExtractor
    {
        /// <summary>
        /// Extracts text from various document types with AI support for images
        /// </summary>
        /// <param name="filePath">Full path to the file</param>
        /// <param name="chatCompletionService">chatCompletionService for Ai Processing</param>
        /// <returns>Extracted text content</returns>
        public static async Task<string> ExtractTextAsync(string filePath, IChatCompletionService chatCompletionService)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "Error: File not found.";

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileName = Path.GetFileName(filePath);

                return extension switch
                {
                    ".pdf" => ExtractFromPdf(filePath),
                    ".txt" => ExtractFromText(filePath),
                    ".csv" => ExtractFromCsv(filePath),
                    ".xlsx" or ".xls" => ExtractFromExcel(filePath),
                    ".docx" => ExtractFromWord(filePath),
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => await ExtractFromImageAsync(filePath, chatCompletionService),
                    _ => $"Unsupported file type: {extension}. Supported types: PDF, TXT, CSV, XLSX, XLS, DOCX, and image files (JPG, PNG, etc.)"
                };
            }
            catch (Exception ex)
            {
                return $"Error extracting text from file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from PDF files using PdfPig
        /// </summary>
        private static string ExtractFromPdf(string filePath)
        {
            try
            {
                var result = new StringBuilder();
                var fileBytes = File.ReadAllBytes(filePath);

                using (var document = PdfDocument.Open(fileBytes))
                {
                    result.AppendLine($"PDF Document: {Path.GetFileName(filePath)} - {document.NumberOfPages} page(s)");
                    result.AppendLine();

                    for (int i = 1; i <= document.NumberOfPages; i++)
                    {
                        var page = document.GetPage(i);
                        var text = ContentOrderTextExtractor.GetText(page);

                        result.AppendLine($"--- Page {i} ---");
                        result.AppendLine(text);
                        result.AppendLine();
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from PDF: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from plain text files
        /// </summary>
        private static string ExtractFromText(string filePath)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                return $"Text File: {Path.GetFileName(filePath)}\n\n{content}";
            }
            catch (Exception ex)
            {
                return $"Error reading text file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from CSV files
        /// </summary>
        private static string ExtractFromCsv(string filePath)
        {
            try
            {
                var result = new StringBuilder();
                var lines = File.ReadAllLines(filePath);

                result.AppendLine($"CSV File: {Path.GetFileName(filePath)} - {lines.Length} rows");
                result.AppendLine();

                foreach (var line in lines.Take(100)) // Limit to first 100 rows
                {
                    result.AppendLine(line);
                }

                if (lines.Length > 100)
                {
                    result.AppendLine($"... and {lines.Length - 100} more rows");
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error reading CSV file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from Excel files using ClosedXML
        /// </summary>
        private static string ExtractFromExcel(string filePath)
        {
            try
            {
                var result = new StringBuilder();

                using (var workbook = new XLWorkbook(filePath))
                {
                    result.AppendLine($"Excel File: {Path.GetFileName(filePath)} - {workbook.Worksheets.Count} worksheet(s)");
                    result.AppendLine();

                    foreach (var worksheet in workbook.Worksheets)
                    {
                        result.AppendLine($"--- Worksheet: {worksheet.Name} ---");

                        var usedRange = worksheet.RangeUsed();
                        if (usedRange != null)
                        {
                            var rowCount = usedRange.RowCount();
                            var colCount = usedRange.ColumnCount();

                            result.AppendLine($"Data Range: {rowCount} rows x {colCount} columns");
                            result.AppendLine();

                            // Extract first 50 rows to avoid too much data
                            var maxRows = Math.Min(rowCount, 50);

                            for (int row = 1; row <= maxRows; row++)
                            {
                                var rowData = new List<string>();
                                for (int col = 1; col <= colCount; col++)
                                {
                                    var cellValue = usedRange.Cell(row, col).GetString();
                                    rowData.Add(cellValue);
                                }
                                result.AppendLine(string.Join(" | ", rowData));
                            }

                            if (rowCount > 50)
                            {
                                result.AppendLine($"... and {rowCount - 50} more rows");
                            }
                        }
                        else
                        {
                            result.AppendLine("No data found in this worksheet.");
                        }

                        result.AppendLine();
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from Excel file: {ex.Message}";
            }
        }

        /// <summary>
        /// Extracts text from Word documents using DocumentFormat.OpenXml
        /// </summary>
        private static string ExtractFromWord(string filePath)
        {
            try
            {
                var result = new StringBuilder();

                using (var document = WordprocessingDocument.Open(filePath, false))
                {
                    result.AppendLine($"Word Document: {Path.GetFileName(filePath)}");
                    result.AppendLine();

                    var body = document.MainDocumentPart?.Document?.Body;
                    if (body != null)
                    {
                        foreach (var paragraph in body.Elements<Paragraph>())
                        {
                            var text = paragraph.InnerText;
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                result.AppendLine(text);
                            }
                        }

                        // Extract text from tables
                        foreach (var table in body.Elements<Table>())
                        {
                            result.AppendLine("\n--- Table ---");
                            foreach (var row in table.Elements<TableRow>())
                            {
                                var cellTexts = row.Elements<TableCell>().Select(cell => cell.InnerText.Trim());
                                result.AppendLine(string.Join(" | ", cellTexts));
                            }
                            result.AppendLine();
                        }
                    }
                    else
                    {
                        result.AppendLine("No content found in the document.");
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from Word document: {ex.Message}";
            }
        }



        /// <summary>
        /// Extracts text from image files
        /// </summary>
        private static async Task<string> ExtractFromImageAsync(string filePath, IChatCompletionService chatCompletionService)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);

                // Check if chat completion service is available
                if (chatCompletionService == null)
                {
                    return $"Error: AI service is required for image text extraction. Image file: {fileName}";
                }

                // Read image file as bytes
                var fileBytes = await File.ReadAllBytesAsync(filePath);
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                // Create chat history with a single message containing text and image
                var chat = new ChatHistory();
                var base64Image = Convert.ToBase64String(fileBytes);
                var mimeType = GetImageMimeType(extension);

                // Combine prompt and image in a single message
                chat.AddUserMessage(new ChatMessageContentItemCollection
                {
                    new TextContent("Extract all readable text from the provided image. Include any visible text, numbers, or written content. If no text is found, indicate that no text was detected."),
                    new ImageContent(fileBytes, mimeType)
                });

                // Get AI response
                var response = await chatCompletionService.GetChatMessageContentAsync(chat);

                // Format the result
                string result = response?.Content?.Trim() ?? "";
                if (string.IsNullOrWhiteSpace(result))
                {
                    return $"Could not extract text from image file: {fileName}. The AI service returned an empty response.";
                }

                // Clean up the formatting
                result = FormatAIResponse(result);

                var finalResult = new StringBuilder();
                finalResult.AppendLine($"Image File: {fileName}");
                finalResult.AppendLine($"AI Text Extraction Result:");
                finalResult.AppendLine();
                finalResult.AppendLine(result);

                return finalResult.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from image using AI: {ex.Message}";
            }
        }

        /// <summary>
        /// Gets file information without extracting content
        /// </summary>
        public static string GetFileInfo(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "File not found.";

                var fileInfo = new FileInfo(filePath);
                var extension = fileInfo.Extension.ToLowerInvariant();

                return $"File: {fileInfo.Name}\n" +
                       $"Size: {fileInfo.Length:N0} bytes\n" +
                       $"Type: {extension}\n" +
                       $"Created: {fileInfo.CreationTime}\n" +
                       $"Modified: {fileInfo.LastWriteTime}";
            }
            catch (Exception ex)
            {
                return $"Error getting file info: {ex.Message}";
            }
        }

        /// <summary>
        /// Formats AI response text to fix common formatting issues
        /// </summary>
        private static string FormatAIResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return response;

            // Basic formatting - remove extra whitespace and normalize line endings
            return response.Trim()
                          .Replace("\r\n", "\n")
                          .Replace("\r", "\n");
        }

        /// <summary>
        /// Gets the MIME type for image files
        /// </summary>
        private static string GetImageMimeType(string extension)
        {
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                _ => "application/octet-stream"
            };
        }
    }
}
