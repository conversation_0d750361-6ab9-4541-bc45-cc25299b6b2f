using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Core.Dtos;
using Microsoft.AspNetCore.Mvc;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using System.Text;
using ProjectApp.Infrastructure;
using DocumentFormat.OpenXml.Office.SpreadSheetML.Y2023.MsForms;
using Microsoft.AspNetCore.SignalR;
using ProjectApp.WebApi.Hubs;
using System.Text.Json;
using System.Data;
using Dapper;
using ProjectApp.Infrastructure.AIAgents;
using Microsoft.KernelMemory;
using ProjectApp.Infrastructure.Services;

namespace AiHub.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ChatController : ControllerBase
    {
        private readonly IChatRepository _chatRepository;
        private readonly IMapper _mapper;
        private readonly AIService _aiService;
        private readonly IExtractEmailFromAccessor _extractEmailFromAccessor;
        private readonly IHubContext<ChatHub> _hubContext;
        private readonly IDbConnection _dbConnection;
        private readonly AIAgentFactory _aIAgentFactory;
        private readonly IChatSourceService _chatSourceService;
        private readonly IHttpClientFactory _httpClientFactory;

        public ChatController(
            IChatRepository chatRepository,
            IMapper mapper,
            AIService aiService,
            IExtractEmailFromAccessor extractEmailAccessor,
            IHubContext<ChatHub> hubContext,
            IDbConnection dbConnection,
            AIAgentFactory aiAgentFactory,
            IChatSourceService chatSourceService,
            IHttpClientFactory httpClientFactory)
        {
            _chatRepository = chatRepository;
            _mapper = mapper;
            _aiService = aiService;
            _extractEmailFromAccessor = extractEmailAccessor;
            _hubContext = hubContext;
            _dbConnection = dbConnection;
            _aIAgentFactory = aiAgentFactory;
            _chatSourceService = chatSourceService;
            _httpClientFactory = httpClientFactory;
        }

        [HttpPost("send")]
        public async Task<ActionResult<ChatMessageDto>> SendMessage(ChatRequestDto request)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            string selectedAgentName = "";
            if (string.IsNullOrEmpty(request.AgentName))
            {


                var tag = new List<MemoryTag>();
                tag.Add(new MemoryTag
                {
                    Name = "WorkspaceName",
                    Value = request.Workspace
                });

                // Get the list of available agents from memory
                var searchMessage = await _aiService.SearchMemory(request.Message, "Agent", tag, 0.2);
                Console.WriteLine($"Memory search returned {searchMessage.Count} results");

                if (false)
                {
                    // Create a dictionary to store agent information
                    var agentDictionary = new Dictionary<string, object>();

                    // Add the user's question as an argument
                    agentDictionary["userQuestion"] = request.Message;

                    // Add the workspace as an argument if available
                    //if (!string.IsNullOrEmpty(request.Workspace))
                    //{
                    //    agentDictionary["workspace"] = request.Workspace;
                    //}

                    // Add the agent list as an array argument
                    //var agentList = new List<Dictionary<string, string>>();
                    //foreach (var message in searchMessage)
                    //{
                    //    Console.WriteLine($"Processing memory result: {message}");

                    //    // Parse the agent information from the memory result
                    //    var agentInfo = new Dictionary<string, string>();

                    //    // Extract agent name and description using improved parsing
                    //    var lines = message.Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    //    foreach (var line in lines)
                    //    {
                    //        // Improved parsing with better error handling
                    //        var colonIndex = line.IndexOf(':');
                    //        if (colonIndex > 0)
                    //        {
                    //            var key = line.Substring(0, colonIndex).Trim();
                    //            var value = line.Substring(colonIndex + 1).Trim();

                    //            // Map to expected keys
                    //            if (key.Equals("AgentName", StringComparison.OrdinalIgnoreCase))
                    //            {
                    //                agentInfo["AgentName"] = value;
                    //            }
                    //            else if (key.Contains("Description", StringComparison.OrdinalIgnoreCase))
                    //            {
                    //                agentInfo["AgentDescription"] = value;
                    //            }
                    //        }
                    //    }

                    //    // Add to the list if we have both name and description
                    //    if (agentInfo.ContainsKey("AgentName"))
                    //    {
                    //        // Ensure we have a description, even if empty
                    //        if (!agentInfo.ContainsKey("AgentDescription"))
                    //        {
                    //            agentInfo["AgentDescription"] = "No description available";
                    //        }

                    //        agentList.Add(agentInfo);
                    //        Console.WriteLine($"Added agent: {agentInfo["AgentName"]} with description: {agentInfo["AgentDescription"]}");
                    //    }
                    //}

                    // Add the agent list to the arguments dictionary
                    searchMessage = searchMessage.Select(x => x.Replace("\n", " ")).ToList();
                    agentDictionary["availableAgents"] = String.Join(", ", searchMessage);

                    // Check if the agent list is empty
                    //if (agentList.Count == 0)
                    //{
                    //    Console.WriteLine("Warning: No agents found in memory search results");
                    //    return BadRequest("No suitable agents found for your question.");
                    //}

                    // Debug logging
                    //Console.WriteLine($"Agent list: {System.Text.Json.JsonSerializer.Serialize(agentList)}");

                    // Create an agent definition with proper instructions for selecting the best agent
                    //var agentSelectorDefinition = new AgentDefinitionDto
                    //{
                    //    AgentName = "AgentSelector",
                    //    Instructions = "You are an agent selection expert. Your task is to analyze the user's question and select the most appropriate agent from the available agents list. " +
                    //                  "Review each agent's name and description carefully to determine which one is best suited to handle the user's question. " +
                    //                  "Return ONLY the name of the selected agent. If none of the available agents are suitable, return 'Default'.",
                    //    ModelName = "gpt-4o-mini",
                    //    Arguments = agentDictionary
                    //};

                    // Debug logging before calling the agent
                    Console.WriteLine($"Calling AgentSelector with arguments: {System.Text.Json.JsonSerializer.Serialize(agentDictionary)}");

                    // Call the agent selector with the arguments
                    var agentResponse1 = await _aIAgentFactory.AgentResponseAsync("AgentSelector", request.Message, agentDictionary);

                    // Debug logging after calling the agent
                    Console.WriteLine($"AgentSelector response: {agentResponse1.Message}");

                    // Get the selected agent name from the response
                    var selectedAgentName1 = agentResponse1.Message.Trim();

                    // Check if the selected agent name is valid
                    if (string.IsNullOrEmpty(selectedAgentName1) || selectedAgentName1 == "Default")
                    {
                        Console.WriteLine($"AgentSelector returned invalid agent name: '{selectedAgentName1}'");
                        return BadRequest("Could not determine the appropriate agent for your question.");
                    }

                    // Now call the selected agent with the user's question
                    Console.WriteLine($"Calling selected agent: {selectedAgentName1}");
                    agentResponse1 = await _aIAgentFactory.AgentResponseAsync(selectedAgentName1, request.Message);
                    // Check if the agent response is valid
                    if (agentResponse1 == null || string.IsNullOrEmpty(agentResponse1.Message))
                    {
                        return BadRequest("Selected agent response is empty or invalid.");
                    }
                }

                var firstMessage = searchMessage.FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(firstMessage))
                {
                    var lines = firstMessage.Split('\n');
                    var agentNameLine = lines.FirstOrDefault(line => line.TrimStart().StartsWith("AgentName:", StringComparison.OrdinalIgnoreCase));

                    if (agentNameLine != null)
                    {
                        selectedAgentName = agentNameLine.Split(':').Skip(1).FirstOrDefault()?.Trim();
                    }
                }
            }
            else
            {
                selectedAgentName = request.AgentName;
            }

            // Get the agent definition for the selected agent
            var agentInformation = await _dbConnection.QueryFirstOrDefaultAsync<AgentDefinition>(
                "SELECT * FROM AgentDefinitions WHERE AgentName = @AgentName",
                new { AgentName = selectedAgentName });

            if (agentInformation == null)
            {
                return BadRequest($"Agent not found for your query.");
            }

            var memoryTags = new List<MemoryTag>
                {
                   new MemoryTag
                   {
                       Name = "Email",
                       Value = userEmail,
                   }
                };
            var userMemory = await _aiService.SearchMemory(request.Message, "Memory", memoryTags, 0.2);
            var formattedUserMemory = string.Join(" ", userMemory.Select((memory, index) => $"{index + 1}. {memory}"));
            var question = $"Question: {request.Message} \n User Memory: {formattedUserMemory}";


            var agentResponse = new StringBuilder();
            var responseType = "";
            await foreach (var message in _aiService.GetResponse(question, selectedAgentName, agentInformation?.ModelName, request.Workspace, null))
            {
                responseType = message.ResponseType;
                // Send the message to all connected clients
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                // Append the message to the response
                agentResponse.Append(message.Message);
            }

            // Get chat sources from the function invocations tracked during the response generation
            var chatSourcesData = _chatSourceService.GenerateChatSources();

            // Clear the tracked invocations for the next request
            _chatSourceService.ClearTrackedInvocations();

            // Save the chat message
            var (chatMessage, _) = await _chatRepository.SaveChatMessageAsync(
                userEmail,
                request.Message,
                agentResponse.ToString(),
                agentInformation?.Workspace,
                agentInformation?.AgentName,
                agentInformation?.ModelName,
                responseType.ToString(),
                chatSourcesData
            );

            var chatMessageDto = Ok(_mapper.Map<ChatMessageDto>(chatMessage));
            return chatMessageDto;
        }

        [HttpPost("continue")]
        public async Task<ActionResult<ChatHistoryDto>> ContinueChat(ChatContinueRequestDto request)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            string selectedAgentName = "";

            if (string.IsNullOrEmpty(request.AgentName))
            {
                var tag = new List<MemoryTag>();
                tag.Add(new MemoryTag
                {
                    Name = "WorkspaceName",
                    Value = request.Workspace
                });

                // Get the list of available agents from memory
                var searchMessage = await _aiService.SearchMemory(request.Message, "Agent", tag, 0.2);
                Console.WriteLine($"Memory search returned {searchMessage.Count} results");

                var firstMessage = searchMessage.FirstOrDefault();

                if (!string.IsNullOrWhiteSpace(firstMessage))
                {
                    var lines = firstMessage.Split('\n');
                    var agentNameLine = lines.FirstOrDefault(line => line.TrimStart().StartsWith("AgentName:", StringComparison.OrdinalIgnoreCase));

                    if (agentNameLine != null)
                    {
                        selectedAgentName = agentNameLine.Split(':').Skip(1).FirstOrDefault()?.Trim();
                    }
                }
            }
            else
            {
                selectedAgentName = request.AgentName;
            }

            // Validate access
            var existingChat = await _chatRepository.GetChatMessageAsync(request.ChatMessageId, userEmail);
            if (existingChat == null)
            {
                return NotFound("Chat not found or access denied");
            }

            var history = await _chatRepository.GetChatHistoryAsync(request.ChatMessageId, userEmail);

            // Get the agent definition for the selected agent
            var agentInformation = await _dbConnection.QueryFirstOrDefaultAsync<AgentDefinition>(
                "SELECT * FROM AgentDefinitions WHERE AgentName = @AgentName",
                new { AgentName = selectedAgentName });

            var memoryTags = new List<MemoryTag>
                {
                   new MemoryTag
                   {
                       Name = "Email",
                       Value = userEmail,
                   }
                };
            var userMemory = await _aiService.SearchMemory(request.Message, "Memory", memoryTags, 0.2);
            var formattedUserMemory = string.Join(" ", userMemory.Select((memory, index) => $"{index + 1}. {memory}"));
            var question = $"Question: {request.Message} \n User Memory: {formattedUserMemory}";

            var response = _aiService.GetResponse(question, selectedAgentName, agentInformation?.ModelName, request.Workspace, history.ToList());
            var fullResponse = new StringBuilder();
            var responseType = "";
            await foreach (var message in response)
            {
                responseType = message.ResponseType;
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                fullResponse.Append(message.Message);
            }

            // Get chat sources from the function invocations tracked during the response generation
            var chatSourcesData = _chatSourceService.GenerateChatSources();

            // Clear the tracked invocations for the next request
            _chatSourceService.ClearTrackedInvocations();

            var chatHistory = await _chatRepository.AddToChatHistoryAsync(
                request.ChatMessageId,
                request.Message,
                fullResponse.ToString(),
                selectedAgentName,
                agentInformation?.ModelName,
                responseType.ToString()
            );

            return Ok(_mapper.Map<ChatHistoryDto>(chatHistory));
        }

        [HttpPost("edit")]
        public async Task<ActionResult<ChatHistoryDto>> EditChat([FromBody] ChatEditRequestDto request)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();

            // Hardcoded values for now
            string agentName = "Developer";
            string modelName = "gpt-4o-mini";

            // Temporarily bypassing CallAgent and hardcoding agentName and modelName
            if (false) // Invalid condition to ensure this block is never executed
            {
                string messageWithWorkspace = string.IsNullOrEmpty(request.Workspace)
                ? request.NewMessage
                : $"Workspace: {request.Workspace}\nMessage: {request.NewMessage}";
                var agentResponse = await _aiService.CallAgent("WorkspaceAgentChooser", messageWithWorkspace).FirstOrDefaultAsync();
                if (agentResponse == null || string.IsNullOrEmpty(agentResponse.Message))
                {
                    return BadRequest("Agent response is empty or invalid.");
                }
                var agentInfo = JsonSerializer.Deserialize<Dictionary<string, string>>(agentResponse.Message);
                if (agentInfo == null || !agentInfo.ContainsKey("agentName") || !agentInfo.ContainsKey("modelName"))
                {
                    return BadRequest("Agent response does not contain required information.");
                }
                agentName = agentInfo["agentName"];
                modelName = agentInfo["modelName"];
            }

            var existingChat = await _chatRepository.GetChatMessageAsync(request.ChatMessageId, userEmail);
            if (existingChat == null)
            {
                return NotFound("Chat not found or access denied");
            }

            var history = await _chatRepository.GetChatHistoryAsync(request.ChatMessageId, userEmail);
            var response = _aiService.GetResponse(request.NewMessage, agentName, modelName, request.Workspace, history.ToList());
            var fullResponse = new StringBuilder();
            await foreach (var message in response)
            {
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                fullResponse.Append(message.Message);
            }
            string fullResponseString = fullResponse.ToString();
            var editedHistory = await _chatRepository.EditChatHistoryAsync(
                request.ChatMessageId,
                request.ChatHistoryId,
                request.NewMessage,
                fullResponseString
            );

            return Ok(_mapper.Map<ChatHistoryDto>(editedHistory));
        }

        [HttpPost("regenerate")]
        public async Task<ActionResult<ChatResponseDto>> RegenerateResponse([FromBody] RegenerateResponseRequestDto request)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();

            // Hardcoded values for now
            string agentName = "Developer";
            string modelName = "gpt-4o-mini";

            // Temporarily bypassing CallAgent and hardcoding agentName and modelName
            if (false) // Invalid condition to ensure this block is never executed
            {
                string message = $"Workspace: {request.Workspace}\n Old question and response: {request.OldMessage} \n {request.OldResponse}";
                var agentResponse = await _aiService.CallAgent("WorkspaceAgentChooser", "Generate Another Response \n " + message).FirstOrDefaultAsync();
                if (agentResponse == null || string.IsNullOrEmpty(agentResponse.Message))
                {
                    return BadRequest("Agent response is empty or invalid.");
                }
                var agentInfo = JsonSerializer.Deserialize<Dictionary<string, string>>(agentResponse.Message);
                if (agentInfo == null || !agentInfo.ContainsKey("agentName") || !agentInfo.ContainsKey("modelName"))
                {
                    return BadRequest("Agent response does not contain required information.");
                }
                agentName = agentInfo["agentName"];
                modelName = agentInfo["modelName"];
            }

            var existingChat = await _chatRepository.GetChatMessageAsync(request.ChatMessageId, userEmail);
            if (existingChat == null)
            {
                return NotFound("Chat not found or access denied");
            }

            var history = await _chatRepository.GetChatHistoryAsync(request.ChatMessageId, userEmail);
            var originalMessage = history.FirstOrDefault(h => h.Id == request.ChatHistoryId);
            if (originalMessage == null)
            {
                return NotFound("Chat history not found");
            }

            var response = _aiService.GetResponse("Generate Another Response \n" + originalMessage.Message, agentName, modelName, request.Workspace, history.ToList());
            var fullResponse = new StringBuilder();
            await foreach (var message in response)
            {
                await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                fullResponse.Append(message.Message);
            }
            string fullResponseString = fullResponse.ToString();

            var chatResponse = await _chatRepository.RegenerateResponseAsync(request.ChatHistoryId, fullResponseString);

            return Ok(_mapper.Map<ChatResponseDto>(chatResponse));
        }

        [HttpGet("history/{chatMessageId}")]
        public async Task<ActionResult<ChatHistoryResponseDto>> GetChatHistory(Guid chatMessageId)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();

            var chatMessage = await _chatRepository.GetChatMessageAsync(chatMessageId, userEmail);
            if (chatMessage == null)
            {
                return NotFound("Chat not found or access denied");
            }

            var history = await _chatRepository.GetChatHistoryAsync(chatMessageId, userEmail);

            var result = new ChatHistoryResponseDto
            {
                Id = chatMessage.Id,
                Title = chatMessage.Title,
                CreatedDate = chatMessage.CreatedDate,
                History = _mapper.Map<List<ChatHistoryDto>>(history)
            };

            return Ok(result);
        }

        [HttpGet("list")]
        public async Task<ActionResult<ChatListResponseDto>> GetUserChats(string workspace = "", [FromQuery] int pageNumber = 1, int pageSize = 10)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var (messages, totalCount) = await _chatRepository.GetUserChatMessagesAsync(workspace, userEmail, pageSize, pageNumber);

            // Calculate if there are more messages
            var hasMoreMessages = (pageNumber * pageSize) < totalCount;

            var response = new ChatListResponseDto
            {
                Messages = messages.ToList(),
                HasMoreMessages = hasMoreMessages
            };

            return Ok(response);
        }

        [HttpPost("pin/{chatMessageId}")]
        public async Task<ActionResult<ChatMessageDto>> PinChat(Guid chatMessageId, [FromQuery] bool isPinned)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chatMessage = await _chatRepository.PinChatAsync(chatMessageId, userEmail, isPinned);

            if (chatMessage == null)
                return NotFound("Chat not found or access denied");

            return Ok(_mapper.Map<ChatMessageDto>(chatMessage));
        }

        [HttpPost("favorite/{chatMessageId}")]
        public async Task<ActionResult<ChatMessageDto>> FavoriteChat(Guid chatMessageId, [FromQuery] bool isFavorite)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chatMessage = await _chatRepository.FavoriteChatAsync(chatMessageId, userEmail, isFavorite);

            if (chatMessage == null)
                return NotFound("Chat not found or access denied");

            return Ok(_mapper.Map<ChatMessageDto>(chatMessage));
        }

        [HttpPost("archive/{chatMessageId}")]
        public async Task<ActionResult<ChatMessageDto>> ArchiveChat(Guid chatMessageId, [FromQuery] bool isArchived)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chatMessage = await _chatRepository.ArchiveChatAsync(chatMessageId, userEmail, isArchived);

            if (chatMessage == null)
                return NotFound("Chat not found or access denied");

            return Ok(_mapper.Map<ChatMessageDto>(chatMessage));
        }

        [HttpPost("status")]
        public async Task<ActionResult<ChatMessageDto>> UpdateChatStatus([FromBody] UpdateChatStatusRequestDto request)
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chatMessage = await _chatRepository.UpdateChatStatusAsync(
                request.ChatMessageId,
                userEmail,
                request.IsPinned,
                request.IsFavorite,
                request.IsArchived
            );

            if (chatMessage == null)
                return NotFound("Chat not found or access denied");

            return Ok(_mapper.Map<ChatMessageDto>(chatMessage));
        }

        [HttpGet("pinned")]
        public async Task<ActionResult<List<ChatMessageDto>>> GetPinnedChats(string workspace = "")
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chats = await _chatRepository.GetPinnedChatsAsync(workspace, userEmail);
            return Ok(_mapper.Map<List<ChatMessageDto>>(chats));
        }

        [HttpGet("favorites")]
        public async Task<ActionResult<List<ChatMessageDto>>> GetFavoriteChats(string workspace = "")
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chats = await _chatRepository.GetFavoriteChatsAsync(workspace, userEmail);
            return Ok(_mapper.Map<List<ChatMessageDto>>(chats));
        }

        [HttpGet("archived")]
        public async Task<ActionResult<List<ChatMessageDto>>> GetArchivedChats(string workspace = "")
        {
            var userEmail = _extractEmailFromAccessor.GetEmail();
            var chats = await _chatRepository.GetArchivedChatsAsync(workspace, userEmail);
            return Ok(_mapper.Map<List<ChatMessageDto>>(chats));
        }

        [HttpPost("save-blog")]
        public async Task<ActionResult<ResponseMessage>> SaveBlog([FromBody] SaveBlogRequestDto request)
        {
            try
            {
                
                // Prepare the content to send with both title and content
                var jsonContent = new StringContent(
                    JsonSerializer.Serialize(new {
                        title = request.Title,
                        content = request.Content
                    }),
                    Encoding.UTF8,
                    "application/json"
                );

                // Create HttpClient and send POST request to the specified URL
                using var httpClient = _httpClientFactory.CreateClient();
                var response = await httpClient.PostAsync(request.Url, jsonContent);

                // Return response based on HTTP status
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return Ok(new ResponseMessage
                    {
                        IsError = false,
                        Message = "Blog saved successfully"
                    });
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return Ok(new ResponseMessage
                    {
                        IsError = true,
                        Message = $"Failed to save blog. Server responded with: {response.StatusCode} - {errorContent}"
                    });
                }
            }
            catch (Exception ex)
            {
                return Ok(new ResponseMessage
                {
                    IsError = true,
                    Message = ex.Message
                });
            }
        }
    }
}