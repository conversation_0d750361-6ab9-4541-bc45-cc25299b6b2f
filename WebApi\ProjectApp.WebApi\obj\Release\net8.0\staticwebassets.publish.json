{"Version": 1, "Hash": "XM+cKmLwQmPICjF3WarQ2h4cYLMIZ/Eb9rIuQPbFbWk=", "Source": "ProjectApp.WebApi", "BasePath": "_content/ProjectApp.WebApi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ProjectApp.WebApi\\wwwroot", "Source": "ProjectApp.WebApi", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugin-details#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3az2yg0702", "Integrity": "G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugin-details.html"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "plugins#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "367d1cpt86", "Integrity": "dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\plugins.html"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpeg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image.jpeg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpeg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpeg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_1.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_2.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_3.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/image_4#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "536fkm048a", "Integrity": "b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\image_4.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_3.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Invoice-F66D817D-0001_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bcvf9xuk76", "Integrity": "a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/landingPage#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "929y9semqo", "Integrity": "YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\landingPage.png"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/maxresdefault_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x27hb5tlzi", "Integrity": "GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\maxresdefault_1.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_1.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_2.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_3#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_3.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_4.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_5.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_6.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/Receipt-2252-1522_7#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "z0f7gfgpqz", "Integrity": "0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Receipt-2252-1522_7.pdf"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/robot leg_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hk1hg0l2za", "Integrity": "MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\robot leg_1.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_1.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_2.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_3.jpg", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "uploads/vibe_3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4oku5zbii3", "Integrity": "JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\vibe_3.jpg"}, {"Identity": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\wwwroot.zip", "SourceId": "ProjectApp.WebApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\", "BasePath": "_content/ProjectApp.WebApi", "RelativePath": "wwwroot#[.{fingerprint}]?.zip", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vp75h618bc", "Integrity": "rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\wwwroot.zip"}], "Endpoints": [{"Route": "plugin-details.3az2yg0702.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3az2yg0702"}, {"Name": "label", "Value": "plugin-details.html"}, {"Name": "integrity", "Value": "sha256-G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I="}]}, {"Route": "plugin-details.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugin-details.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9832"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G4EPNtbn7rEifJUNLbwijif+dhy9YnlvjPeurW3sv1I="}]}, {"Route": "plugins.367d1cpt86.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23350"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "367d1cpt86"}, {"Name": "label", "Value": "plugins.html"}, {"Name": "integrity", "Value": "sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="}]}, {"Route": "plugins.html", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\plugins.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23350"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Apr 2025 03:14:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="}]}, {"Route": "uploads/image.536fkm048a.jpeg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:44:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image.jpeg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image.jpeg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:44:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.536fkm048a.jpeg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:48:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_1.jpeg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_1.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.jpeg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpeg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:48:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 31 Mar 2025 11:23:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_2.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:30:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_2.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_2.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:30:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_3.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:38:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_3.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Tue, 01 Apr 2025 03:38:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_4.536fkm048a.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:43:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "536fkm048a"}, {"Name": "label", "Value": "uploads/image_4.jpg"}, {"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/image_4.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\image_4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "56443"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:43:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5+Or1ZcrKyJJw="}]}, {"Route": "uploads/Invoice-F66D817D-0001.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:28:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:28:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_1.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 09:34:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_1.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 09:34:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_2.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 01:54:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_2.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_2.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 01:54:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_3.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 01:58:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_3.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_3.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 01:58:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_4.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:02:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_4.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_4.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:02:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_5.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:49:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_5.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_5.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:49:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_6.bcvf9xuk76.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:51:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bcvf9xuk76"}, {"Name": "label", "Value": "uploads/Invoice-F66D817D-0001_6.pdf"}, {"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/Invoice-F66D817D-0001_6.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Invoice-F66D817D-0001_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20565"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:51:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a/mgsnghWt9LAe46R91UeSUs+/XykO12aE9iTqU+6GQ="}]}, {"Route": "uploads/landingPage.929y9semqo.png", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292001"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 19:56:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "929y9semqo"}, {"Name": "label", "Value": "uploads/landingPage.png"}, {"Name": "integrity", "Value": "sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo="}]}, {"Route": "uploads/landingPage.png", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\landingPage.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "292001"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo=\""}, {"Name": "Last-Modified", "Value": "Mon, 24 Mar 2025 19:56:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb+ezIcRfndo="}]}, {"Route": "uploads/maxresdefault.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:53:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/maxresdefault.x27hb5tlzi.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:53:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x27hb5tlzi"}, {"Name": "label", "Value": "uploads/maxresdefault.jpg"}, {"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/maxresdefault_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:54:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/maxresdefault_1.x27hb5tlzi.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\maxresdefault_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "99558"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:54:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x27hb5tlzi"}, {"Name": "label", "Value": "uploads/maxresdefault_1.jpg"}, {"Name": "integrity", "Value": "sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]}, {"Route": "uploads/Receipt.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:15:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:15:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 02:30:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 02:30:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_1.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:20:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_1.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:20:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_1.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_2.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:23:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_2.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:23:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_2.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_3.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:26:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_3.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_3.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 04:26:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_3.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_4.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 06:57:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_4.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Fri, 16 May 2025 06:57:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_4.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_5.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 02:39:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_5.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 02:39:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_5.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_6.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:35:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_6.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 03:35:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_6.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_7.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:09:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/Receipt-2252-1522_7.z0f7gfgpqz.pdf", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\Receipt-2252-1522_7.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20386"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 05:09:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "z0f7gfgpqz"}, {"Name": "label", "Value": "uploads/Receipt-2252-1522_7.pdf"}, {"Name": "integrity", "Value": "sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]}, {"Route": "uploads/robot leg.hk1hg0l2za.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:14:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hk1hg0l2za"}, {"Name": "label", "Value": "uploads/robot leg.jpg"}, {"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/robot leg.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:14:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/robot leg_1.hk1hg0l2za.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:18:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hk1hg0l2za"}, {"Name": "label", "Value": "uploads/robot leg_1.jpg"}, {"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/robot leg_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\robot leg_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47187"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Apr 2025 01:18:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MQLa3BFD6qIyzZL+vslinGQv2tjcO9UYI/wsQ1G48rI="}]}, {"Route": "uploads/vibe.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:30:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:30:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_1.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:44:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe_1.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_1.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:44:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_2.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:46:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe_2.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_2.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Thu, 01 May 2025 12:46:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_3.4oku5zbii3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:50:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4oku5zbii3"}, {"Name": "label", "Value": "uploads/vibe_3.jpg"}, {"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "uploads/vibe_3.jpg", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\uploads\\vibe_3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25704"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 09:50:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]}, {"Route": "wwwroot.vp75h618bc.zip", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\wwwroot.zip", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "application/x-zip-compressed"}, {"Name": "ETag", "Value": "\"rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Apr 2025 06:19:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vp75h618bc"}, {"Name": "label", "Value": "wwwroot.zip"}, {"Name": "integrity", "Value": "sha256-rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM="}]}, {"Route": "wwwroot.zip", "AssetFile": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\wwwroot\\wwwroot.zip", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "application/x-zip-compressed"}, {"Name": "ETag", "Value": "\"rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Apr 2025 06:19:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rbC8AzXPABigYHtfyNDM8/v4H945mcVPXRs/5qLR0yM="}]}]}