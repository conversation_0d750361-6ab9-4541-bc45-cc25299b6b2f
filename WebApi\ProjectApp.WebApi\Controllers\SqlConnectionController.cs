using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Threading.Tasks;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SqlConnectionController : ControllerBase
    {
        private readonly ISqlConnectionRepository _sqlConnectionRepository;

        public SqlConnectionController(ISqlConnectionRepository sqlConnectionRepository)
        {
            _sqlConnectionRepository = sqlConnectionRepository;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<SqlConnectionInfoList>> GetAll()
        {
            var connections = await _sqlConnectionRepository.GetAllConnectionsAsync();
            return Ok(connections);
        }

        [HttpPost("TestConnection")]
        public async Task<ActionResult<ResponseMessage>> TestConnection([FromBody] SqlConnectionInfo connectionInfo)
        {
            var result = await _sqlConnectionRepository.TestConnectionAsync(connectionInfo.ConnectionString);
            return Ok(result);
        }

        [HttpPost("Execute")]
        public async Task<ActionResult<SqlQueryResponse>> Execute([FromBody] SqlQueryRequest queryRequest)
        {
            var result = await _sqlConnectionRepository.ExecuteSqlQueryAsync(queryRequest);
            return Ok(result);
        }
    }
}
