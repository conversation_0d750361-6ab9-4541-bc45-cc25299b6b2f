using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class EmbeddingConfigRepository : IEmbeddingConfigRepository
    {
        private readonly IDbConnection _dbConnection;

        public EmbeddingConfigRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<EmbeddingConfiguration> GetActiveEmbeddingConfigAsync()
        {
            var sql = "SELECT * FROM EmbeddingConfigurations WHERE IsActive = 1";
            return await _dbConnection.QueryFirstOrDefaultAsync<EmbeddingConfiguration>(sql);
        }

        public async Task<EmbeddingConfiguration> GetEmbeddingConfigByIdAsync(int id)
        {
            var sql = "SELECT * FROM EmbeddingConfigurations WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<EmbeddingConfiguration>(sql, new { Id = id });
        }

        public async Task<List<EmbeddingConfiguration>> GetAllEmbeddingConfigsAsync()
        {
            var sql = "SELECT * FROM EmbeddingConfigurations ORDER BY CreatedDate DESC";
            var result = await _dbConnection.QueryAsync<EmbeddingConfiguration>(sql);
            return result.ToList();
        }

        public async Task<int> SaveEmbeddingConfigAsync(EmbeddingConfiguration config)
        {
            if (config.Id == 0)
            {
                var sqlInsert = @"
                    INSERT INTO EmbeddingConfigurations (ModelId, Provider, ApiKey, IsActive, CreatedDate)
                    VALUES (@ModelId, @Provider, @ApiKey, @IsActive, @CreatedDate);
                    SELECT CAST(SCOPE_IDENTITY() as int)";

                config.CreatedDate = DateTime.UtcNow;
                return await _dbConnection.QuerySingleAsync<int>(sqlInsert, config);
            }
            else
            {
                var sqlUpdate = @"
                    UPDATE EmbeddingConfigurations
                    SET ModelId = @ModelId,
                        Provider = @Provider,
                        ApiKey = @ApiKey,
                        IsActive = @IsActive,
                        ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                config.ModifiedDate = DateTime.UtcNow;
                var affectedRows = await _dbConnection.ExecuteAsync(sqlUpdate, config);
                return affectedRows > 0 ? config.Id : 0;
            }
        }

        public async Task<bool> SetEmbeddingConfigActiveAsync(int id)
        {
            // Ensure the connection is open before starting a transaction
            if (_dbConnection.State != ConnectionState.Open)
            {
                _dbConnection.Open();
            }

            using (var transaction = _dbConnection.BeginTransaction())
            {
                try
                {
                    // First, set all configs to inactive
                    await _dbConnection.ExecuteAsync(
                        "UPDATE EmbeddingConfigurations SET IsActive = 0, ModifiedDate = @ModifiedDate",
                        new { ModifiedDate = DateTime.UtcNow },
                        transaction);

                    // Then set the specified one to active
                    var result = await _dbConnection.ExecuteAsync(
                        "UPDATE EmbeddingConfigurations SET IsActive = 1, ModifiedDate = @ModifiedDate WHERE Id = @Id",
                        new { Id = id, ModifiedDate = DateTime.UtcNow },
                        transaction);

                    transaction.Commit();
                    return result > 0;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
        }

        public async Task<bool> DeleteEmbeddingConfigAsync(int id)
        {
            var sql = "DELETE FROM EmbeddingConfigurations WHERE Id = @Id";
            var affectedRows = await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }
    }
} 