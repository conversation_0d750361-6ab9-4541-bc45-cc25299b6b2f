﻿using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IProjectCategoryRepository
    {
        Task<List<ProjectCategory>> GetAllAsync();
        Task<ProjectCategory> GetByIdAsync(int id);
        Task<ProjectCategory> CreateOrUpdateAsync(ProjectCategory projectCategory);
        Task<ProjectCategory> DeleteAsync(int id);
        Task<ProjectCategory> GetByNameAsync(string name);
        Task<List<ProjectCategory>> GetAllByWorkspaceIdAsync(int id);
    }
}
