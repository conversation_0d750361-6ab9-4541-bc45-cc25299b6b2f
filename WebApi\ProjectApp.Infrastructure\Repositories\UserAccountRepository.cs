﻿using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class UserAccountRepository : IUserAccountRepository
    {
        private readonly IDbConnection _dbConnection;

        public UserAccountRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<UserDto>> GetAllUsers()
        {
            var sql = @"SELECT Name, Email, Role, Skills 
                        FROM UserAccounts 
                        WHERE LOWER(Role) LIKE '%user%'";

            var result = await _dbConnection.QueryAsync<RegisterDto>(sql);
            return result.Select(user => new UserDto
            {
                Name = user.Name,
                Email = user.Email,
                Roles = user.Role.Split(", ").ToList(),
                Skills = user.Skills
            }).ToList();
        }

        public async Task<List<UserDto>> GetAll()
        {
            var sql = @"SELECT Name, Email, Role, Skills 
                            FROM UserAccounts";

            var result = await _dbConnection.QueryAsync<RegisterDto>(sql);
            return result.Select(user => new UserDto
            {
                Name = user.Name,
                Email = user.Email,
                Roles = user.Role.Split(", ").ToList(),
                Skills = user.Skills
            }).ToList();
        }

        public async Task<UserDto> IsUser(string email)
        {
            var sql = "SELECT Name, Email, Role, Skills FROM UserAccounts WHERE Email = @Email";
            var parameters = new { Email = email };
            var user = await _dbConnection.QueryFirstOrDefaultAsync<RegisterDto>(sql, parameters);

            if (user == null)
            {
                throw new Exception("User not found");
            }

            return new UserDto
            {
                Name = user.Name,
                Email = user.Email,
                Roles = user.Role.Split(", ").ToList(),
                Skills = user.Skills
            };
        }

        public async Task Register(RegisterDto model)
        {
            var existingUser = await _dbConnection.QueryFirstOrDefaultAsync<UserAccount>(
                "SELECT * FROM UserAccounts WHERE Email = @Email",
                new { Email = model.Email }
            );

            if (existingUser != null)
            {
                throw new Exception("Email already exists");
            }

            var hashedPassword = BCrypt.Net.BCrypt.HashPassword(model.Password);

            var sql = @"INSERT INTO UserAccounts (Name, Email, Password, Role, Skills) 
                            VALUES (@Name, @Email, @Password, @Role, @Skills)";

            await _dbConnection.ExecuteScalarAsync<int>(sql, new
            {
                model.Name,
                model.Email,
                Password = hashedPassword,
                model.Role,
                model.Skills
            });
        }

        public async Task<bool> GenerateOtp(string email)
        {
            var otp = new Random().Next(100000, 999999).ToString();
            var expiry = DateTime.UtcNow.AddMinutes(15);

            var sql = @"UPDATE UserAccounts 
                                SET OtpCode = @Otp, OtpExpiry = @Expiry 
                                WHERE Email = @Email";

            var result = await _dbConnection.ExecuteAsync(sql, new
            {
                Otp = otp,
                Expiry = expiry,
                Email = email
            });

            if (result > 0)
            {
                // Send email with OTP
                // TODO: Implement email sending

                return true;
            }
            return false;
        }

        public async Task<bool> VerifyOtp(string email, string otp)
        {
            var sql = @"UPDATE UserAccounts 
                                SET OtpCode = NULL, OtpExpiry = NULL 
                                WHERE Email = @Email 
                                AND OtpCode = @Otp 
                                AND OtpExpiry > @Now";

            var result = await _dbConnection.ExecuteAsync(sql, new
            {
                Email = email,
                Otp = otp,
                Now = DateTime.UtcNow
            });

            return result > 0;
        }

        public async Task<UserDto> Login(string email, string password)
        {
            var user = await _dbConnection.QueryFirstOrDefaultAsync<UserAccount>(
                "SELECT * FROM UserAccounts WHERE Email = @Email",
                new { Email = email }
            );

            if (user != null && BCrypt.Net.BCrypt.Verify(password, user.Password))
            {
                return new UserDto
                {
                    Name = user.Name,
                    Email = user.Email,
                    Roles = user.Role?.Split(',').Select(r => r.Trim()).ToList(),
                    Skills = user.Skills
                };
            }

            return null;
        }
        public async Task DeleteUser(string email)
        {
            var sql = "DELETE FROM UserAccounts WHERE Email = @Email";
            var result = await _dbConnection.ExecuteAsync(sql, new { Email = email });

            if (result == 0)
            {
                throw new Exception("User not found");
            }
        }

        public async Task AssignRole(string email, string role)
        {
            var user = await _dbConnection.QueryFirstOrDefaultAsync<UserAccount>(
                "SELECT * FROM UserAccounts WHERE Email = @Email",
                new { Email = email }
            );

            if (user == null)
                throw new Exception("User not found");

            var currentRoles = user.Role?.Split(',').Select(r => r.Trim()).ToList() ?? new List<string>();

            if (currentRoles.Contains(role))
                throw new Exception("User already has this role");

            currentRoles.Add(role);
            var newRoles = string.Join(", ", currentRoles);

            await _dbConnection.ExecuteAsync(
                "UPDATE UserAccounts SET Role = @Role WHERE Email = @Email",
                new { Email = email, Role = newRoles }
            );
        }

        public async Task RemoveRole(string email, string role)
        {
            var user = await _dbConnection.QueryFirstOrDefaultAsync<UserAccount>(
                "SELECT * FROM UserAccounts WHERE Email = @Email",
                new { Email = email }
            );

            if (user == null)
                throw new Exception("User not found");

            var currentRoles = user.Role?.Split(',').Select(r => r.Trim()).ToList() ?? new List<string>();

            if (!currentRoles.Contains(role))
                throw new Exception("User doesn't have this role");

            if (currentRoles.Count <= 1)
                throw new Exception("Cannot remove the last role");

            currentRoles.Remove(role);
            var newRoles = string.Join(", ", currentRoles);

            await _dbConnection.ExecuteAsync(
                "UPDATE UserAccounts SET Role = @Role WHERE Email = @Email",
                new { Email = email, Role = newRoles }
            );
        }
    }
}
