﻿using Dapper;
using Microsoft.KernelMemory;
using Microsoft.SemanticKernel;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class KernelMemoryPlugin
    {
        private readonly IKernelMemory _memory;
        private readonly IDbConnection _dbConnection;

        public KernelMemoryPlugin(IKernelMemory memory, IDbConnection dbConnection)
        {
            _memory = memory;
            _dbConnection = dbConnection;
        }

        [KernelFunction("search_memory")]
        [Description("Searches  memory with associated information and returns document IDs")]
        public async Task<List<char>> SearchMemory(string searchQuery, string index, double minRelevance = 0.4, List<MemoryTag> tags = null)
        {
            var filter = new MemoryFilter();
            if (tags != null)
            {
                foreach (var memoryTag in tags)
                {
                    filter.ByTag(memoryTag.Name, memoryTag.Value);
                }
            }

            var memoryAnswer = await _memory.SearchAsync(searchQuery, minRelevance: minRelevance, index: index, filter: filter);
            var documentIds = memoryAnswer.Results.SelectMany(result => result.DocumentId).ToList();
            return documentIds;
        }

        

        
    }
}
