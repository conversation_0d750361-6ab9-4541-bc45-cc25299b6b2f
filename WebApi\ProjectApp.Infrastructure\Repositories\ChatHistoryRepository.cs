using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;

namespace WebApi.Infrastructure.Repositories
{
    public class ChatHistoryRepository : IChatHistoryRepository
    {
        private readonly IDbConnection _dbConnection;

        public ChatHistoryRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<ChatHistoryModel>> GetChatHistoryBySessionId(string sessionId)
        {
            const string sql = "SELECT * FROM ChatHistory WHERE SessionId = @SessionId ORDER BY Timestamp";
            return (await _dbConnection.QueryAsync<ChatHistoryModel>(sql, new { SessionId = sessionId })).ToList();
        }

        public async Task SaveChatHistory(ChatHistoryModel chatHistory)
        {
            const string sql = @"INSERT INTO ChatHistory (SessionId, Question, Answer, Timestamp) 
                                VALUES (@SessionId, @Question, @Answer, @Timestamp)";
            await _dbConnection.ExecuteAsync(sql, chatHistory);
        }
    }
} 