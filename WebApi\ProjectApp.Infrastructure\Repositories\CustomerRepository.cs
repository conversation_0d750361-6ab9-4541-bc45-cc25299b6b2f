using Dapper;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class CustomerRepository : ICustomerRepository
    {
        private readonly IDbConnection _dbConnection;

        public CustomerRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<Customer>> GetAllAsync()
        {
            var sql = "SELECT * FROM Customers ORDER BY CustomerName";
            var result = await _dbConnection.QueryAsync<Customer>(sql);
            return result.ToList();
        }

        public async Task<Customer> GetByIdAsync(int id)
        {
            var sql = "SELECT * FROM Customers WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<Customer>(sql, new { Id = id });
        }

        public async Task<Customer> GetByEmailAsync(string email)
        {
            var sql = "SELECT * FROM Customers WHERE Email = @Email";
            return await _dbConnection.QueryFirstOrDefaultAsync<Customer>(sql, new { Email = email });
        }

        public async Task<Customer> CreateOrUpdateAsync(Customer customer)
        {
            if (customer.Id == 0)
            {
                // Create new customer
                var existingCustomer = await GetByEmailAsync(customer.Email);
                if (existingCustomer != null)
                {
                    throw new Exception("Customer with this email already exists");
                }

                customer.CreatedAt = DateTime.UtcNow;

                var insertSql = @"INSERT INTO Customers (CustomerName, Email, Address, CreatedAt) 
                                  OUTPUT INSERTED.Id 
                                  VALUES (@CustomerName, @Email, @Address, @CreatedAt)";

                customer.Id = await _dbConnection.ExecuteScalarAsync<int>(insertSql, customer);
                return customer;
            }
            else
            {
                // Update existing customer
                var existingCustomer = await GetByIdAsync(customer.Id);
                if (existingCustomer == null)
                {
                    throw new Exception("Customer not found");
                }

                // Check if email already exists for another customer
                var emailOwner = await GetByEmailAsync(customer.Email);
                if (emailOwner != null && emailOwner.Id != customer.Id)
                {
                    throw new Exception("Email already in use by another customer");
                }

                customer.UpdatedAt = DateTime.UtcNow;
                customer.CreatedAt = existingCustomer.CreatedAt;  // Preserve original creation date

                var updateSql = @"UPDATE Customers 
                                  SET CustomerName = @CustomerName, 
                                      Email = @Email, 
                                      Address = @Address, 
                                      UpdatedAt = @UpdatedAt 
                                  WHERE Id = @Id";

                await _dbConnection.ExecuteAsync(updateSql, customer);
                return customer;
            }
        }

        public async Task<Customer> CreateAsync(Customer customer)
        {
            // Check if customer with same email already exists
            var existingCustomer = await GetByEmailAsync(customer.Email);
            if (existingCustomer != null)
            {
                throw new Exception("Customer with this email already exists");
            }

            customer.CreatedAt = DateTime.UtcNow;

            var sql = @"INSERT INTO Customers (CustomerName, Email, Address, CreatedAt) 
                        OUTPUT INSERTED.Id 
                        VALUES (@CustomerName, @Email, @Address, @CreatedAt)";

            customer.Id = await _dbConnection.ExecuteScalarAsync<int>(sql, customer);
            return customer;
        }

        public async Task<Customer> UpdateAsync(Customer customer)
        {
            // Check if customer exists
            var existingCustomer = await GetByIdAsync(customer.Id);
            if (existingCustomer == null)
            {
                throw new Exception("Customer not found");
            }

            // Check if email already exists for another customer
            var emailOwner = await GetByEmailAsync(customer.Email);
            if (emailOwner != null && emailOwner.Id != customer.Id)
            {
                throw new Exception("Email already in use by another customer");
            }

            customer.UpdatedAt = DateTime.UtcNow;
            customer.CreatedAt = existingCustomer.CreatedAt;  // Preserve original creation date

            var sql = @"UPDATE Customers 
                        SET CustomerName = @CustomerName, 
                            Email = @Email, 
                            Address = @Address, 
                            UpdatedAt = @UpdatedAt 
                        WHERE Id = @Id";

            await _dbConnection.ExecuteAsync(sql, customer);
            return customer;
        }

        public async Task<Customer> DeleteAsync(int id)
        {
            var customer = await GetByIdAsync(id);
            if (customer == null)
            {
                throw new Exception("Customer not found");
            }

            var sql = "DELETE FROM Customers WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return customer;
        }
    }
}
