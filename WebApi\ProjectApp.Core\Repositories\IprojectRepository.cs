﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IProjectRepository
    {
        Task<List<ProjectViewDto>> GetAll();
        Task<List<ProjectViewDto>> GetAllForUser();
        Task<ProjectViewDto> GetById(int id);

        Task<Project> CreateOrUpdate(ProjectDto request, string assigneeEmail = null);
        Task<Project> newProjectCreate(NewProjectDto request);


        Task<Project> Delete(int id);
        Task<ResponseMessage> ChangeStatus(string status, int id);
        Task<List<ProjectViewDto>> GetAllUserByWorkspaceId(int workspaceId);
        Task<List<ProjectViewDto>> GetPastDueProjects(int workspaceId);
        Task<List<ProjectViewDto>> GetForWorkspace(int workspaceId);
        Task<List<ProjectViewDto>> GetAllByCategoryName(string categoryName);
    }
}
