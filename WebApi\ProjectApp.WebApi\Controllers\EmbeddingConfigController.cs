//using System;
//using System.Collections.Generic;
//using System.Threading.Tasks;
//using Microsoft.AspNetCore.Authorization;
//using Microsoft.AspNetCore.Http;
//using Microsoft.AspNetCore.Mvc;
//using ProjectApp.Core.Dtos;
//using ProjectApp.Core.Models;
//using ProjectApp.Core.Repositories;

//namespace ProjectApp.WebApi.Controllers
//{
//    [Route("api/[controller]")]
//    [ApiController]
//    [Authorize]
//    public class EmbeddingConfigController : ControllerBase
//    {
//        private readonly IEmbeddingConfigRepository _repository;
//        private readonly EmbeddingConfigurationProvider _configProvider;

//        public EmbeddingConfigController(
//            IEmbeddingConfigRepository repository, 
//            EmbeddingConfigurationProvider configProvider)
//        {
//            _repository = repository;
//            _configProvider = configProvider;
//        }

//        [HttpGet("GetAllConfigurations")]
//        [Authorize(Roles = "Admin")]
//        public async Task<ActionResult<List<EmbeddingConfiguration>>> GetAllConfigurations()
//        {
//            var configs = await _repository.GetAllEmbeddingConfigsAsync();
//            return Ok(configs);
//        }


//        [HttpGet("current")]
//        [Authorize(Roles = "Admin")]
//        public ActionResult<EmbeddingConfigurationProvider> GetCurrentlyLoadedConfiguration()
//        {
//            var activeConfig = new EmbeddingConfigurationProvider
//            {
//                EmbeddingModelId = _configProvider.EmbeddingModelId,
//                ApiKey = _configProvider.ApiKey
//            };
//            if (string.IsNullOrEmpty(activeConfig.EmbeddingModelId) || string.IsNullOrEmpty(activeConfig.ApiKey))
//            {
//                return NotFound(new { message = "No active embedding configuration found" });
//            }
//            return Ok(activeConfig); // Return the currently loaded configuration
//        }

//        [HttpGet("GetEmbeddingConfig/{id}")]
//        [Authorize(Roles = "Admin")]
//        public async Task<ActionResult<EmbeddingConfiguration>> GetEmbeddingConfig(int id)
//        {
//            var config = await _repository.GetEmbeddingConfigByIdAsync(id);
//            if (config == null)
//                return NotFound(new { message = $"Configuration with ID {id} not found" });

//            return Ok(config);
//        }

//        [HttpPost("CreateEmbeddingConfig")]
//        [Authorize(Roles = "Admin")]
//        public async Task<ActionResult<EmbeddingConfiguration>> CreateEmbeddingConfig(EmbeddingConfiguration config)
//        {
//            try
//            {
//                int id = await _repository.SaveEmbeddingConfigAsync(config);
//                config.Id = id;

//                return Ok(config);
//            }
//            catch (Exception ex)
//            {
//                return StatusCode(StatusCodes.Status500InternalServerError,
//                    new { message = "Error creating embedding configuration", error = ex.Message });
//            }
//        }

//        [HttpPut("SetActiveEmbeddingConfig/{id}")]
//        [Authorize(Roles = "Admin")]
//        public async Task<ActionResult<ResponseMessage>> SetActiveEmbeddingConfig(int id)
//        {
//            try
//            {
//                // Check if the configuration exists first
//                var config = await _repository.GetEmbeddingConfigByIdAsync(id);
//                if (config == null)
//                    return NotFound(new { message = $"Configuration with ID {id} not found" });

//                bool success = await _repository.SetEmbeddingConfigActiveAsync(id);
                
//                return Ok(new ResponseMessage
//                {
//                    IsError = !success,
//                    Message = success ?
//                        $"Configuration with ID {id} set as active. Please Restart the project" :
//                        $"Failed to set configuration with ID {id} as active. Please try again."
//                });
//            }
//            catch (Exception ex)
//            {
//                return StatusCode(StatusCodes.Status500InternalServerError, 
//                    new { message = "Error activating embedding configuration", error = ex.Message });
//            }
//        }

//        [HttpDelete("DeleteEmbeddingConfig/{id}")]
//        [Authorize(Roles = "Admin")]
//        public async Task<ActionResult<ResponseMessage>> DeleteEmbeddingConfig(int id)
//        {
//            try
//            {
//                // First, check if the configuration exists
//                var config = await _repository.GetEmbeddingConfigByIdAsync(id);
//                if (config == null)
//                    return NotFound(new ResponseMessage{ IsError = true, Message = $"Configuration with ID {id} not found" });
            
//                // Check if it's the active configuration
//                if (config.IsActive)
//                    return BadRequest(new ResponseMessage{ 
//                        IsError = true,
//                        Message = "Cannot delete the active embedding configuration. Please activate another configuration first." 
//                    });
            
//                // Delete the configuration
//                bool success = await _repository.DeleteEmbeddingConfigAsync(id);
//                if (!success)
//                    return StatusCode(StatusCodes.Status500InternalServerError, 
//                        new ResponseMessage { IsError = true, Message = "Failed to delete the embedding configuration" });
                
//                return Ok(new ResponseMessage { IsError = true, Message = $"Configuration with ID {id} has been deleted successfully" });
//            }
//            catch (Exception ex)
//            {
//                return StatusCode(StatusCodes.Status500InternalServerError,
//                    new ResponseMessage { IsError = true, Message = "Error deleting embedding configuration" });
//            }
//        }
//    }
//} 