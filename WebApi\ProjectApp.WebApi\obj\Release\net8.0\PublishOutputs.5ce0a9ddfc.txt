C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.WebApi.exe
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\appsettings.Development.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\appsettings.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\libman.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.WebApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.WebApi.deps.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.WebApi.runtimeconfig.json
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.WebApi.pdb
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\AutoMapper.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\AWSSDK.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\AWSSDK.S3.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.AI.ContentSafety.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.AI.FormRecognizer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.AI.OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.Identity.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.Search.Documents.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.Storage.Blobs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.Storage.Common.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Azure.Storage.Queues.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\BCrypt.Net-Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ClosedXML.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ClosedXML.Parser.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\CommunityToolkit.HighPerformance.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Dapper.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\DnsClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\DocumentFormat.OpenXml.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\DocumentFormat.OpenXml.Framework.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Elastic.Clients.Elasticsearch.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Elastic.Transport.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ExcelNumberFormat.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\FluentValidation.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\FluentValidation.AspNetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Google.Protobuf.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Hangfire.AspNetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Hangfire.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Hangfire.NetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Hangfire.SqlServer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\HtmlAgilityPack.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\JetBrains.Annotations.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Json.More.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\JsonPointer.Net.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\JsonSchema.Net.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\LLamaSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\MediatR.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\MediatR.Contracts.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Bcl.Cryptography.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Bcl.HashCode.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.AI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.AI.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.CommandLine.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Diagnostics.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Http.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.Configuration.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.Console.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.Debug.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.EventLog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Logging.EventSource.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Extensions.VectorData.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Identity.Client.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.Identity.Client.Extensions.Msal.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.All.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.Anthropic.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.AzureOpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.LlamaSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.Ollama.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.Onnx.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.AI.Tiktoken.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Chunkers.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.DataFormats.AzureAIDocIntel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.DocumentStorage.AWSS3.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.DocumentStorage.AzureBlobs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.MemoryDb.AzureAISearch.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.MemoryDb.Elasticsearch.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Postgres.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.MemoryDb.Qdrant.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.MemoryDb.Redis.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.MemoryDb.SQLServer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.MongoDbAtlas.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Orchestration.AzureQueues.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Orchestration.RabbitMQ.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.Safety.AzureAIContentSafety.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.SemanticKernelPlugin.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.KernelMemory.WebClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.ML.OnnxRuntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.ML.OnnxRuntimeGenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.ML.Tokenizers.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.ML.Tokenizers.Data.Cl100kBase.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.ML.Tokenizers.Data.O200kBase.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.ML.Tokenizers.Data.P50kBase.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.OpenApi.Readers.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Agents.Abstractions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Agents.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Connectors.Google.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Connectors.Ollama.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Connectors.OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SemanticKernel.Plugins.OpenApi.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Microsoft.SqlServer.Server.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ModelContextProtocol.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\NetTopologySuite.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Npgsql.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\NRedisStack.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OllamaSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OpenAI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OpenTelemetry.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OpenTelemetry.Api.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OpenTelemetry.Api.ProviderBuilderExtensions.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OpenTelemetry.Exporter.Console.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\OpenTelemetry.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.DocumentLayoutAnalysis.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.Fonts.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.Package.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.Tokenization.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.Tokens.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\UglyToad.PdfPig.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Pgvector.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Pipelines.Sockets.Unofficial.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Polly.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\RabbitMQ.Client.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\RBush.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.AspNetCore.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Extensions.Logging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Formatting.Compact.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Settings.Configuration.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.Console.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.Debug.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.File.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Serilog.Sinks.MSSqlServer.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\SharpCompress.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\SharpYaml.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\SixLabors.Fonts.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Snappier.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\StackExchange.Redis.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.ClientModel.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.IO.Hashing.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.IO.Packaging.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Linq.Async.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Memory.Data.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Net.ServerSentEvents.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Numerics.Tensors.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Text.Json.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\System.Threading.Channels.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ZstdSharp.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ca\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\de\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\es\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\fa\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\fr\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\nb\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\nl\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\pt-BR\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\pt-PT\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\pt\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\sv\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\tr-TR\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\zh-TW\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\zh\Hangfire.Core.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\cs\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\de\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\es\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\fr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\it\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ja\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ko\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\pl\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\pt-BR\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ru\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\tr\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\zh-Hans\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\zh-Hant\Microsoft.Data.SqlClient.resources.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\unix\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net8.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\android\native\onnxruntime.aar
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\ios\native\onnxruntime.xcframework.zip
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\linux-arm64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\linux-arm64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\linux-x64\native\libonnxruntime.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\osx-arm64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\osx-x64\native\libonnxruntime.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\onnxruntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\onnxruntime.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x86\native\onnxruntime.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x86\native\onnxruntime.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x86\native\onnxruntime_providers_shared.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x86\native\onnxruntime_providers_shared.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\android\native\onnxruntime-genai.aar
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\ios\native\onnxruntime-genai.xcframework.zip
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\linux-x64\native\libonnxruntime-genai.so
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\osx-arm64\native\libonnxruntime-genai.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\osx-x64\native\libonnxruntime-genai.dylib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-arm64\native\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\onnxruntime-genai.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win-x64\native\onnxruntime-genai.lib
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.Messages.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net8.0\System.Runtime.Caching.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\win\lib\net8.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.Core.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.Infrastructure.dll
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.Core.pdb
C:\Users\<USER>\source\repos\project-app\WebApi\ProjectApp.WebApi\obj\Release\net8.0\PubTmp\Out\ProjectApp.Infrastructure.pdb
