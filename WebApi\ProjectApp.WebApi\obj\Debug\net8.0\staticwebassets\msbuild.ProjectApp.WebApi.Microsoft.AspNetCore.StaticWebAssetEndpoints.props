﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/.5ipweew5fc.gitkeep">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\.gitkeep'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"5ipweew5fc"},{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="},{"Name":"label","Value":"_content/ProjectApp.WebApi/.gitkeep"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 10:59:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/.gitkeep">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\.gitkeep'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-47DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"0"},{"Name":"Content-Type","Value":"application/octet-stream"},{"Name":"ETag","Value":"\u002247DEQpj8HBSa\u002B/TImW\u002B5JCeuQeRkm5NMpJWZG3hSuFU=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 10:59:48 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/agent-chat-test.2m651078tc.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\agent-chat-test.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"2m651078tc"},{"Name":"integrity","Value":"sha256-usFFnQU4D9cPkeG1dil1t3lWgs6t\u002BAp1Wi9c\u002BJkahO4="},{"Name":"label","Value":"_content/ProjectApp.WebApi/agent-chat-test.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"10511"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022usFFnQU4D9cPkeG1dil1t3lWgs6t\u002BAp1Wi9c\u002BJkahO4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 06:39:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/agent-chat-test.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\agent-chat-test.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-usFFnQU4D9cPkeG1dil1t3lWgs6t\u002BAp1Wi9c\u002BJkahO4="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"10511"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022usFFnQU4D9cPkeG1dil1t3lWgs6t\u002BAp1Wi9c\u002BJkahO4=\u0022"},{"Name":"Last-Modified","Value":"Wed, 28 May 2025 06:39:11 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugin-details.3az2yg0702.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugin-details.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"3az2yg0702"},{"Name":"integrity","Value":"sha256-G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I="},{"Name":"label","Value":"_content/ProjectApp.WebApi/plugin-details.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"9832"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugin-details.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugin-details.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"9832"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022G4EPNtbn7rEifJUNLbwijif\u002Bdhy9YnlvjPeurW3sv1I=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugins.367d1cpt86.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugins.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"367d1cpt86"},{"Name":"integrity","Value":"sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="},{"Name":"label","Value":"_content/ProjectApp.WebApi/plugins.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"23350"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/plugins.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\plugins.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"23350"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022dXkXxWBPJEKs1gZrPYLB/YfJGaMUAF/JVp1feWadYVE=\u0022"},{"Name":"Last-Modified","Value":"Tue, 15 Apr 2025 03:14:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/apexmakerclub5.d5944pppt6.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\apexmakerclub5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"d5944pppt6"},{"Name":"integrity","Value":"sha256-r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/apexmakerclub5.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"57189"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:20:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/apexmakerclub5.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\apexmakerclub5.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"57189"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022r6CqGBS/60yeTgKcGtRbjBvQFQ/eWOXlUKZeknVBXCQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:20:34 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.536fkm048a.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_1.jpeg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:48:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.jpeg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpeg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:48:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 31 Mar 2025 11:23:58 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_2.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:30:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:30:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_3.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_3.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:38:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_3.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Tue, 01 Apr 2025 03:38:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_4.536fkm048a.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"536fkm048a"},{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/image_4.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:43:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/image_4.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\image_4.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"56443"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022b4eByHu4rUCz9p/tSkCM6kn3qox4RT5\u002BOr1ZcrKyJJw=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:43:16 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:28:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:28:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_1.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 09:34:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 09:34:54 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_2.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_2.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 01:54:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_2.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 01:54:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_4.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_4.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:02:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_4.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:02:09 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_5.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_5.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:49:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_5.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:49:10 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_6.bcvf9xuk76.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bcvf9xuk76"},{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_6.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 08:51:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Invoice-F66D817D-0001_6.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Invoice-F66D817D-0001_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20565"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u0022a/mgsnghWt9LAe46R91UeSUs\u002B/XykO12aE9iTqU\u002B6GQ=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 08:51:27 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/landingPage.929y9semqo.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\landingPage.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"929y9semqo"},{"Name":"integrity","Value":"sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/landingPage.png"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"292001"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 24 Mar 2025 19:56:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/landingPage.png">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\landingPage.png'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"292001"},{"Name":"Content-Type","Value":"image/png"},{"Name":"ETag","Value":"\u0022YVtvcTETopRT42X0WpHNvtStMnx72TXRb\u002BezIcRfndo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 24 Mar 2025 19:56:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:53:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault.x27hb5tlzi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x27hb5tlzi"},{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/maxresdefault.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:53:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/maxresdefault_1.x27hb5tlzi.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\maxresdefault_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"x27hb5tlzi"},{"Name":"integrity","Value":"sha256-GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/maxresdefault_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"99558"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022GW1m7f4004NJdyaIMW7/UDgoR3Btt0ZKRiD5nPjTvGo=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 09:54:04 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/name.csv">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\name.csv'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"32"},{"Name":"Content-Type","Value":"text/csv"},{"Name":"ETag","Value":"\u00225PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 12:59:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/name.gze1rkoz4g.csv">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\name.csv'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"gze1rkoz4g"},{"Name":"integrity","Value":"sha256-5PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/name.csv"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"32"},{"Name":"Content-Type","Value":"text/csv"},{"Name":"ETag","Value":"\u00225PJlhbMCHpTvT7sy1dU2L1UoX05ZtCQbvAmkCua85WQ=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 12:59:12 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/name.vskwaa03f5.xlsx">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\name.xlsx'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"vskwaa03f5"},{"Name":"integrity","Value":"sha256-T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/name.xlsx"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"4748"},{"Name":"Content-Type","Value":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{"Name":"ETag","Value":"\u0022T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 12:57:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/name.xlsx">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\name.xlsx'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"4748"},{"Name":"Content-Type","Value":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},{"Name":"ETag","Value":"\u0022T6O/SDsqdV0Q/J7JA5wxWFx5DnLIHA3eA9iAOQyClsY=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 12:57:22 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 11:12:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Sun, 25 May 2025 11:12:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 04:48:17 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_1.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 04:48:17 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_2.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:23:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_2.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_2.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:23:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_3.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:26:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_3.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_3.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:26:51 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_4.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 06:57:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_4.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_4.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 06:57:45 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_5.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 02:39:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_5.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_5.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_5.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 02:39:32 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_6.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:35:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_6.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_6.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_6.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 03:35:23 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_7.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_7.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 05:09:55 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_7.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt-2252-1522_7.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt-2252-1522_7.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Mon, 19 May 2025 05:09:55 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:15:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Fri, 16 May 2025 04:15:39 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:11:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/receipt_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:11:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:12:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_1.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/receipt_1_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:12:08 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_2.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:13:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_2.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_2.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/receipt_1_2.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:13:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_3.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:26:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_3.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_3.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/receipt_1_3.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:26:31 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_4.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:29:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/receipt_1_4.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\receipt_1_4.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/receipt_1_4.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 11:29:40 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt_amardeep.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt_amardeep.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 05:33:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt_amardeep.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt_amardeep.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt_amardeep.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 05:33:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt_shyam.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt_shyam.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 10:39:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt_shyam.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt_shyam.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt_shyam.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 10:39:47 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt_shyam_1.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt_shyam_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 10:55:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/Receipt_shyam_1.z0f7gfgpqz.pdf">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\Receipt_shyam_1.pdf'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"z0f7gfgpqz"},{"Name":"integrity","Value":"sha256-0zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/Receipt_shyam_1.pdf"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"20386"},{"Name":"Content-Type","Value":"application/pdf"},{"Name":"ETag","Value":"\u00220zN6ooxyyiBUsq7ihY8kzLhcwNw0SRjlTvyFIwzEBC0=\u0022"},{"Name":"Last-Modified","Value":"Thu, 05 Jun 2025 10:55:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg.hk1hg0l2za.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hk1hg0l2za"},{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/robot leg.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:14:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:14:49 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg_1.hk1hg0l2za.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hk1hg0l2za"},{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/robot leg_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:18:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/robot leg_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\robot leg_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"47187"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022MQLa3BFD6qIyzZL\u002BvslinGQv2tjcO9UYI/wsQ1G48rI=\u0022"},{"Name":"Last-Modified","Value":"Thu, 03 Apr 2025 01:18:44 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/test.54emubzsk1.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\test.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"54emubzsk1"},{"Name":"integrity","Value":"sha256-C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB\u002B8="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/test.txt"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"38"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB\u002B8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:03:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/test.docx">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\test.docx'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"6507"},{"Name":"Content-Type","Value":"application/vnd.openxmlformats-officedocument.wordprocessingml.document"},{"Name":"ETag","Value":"\u0022/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:01:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/test.lacrko49p0.docx">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\test.docx'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lacrko49p0"},{"Name":"integrity","Value":"sha256-/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/test.docx"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"6507"},{"Name":"Content-Type","Value":"application/vnd.openxmlformats-officedocument.wordprocessingml.document"},{"Name":"ETag","Value":"\u0022/epIw3cWKg4hl9qFET9SOwI2dG9WJMYIFOFYjs2e7Ho=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:01:56 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/test.txt">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\test.txt'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB\u002B8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"38"},{"Name":"Content-Type","Value":"text/plain"},{"Name":"ETag","Value":"\u0022C/XzowFBgPjdVYIiXA7DJRnIC3JO9geohpUEmtlGB\u002B8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:03:28 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/text.4rhg4rlloj.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\text.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4rhg4rlloj"},{"Name":"integrity","Value":"sha256-IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/text.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"109894"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:28:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/text.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\text.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"109894"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022IOIywCEQkhTpcHtsOlj02do1ZjqkV2PmTrYy3NXrzS8=\u0022"},{"Name":"Last-Modified","Value":"Wed, 04 Jun 2025 13:28:21 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:30:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:30:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_1.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe_1.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:44:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_1.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_1.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:44:25 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_2.4oku5zbii3.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"4oku5zbii3"},{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="},{"Name":"label","Value":"_content/ProjectApp.WebApi/uploads/vibe_2.jpg"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:46:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/ProjectApp.WebApi/uploads/vibe_2.jpg">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\uploads\vibe_2.jpg'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=3600, must-revalidate"},{"Name":"Content-Length","Value":"25704"},{"Name":"Content-Type","Value":"image/jpeg"},{"Name":"ETag","Value":"\u0022JIXeqRx1I/5Ok7OuO66qZOXqpIwzSPf2FyVxVG1pRro=\u0022"},{"Name":"Last-Modified","Value":"Thu, 01 May 2025 12:46:50 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>