{"ConnectionStrings": {"AdminConnection": "Server=NC1-GaneshT540P; Database=ProjectApp; User ID=tms-local; Password=password@7894!;TrustServerCertificate=True"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.MSSqlServer"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Hangfire": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "MSSqlServer", "Args": {"connectionString": "AdminConnection", "tableName": "AppLogs", "autoCreateSqlTable": true, "columnOptionsSection": {"addStandardColumns": ["LogEvent", "TraceId", "SpanId"], "removeStandardColumns": ["MessageTemplate", "Properties"]}}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithExceptionDetails"]}, "CorsOrigins": "http://localhost:4200,http://localhost:4201,https://aihub-dev.3dbotics.com,https://aihub.3dbotics.com,https://project-dev.3dbotics.com", "AllowedHosts": "*", "Jwt": {"Key": "YourSecretKeyHere12345678901234567890", "Issuer": "ProjectApp<PERSON>ssuer", "Audience": "ProjectAppAudience", "DurationInDays": 2}, "OpenAI": {"ApiKey": "********************************************************************************************************************************************************************", "ChatModelId": "gpt-4o-mini", "EmbeddingModelId": "text-embedding-3-small"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderPassword": "cokm oocu yknn xbml", "SenderName": "Project Management System"}, "EmailReceiver": {"ImapServer": "imap.gmail.com", "ImapPort": 993, "EnableSsl": true, "Email": "<EMAIL>", "Password": "cokm oocu yknn xbml", "InboxFolder": "INBOX", "CheckIntervalMinutes": 5, "IsEnabled": true}}