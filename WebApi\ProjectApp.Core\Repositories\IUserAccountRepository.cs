﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;


namespace ProjectApp.Core.Repositories
{
    public interface IUserAccountRepository
    {
        Task<List<UserDto>> GetAll();
        Task<List<UserDto>> GetAllUsers();
        Task<UserDto> IsUser(string email);
        Task Register(RegisterDto model);
        Task<bool> VerifyOtp(string email, string otp);
        Task<bool> GenerateOtp(string email);
        Task<UserDto> Login(string email, string password);
        Task DeleteUser(string email);
        Task AssignRole(string email, string role);
        Task RemoveRole(string email, string role);
    }
}
