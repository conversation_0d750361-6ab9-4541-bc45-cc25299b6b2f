﻿using AutoMapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System.Net;
using System.Text.Json;

namespace ProjectApp.Core.Profiles
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<Project, ProjectViewDto>();
            CreateMap<ProjectDto, Project>();
            CreateMap<CommentDto, Comment>();
            CreateMap<NewProjectDto, Project>();
            CreateMap<MemoryDto, Memory>(); // Add this mapping
            CreateMap<Memory, MemoryDto>(); // Add this mapping


            // Chat mappings
            CreateMap<ChatMessage, ChatMessageDto>();

            CreateMap<ChatHistories, ChatHistoryDto>()
                .ForMember(dest => dest.Responses, opt => opt.MapFrom(src => src.Responses));

            CreateMap<ChatResponse, ChatResponseDto>()
                .ForMember(dest => dest.ChatSources, opt => opt.MapFrom(src =>
                    !string.IsNullOrEmpty(src.ChatSources)
                        ? JsonSerializer.Deserialize<List<ChatSource>>(src.ChatSources,
                            new JsonSerializerOptions { PropertyNameCaseInsensitive = true })
                        : null));

            CreateMap<AgentDefinitionDto, AgentDefinition>()
                .ForMember(dest => dest.Tools, opt => opt.MapFrom(src => src.Tools != null ? string.Join(",", src.Tools) : null));
            CreateMap<AgentDefinition, AgentDefinitionDto>()
                .ForMember(dest => dest.Tools, opt => opt.MapFrom(src => src.ToolsArray));

            CreateMap<CreateChatModelDto, ChatModel>();
            CreateMap<ChatModel, CreateChatModelDto>();

            CreateMap<ApiCredentialsDto, ApiCredentials>();

            // Agent Chat History mappings
            //CreateMap<AgentChatHistory, AgentChatHistoryDto>();
            //CreateMap<AgentChatHistoryDto, AgentChatHistory>();
            //CreateMap<AgentChatRequestDto, AgentChatHistory>();
            
            //CreateMap<AgentChatResponse, AgentChatResponseDto>();
            //CreateMap<AgentChatResponseDto, AgentChatResponse>();
            //CreateMap<CreateAgentChatResponseDto, AgentChatResponse>();
        }
    }
}
