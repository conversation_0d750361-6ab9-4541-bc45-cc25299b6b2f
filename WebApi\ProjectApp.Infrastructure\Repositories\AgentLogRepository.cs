using Dapper;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class AgentLogRepository : IAgentLogRepository
    {
        private readonly IDbConnection _dbConnection;

        public AgentLogRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<int> CreateLog(AgentLog log)
        {
            const string sql = @"
                INSERT INTO AgentLogs (AgentName, FunctionName, Parameters, Status, CreatedAt)
                OUTPUT INSERTED.Id
                VALUES (@AgentName, @FunctionName, @Parameters, @Status, @CreatedAt)";

            return await _dbConnection.ExecuteScalarAsync<int>(sql, log);
        }

        public async Task<List<AgentLog>> GetPendingLogs()
        {
            const string sql = "SELECT * FROM AgentLogs WHERE Status = 'Created' ORDER BY CreatedAt";
            return (await _dbConnection.QueryAsync<AgentLog>(sql)).ToList();
        }

        public async Task UpdateLogStatus(int id, string status, string errorMessage = null)
        {
            const string sql = @"
                UPDATE AgentLogs 
                SET Status = @Status, 
                    ExecutedAt = CASE WHEN @Status IN ('Executed', 'Failed') THEN GETUTCDATE() ELSE ExecutedAt END,
                    ErrorMessage = @ErrorMessage
                WHERE Id = @Id";

            await _dbConnection.ExecuteAsync(sql, new { Id = id, Status = status, ErrorMessage = errorMessage });
        }

        public async Task<AgentLog> GetById(int id)
        {
            const string sql = "SELECT * FROM AgentLogs WHERE Id = @Id";
            return await _dbConnection.QuerySingleOrDefaultAsync<AgentLog>(sql, new { Id = id });
        }
    }
} 