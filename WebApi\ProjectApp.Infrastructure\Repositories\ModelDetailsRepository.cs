﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ModelDetailsRepository : IModelDetailsRepository
    {
        private readonly IDbConnection _dbConnection;

        public ModelDetailsRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<Guid> Create(ModelDetails modelDetails)
        {
            modelDetails.Id = Guid.NewGuid();
            var sql = "INSERT INTO ModelDetails (Id, ModelName, ModelProvider, IsActive, IsCustom, ApiCredentialsId) VALUES (@Id, @ModelName, @ModelProvider, @IsActive, @IsCustom, @ApiCredentialsId); SELECT @Id;";
            return await _dbConnection.ExecuteScalarAsync<Guid>(sql, modelDetails);
        }

        public async Task CreateRangeAsync(List<ModelDetails> modelDetailsList)
        {
            foreach (var modelDetails in modelDetailsList)
            {
                modelDetails.Id = Guid.NewGuid();
            }
            var sql = "INSERT INTO ModelDetails (Id, ModelName, ModelProvider, IsActive, IsEmbeddingActive, IsCustom, ApiCredentialsId) VALUES (@Id, @ModelName, @ModelProvider, @IsActive, 0, @IsCustom, @ApiCredentialsId);";
            await _dbConnection.ExecuteAsync(sql, modelDetailsList);
        }

        public async Task<IEnumerable<ModelDetailsDto>> GetAll()
        {
            return await QueryModelDetailsAsync();
        }

        public async Task<IEnumerable<ModelDetailsNameDto>> GetAllActiveModel()
        {
            var sql = "SELECT ModelName FROM ModelDetails WHERE IsActive = 1";
            var modelNames = await _dbConnection.QueryAsync<string>(sql);
            return modelNames.Select(name => new ModelDetailsNameDto { ModelName = name });
        }

        public async Task<ModelDetailsDto> GetByModelNameAsync(string modelName)
        {
            var results = await QueryModelDetailsAsync("md.ModelName = @ModelName", new { ModelName = modelName });
            return results.FirstOrDefault();
        }

        public async Task<ResponseMessage> UpdateIsActive(string modelName, bool isActive)
        {
            var sql = "UPDATE ModelDetails SET IsActive = @IsActive WHERE ModelName = @ModelName";
            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new { ModelName = modelName, IsActive = isActive });

            return new ResponseMessage
            {
                IsError = rowsAffected == 0,
                Message = rowsAffected > 0 ? "Update successful" : "Update failed"
            };
        }

        public async Task<int> DeleteByProvider(string modelProvider)
        {
            var sql = "DELETE FROM ModelDetails WHERE ModelProvider = @ModelProvider";
            return await _dbConnection.ExecuteAsync(sql, new { ModelProvider = modelProvider });
        }

        public async Task<int> DeleteByProviderExcept(string modelProvider, List<string> modelNamesToKeep)
        {
            var sql = "DELETE FROM ModelDetails WHERE ModelProvider = @ModelProvider AND ModelName NOT IN @ModelNames AND IsCustom = 0";
            return await _dbConnection.ExecuteAsync(sql, new { ModelProvider = modelProvider, ModelNames = modelNamesToKeep });
        }

        public async Task<int> DeleteCustomModelsByApiCredentialsId(Guid apiCredentialsId)
        {
            var sql = "DELETE FROM ModelDetails WHERE ApiCredentialsId = @ApiCredentialsId AND IsCustom = 1";
            return await _dbConnection.ExecuteAsync(sql, new { ApiCredentialsId = apiCredentialsId });
        }

        public async Task<IEnumerable<ModelDetailsDto>> GetByApiCredentialsIdAsync(Guid apiCredentialsId)
        {
            return await QueryModelDetailsAsync("md.ApiCredentialsId = @ApiCredentialsId", new { ApiCredentialsId = apiCredentialsId });
        }

        public async Task<IEnumerable<ModelDetailsDto>> GetByProviderAsync(string modelProvider)
        {
            return await QueryModelDetailsAsync("md.ModelProvider = @ModelProvider", new { ModelProvider = modelProvider });
        }

        // Private reusable method to handle the repeated query logic
        private async Task<IEnumerable<ModelDetailsDto>> QueryModelDetailsAsync(string whereClause = null, object parameters = null)
        {
            var sql = @"
                SELECT
                    md.Id,
                    md.ModelName,
                    md.ModelProvider,
                    md.IsActive,
                    md.IsCustom,
                    md.ApiCredentialsId,
                    STRING_AGG(a.AgentName, ',') AS AgentNames
                FROM
                    ModelDetails md
                LEFT JOIN
                    AgentDefinitions a ON md.ModelName = a.ModelName
                " + (string.IsNullOrEmpty(whereClause) ? "" : "WHERE " + whereClause) + @"
                GROUP BY
                    md.Id, md.ModelName, md.ModelProvider, md.IsActive, md.IsCustom, md.ApiCredentialsId
                ORDER BY
                    md.IsActive DESC";

            var modelDetailsDictionary = new Dictionary<Guid, ModelDetailsDto>();

            await _dbConnection.QueryAsync<ModelDetailsDto, string, ModelDetailsDto>(
                sql,
                (modelDetails, agentNames) =>
                {
                    if (!modelDetailsDictionary.TryGetValue(modelDetails.Id, out var modelDetailsEntry))
                    {
                        modelDetailsEntry = modelDetails;
                        modelDetailsEntry.AgentNames = new List<string>();
                        modelDetailsDictionary.Add(modelDetailsEntry.Id, modelDetailsEntry);
                    }

                    if (!string.IsNullOrEmpty(agentNames))
                    {
                        modelDetailsEntry.AgentNames.AddRange(agentNames.Split(','));
                    }

                    return modelDetailsEntry;
                },
                parameters,
                splitOn: "AgentNames"
            );

            return modelDetailsDictionary.Values;
        }
        public async Task<IEnumerable<EmbeddingModelDto>> GetAllEmbeddingModels()
        {
            var sql = "SELECT * FROM ModelDetails WHERE ModelName LIKE '%embedding%' OR ModelName LIKE '%embed%'";
            var modelDetailsList = await _dbConnection.QueryAsync<ModelDetails>(sql);
            return modelDetailsList.Select(model => new EmbeddingModelDto
            {
                ModelName = model.ModelName,
                Provider = model.ModelProvider,
                IsEmbeddingActive = model.IsEmbeddingActive,
            });
        }

        public async Task<ResponseMessage> SetEmbeddingToTrue(string modelName)
        {
            // Ensure the connection is open before starting a transaction
            if (_dbConnection.State != ConnectionState.Open)
            {
                _dbConnection.Open();
            }

            using (var transaction = _dbConnection.BeginTransaction())
            {
                try
                {
                    var sqlDeactivate = "UPDATE ModelDetails SET IsEmbeddingActive = 0 WHERE ModelName != @ModelName";
                    await _dbConnection.ExecuteAsync(sqlDeactivate, new { ModelName = modelName }, transaction);

                    var sqlActivate = "UPDATE ModelDetails SET IsEmbeddingActive = 1 WHERE ModelName = @ModelName";
                    var rowsAffected = await _dbConnection.ExecuteAsync(sqlActivate, new { ModelName = modelName }, transaction);

                    transaction.Commit();

                    return new ResponseMessage
                    {
                        IsError = rowsAffected == 0,
                        Message = rowsAffected > 0 ? "Update successful! Restart the project" : "Update failed"
                    };
                }
                catch
                {
                    transaction.Rollback();
                    return new ResponseMessage
                    {
                        IsError = true,
                        Message = "Update failed"
                    };
                }
            }
        }

        public async Task<EmbeddingModelAndKey> GetActiveEmbeddingConfigAsync()
        {
            var sql = "SELECT * FROM ModelDetails WHERE IsEmbeddingActive = 1";
            var res = await _dbConnection.QueryFirstOrDefaultAsync<ModelDetails>(sql);

            if (res == null)
            {
                return null; // or throw an exception based on your design
            }
            var sql2 = "SELECT * FROM ApiCredentials";
            var res2 = await _dbConnection.QueryAsync<ApiCredentials>(sql2);


            foreach (var item in res2)
            {
                var provider = ExtractProviderFromUrl(item.TokenUrl);
                if (provider.Equals(res.ModelProvider, StringComparison.OrdinalIgnoreCase))
                {
                    return new EmbeddingModelAndKey
                    {
                        ModelName = res.ModelName,
                        APIKey = item.ApiKey
                    };
                }
            }
            // If no matching API key found for the provider
            return null;

        }

        private string ExtractProviderFromUrl(string tokenUrl)
        {
            var uri = new Uri(tokenUrl.ToLower());
            if (uri.Host.Contains("azure.com")) return "Azure";
            if (uri.Host.Contains("openai.com")) return "OpenAI";
            if (uri.Host.Contains("generativelanguage.googleapis.com")) return "GoogleAI";
            if (uri.Host.Contains("localai")) return "LocalAI";
            return "Unknown";
        }

        public async Task<int> DeleteByModelNameAsync(List<string> modelNames)
        {
            var sql = "DELETE FROM ModelDetails WHERE ModelName IN @ModelNames";
            return await _dbConnection.ExecuteAsync(sql, new { ModelNames = modelNames });
        }
    }
}