﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Microsoft.SemanticKernel;
using System.ComponentModel;

namespace ProjectApp.Infrastructure.AIAgents.Tools;

public class SqlGeneratorPlugin
{
    [KernelFunction("generate_select")]
    [Description("Generates a SELECT SQL statement for the given table and columns. Optionally add a WHERE clause.")]
    public string GenerateSelect(
        [Description("Table name")] string tableName,
        [Description("List of columns to select")] List<string> columns,
        [Description("Optional WHERE clause")] string? whereClause = null)
    {
        if (string.IsNullOrWhiteSpace(tableName) || columns == null || columns.Count == 0)
            throw new ArgumentException("Table name and columns are required");
        var cols = string.Join(", ", columns.Select(c => $"[{c}]").ToArray());
        var sql = $"SELECT {cols} FROM [{tableName}]";
        if (!string.IsNullOrWhiteSpace(whereClause))
            sql += $" WHERE {whereClause}";
        return sql;
    }

    [KernelFunction("generate_insert")]
    [Description("Generates an INSERT SQL statement for the given table and columns.")]
    public string GenerateInsert(
        [Description("Table name")] string tableName,
        [Description("List of columns to insert")] List<string> columns)
    {
        if (string.IsNullOrWhiteSpace(tableName) || columns == null || columns.Count == 0)
            throw new ArgumentException("Table name and columns are required");
        var cols = string.Join(", ", columns.Select(c => $"[{c}]").ToArray());
        var vals = string.Join(", ", columns.Select(c => $"@{c}").ToArray());
        return $"INSERT INTO [{tableName}] ({cols}) VALUES ({vals})";
    }

    [KernelFunction("generate_update")]
    [Description("Generates an UPDATE SQL statement for the given table, columns, and WHERE clause.")]
    public string GenerateUpdate(
        [Description("Table name")] string tableName,
        [Description("List of columns to update")] List<string> columns,
        [Description("WHERE clause")] string whereClause)
    {
        if (string.IsNullOrWhiteSpace(tableName) || columns == null || columns.Count == 0 || string.IsNullOrWhiteSpace(whereClause))
            throw new ArgumentException("Table name, columns, and whereClause are required");
        var sets = string.Join(", ", columns.Select(c => $"[{c}] = @{c}").ToArray());
        return $"UPDATE [{tableName}] SET {sets} WHERE {whereClause}";
    }

    [KernelFunction("generate_delete")]
    [Description("Generates a DELETE SQL statement for the given table and WHERE clause.")]
    public string GenerateDelete(
        [Description("Table name")] string tableName,
        [Description("WHERE clause")] string whereClause)
    {
        if (string.IsNullOrWhiteSpace(tableName) || string.IsNullOrWhiteSpace(whereClause))
            throw new ArgumentException("Table name and whereClause are required");
        return $"DELETE FROM [{tableName}] WHERE {whereClause}";
    }

    [KernelFunction("is_valid_sql")]
    [Description("Checks if the provided SQL is a valid SELECT, INSERT, UPDATE, or DELETE statement.")]
    public bool IsValidSql(
        [Description("SQL statement to validate")] string sql)
    {
        if (string.IsNullOrWhiteSpace(sql)) return false;
        var upper = sql.Trim().ToUpperInvariant();
        return upper.StartsWith("SELECT") || upper.StartsWith("INSERT") || upper.StartsWith("UPDATE") || upper.StartsWith("DELETE");
    }
}
