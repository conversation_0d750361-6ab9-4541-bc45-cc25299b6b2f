﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]    public class DailyInsightController : ControllerBase
    {
        private readonly IDailyInsightAgentRepository _dailyInsightAgentRepository;
        private readonly IEmailProcessingResultRepository _emailProcessingResultRepository;
        private readonly IExtractEmailFromAccessor _emailExtractor;
        private readonly AIAgentFactory _aiAgentFactory;

        public DailyInsightController(
            IDailyInsightAgentRepository dailyInsightAgentRepository,
            IEmailProcessingResultRepository emailProcessingResultRepository,
            IExtractEmailFromAccessor emailExtractor,
            AIAgentFactory aiAgentFactory)
        {
            _dailyInsightAgentRepository = dailyInsightAgentRepository;
            _emailProcessingResultRepository = emailProcessingResultRepository;
            _emailExtractor = emailExtractor;
            _aiAgentFactory = aiAgentFactory;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<IEnumerable<DailyInsightAgentResponseDto>>> GetAll()
        {
            var userEmail = _emailExtractor.GetEmail();
            var agents = await _dailyInsightAgentRepository.GetAllByUserEmailAsync(userEmail);

            var result = agents.Select(a => new DailyInsightAgentResponseDto
            {
                Id = a.Id,
                AgentName = a.AgentName,
                Prompt = a.Prompt,
                Title = a.Title,
                CreatedAt = a.CreatedAt,
                LastRunAt = a.LastRunAt,
                LastResponse = a.LastResponse
            });

            return Ok(result);
        }

        [HttpPost("Create")]
        public async Task<ActionResult<DailyInsightAgentResponseDto>> Create(DailyInsightAgentDto dto)
        {
            var userEmail = _emailExtractor.GetEmail();
            var agent = await _dailyInsightAgentRepository.CreateAsync(dto, userEmail);

            try
            {
                // Run the agent immediately after creation to get initial data
                Console.WriteLine($"[{DateTime.Now}] Running newly created agent: {agent.AgentName} (ID: {agent.Id})");

                // Call the AI agent with the prompt
                var response = await _aiAgentFactory.CallAIAgentAsync(agent.AgentName, agent.Prompt);

                // Truncate response if it's too long (to avoid DB issues)
                if (response != null && response.Length > 8000)
                {
                    response = response.Substring(0, 8000) + "\n\n[Response truncated due to length]";
                }

                // Update the agent with the response
                await _dailyInsightAgentRepository.UpdateLastRunAsync(agent.Id, response);
                Console.WriteLine($"[{DateTime.Now}] Successfully processed newly created agent: {agent.AgentName}");

                // Refresh agent data after update
                agent = await _dailyInsightAgentRepository.GetByIdAsync(agent.Id);
            }
            catch (Exception ex)
            {
                // Log the error but continue - we don't want to fail the creation if the initial run fails
                Console.WriteLine($"[{DateTime.Now}] Error processing newly created agent {agent.AgentName}: {ex.Message}");
                Console.WriteLine($"[{DateTime.Now}] Stack trace: {ex.StackTrace}");
            }

            var result = new DailyInsightAgentResponseDto
            {
                Id = agent.Id,
                AgentName = agent.AgentName,
                Prompt = agent.Prompt,
                Title = agent.Title,
                CreatedAt = agent.CreatedAt,
                LastRunAt = agent.LastRunAt,
                LastResponse = agent.LastResponse
            };

            return Ok(result);
        }

        [HttpDelete("Delete/{id}")]
        public async Task<ActionResult<ResponseMessage>> Delete(int id)
        {
            var deleted = await _dailyInsightAgentRepository.DeleteAsync(id);
            if (!deleted)
            {
                return NotFound(new ResponseMessage { IsError = true, Message = "Daily insight agent not found." });
            }

            return Ok(new ResponseMessage { IsError = false, Message = "Daily insight agent deleted successfully." });
        }

        [HttpPost("RunNow/{id}")]
        public async Task<ActionResult<ResponseMessage>> RunNow(int id)
        {
            var agent = await _dailyInsightAgentRepository.GetByIdAsync(id);
            if (agent == null)
            {
                return NotFound(new ResponseMessage { IsError = true, Message = "Daily insight agent not found." });
            }

            try
            {
                // Call the AI agent with the prompt
                var response = await _aiAgentFactory.CallAIAgentAsync(agent.AgentName, agent.Prompt);

                // Truncate response if it's too long (to avoid DB issues)
                if (response != null && response.Length > 8000)
                {
                    response = response.Substring(0, 8000) + "\n\n[Response truncated due to length]";
                }

                // Update the agent with the response
                await _dailyInsightAgentRepository.UpdateLastRunAsync(agent.Id, response);

                return Ok(new ResponseMessage { IsError = false, Message = "Daily insight agent executed successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }

        /// <summary>
        /// Get email processing results for the current user (for Daily Insights panel)
        /// </summary>
        [HttpGet("GetEmailResults")]
        public async Task<ActionResult<IEnumerable<EmailProcessingResultDto>>> GetEmailResults()
        {
            try
            {
                var userEmail = _emailExtractor.GetEmail();
                var results = await _emailProcessingResultRepository.GetRecentResultsAsync(userEmail, 50);

                var dtos = results.Select(r => new EmailProcessingResultDto
                {
                    Id = r.Id,
                    SenderEmail = r.SenderEmail,
                    EmailSubject = r.EmailSubject,
                    EmailContent = r.EmailContent,
                    AgentName = r.AgentName,
                    AgentResponse = r.AgentResponse,
                    ProcessedAt = r.ProcessedAt,
                    MessageId = r.MessageId
                });

                return Ok(dtos);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
