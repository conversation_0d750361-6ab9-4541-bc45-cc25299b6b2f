using System;

namespace ProjectApp.Core.Dtos
{
    public class PluginRequestDto
    {
        public Guid? Id { get; set; }
        public string PluginName { get; set; }
        public string Type { get; set; }
        public string Functions { get; set; }
        public string Url { get; set; }
        public string RequiredParameters { get; set; }
        public string EnvironmentVariables { get; set; }
    }

    public class PluginResponseDto
    {
        public Guid Id { get; set; }
        public string PluginName { get; set; }
        public string Type { get; set; }
        public string Functions { get; set; }
        public string Url { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? LastModifiedDate { get; set; }
        public string RequiredParameters { get; set; }
        public string EnvironmentVariables { get; set; }
    }
} 