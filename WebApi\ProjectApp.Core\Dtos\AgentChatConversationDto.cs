namespace ProjectApp.Core.Dtos
{
    public class AgentChatConversationDto
    {
        public string AgentName { get; set; }
        public List<AgentChatHistoryDto> Histories { get; set; }
    }
    
    public class PaginatedAgentChatConversationDto
    {
        public string AgentName { get; set; }
        public List<AgentChatHistoryDto> Histories { get; set; }
        public bool HasMore { get; set; }
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
    }
    
    public class AgentChatHistoryDto
    {
        public Guid Id { get; set; }
        public string Question { get; set; }
        public DateTime Timestamp { get; set; }
        public List<AgentChatResponseDto> Responses { get; set; }
    }

    public class AgentChatResponseDto
    {
        public Guid Id { get; set; }
        public string ResponseText { get; set; }
        public string ChatSource { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
