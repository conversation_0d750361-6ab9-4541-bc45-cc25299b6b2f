using Dapper;
using Hangfire;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;
using System.Data;

namespace ProjectApp.Infrastructure.Services
{
    public class ProjectBackgroundService
    {
        private readonly IDbConnection _db;
        private readonly AIService _aiService;
        private readonly ITaskRepository _taskRepository;
        private readonly EmailService _emailService;
        private readonly IWorkspaceRepository _workspaceRepository;
        private readonly AIAgentFactory _aIAgentFactory;

        public ProjectBackgroundService(
            IDbConnection db,
            AIService aiService,
            ITaskRepository taskRepository,
            EmailService emailService,
            IWorkspaceRepository workspaceRepository,
            AIAgentFactory aiAgentFactory)
        {
            _db = db;
            _aiService = aiService;
            _taskRepository = taskRepository;
            _emailService = emailService;
            _workspaceRepository = workspaceRepository;
            _aIAgentFactory = aiAgentFactory;
        }

        [AutomaticRetry(Attempts = 3)]
        public async Task ProcessProjectAfterCreation(int projectId)
        {
            var project = await _db.QueryFirstOrDefaultAsync<Project>(
                "SELECT * FROM Projects WHERE Id = @Id",
                new { Id = projectId });

            if (project == null) return;

            try
            {
                // Get last completion date for the assigned developer
                var lastCompletionDate = await _db.QueryFirstOrDefaultAsync<DateTime?>(
                    @"SELECT TOP 1 CompletionDate FROM Projects 
                        WHERE AssignedEmail = @AssignedEmail 
                        ORDER BY CompletionDate DESC",
                    new { AssignedEmail = project.AssignedEmail });

                var startDate = lastCompletionDate ?? DateTime.Now;

                var input = $"Subject: {project.Subject}, Message: {project.Message}, Priority: {project.Priority}";
                // Generate AI content
                project.Summary = await _aIAgentFactory.CallAIAgentAsync("Summarizer", input);

                input = input + $", StartDate: {startDate}";
                var days = await _aIAgentFactory.CallAIAgentAsync("CompletionDateEstimator", input);
                project.CompletionDate = startDate.AddDays(int.Parse(days));


                // Update project with AI results
                await _db.ExecuteAsync(
                    @"UPDATE Projects SET
                            Summary = @Summary,
                            CompletionDate = @CompletionDate
                        WHERE Id = @Id",
                    new
                    {
                        project.Id,
                        project.Summary,
                        project.CompletionDate
                    });

                //// Generate tasks using workspace from main flow
                //var tasks = await _aiService.GenerateProjectTasks(
                //    project.Subject, project.Message,
                //    project.WorkspaceId,
                //    startDate, project.CompletionDate,
                //    project.FileNames.Split("@#").ToList());

                input += $", CompletionDate: {project.CompletionDate}, FilesNames = [{project.FileNames}] Index: DocsData, MemoryTag: [name: WorkspaceId, value: {project.WorkspaceId}]";

                var tasks = await _aIAgentFactory.CallAIAgentAsync("TaskGeneratorAgent", input);
                var taskDtos = ProcessAgentResponse(tasks, startDate, project.CompletionDate);
                if (taskDtos.Count == 0)
                {
                    taskDtos = GetFallbackTasks(startDate);
                }

                await _taskRepository.CreateProjectTasksFromAi(taskDtos, project.Id, project.AssignedEmail);

                // Send email
                //await _emailService.SendProjectCreationEmail(project);
            }
            catch (Exception ex)
            {
                await _db.ExecuteAsync(
                    "UPDATE Projects SET Summary = @Summary WHERE Id = @Id",
                    new { project.Id, Summary = "Error in processing" });
            }
        }

        private List<TaskDateDto> ProcessAgentResponse(string responseContent, DateTime StartDate, DateTime completionDate)
        {
            var taskDtos = new List<TaskDateDto>();
            var phaseMatches = System.Text.RegularExpressions.Regex.Matches(
                responseContent,
                @"PHASE (\d+):\s*(.*?)\s*\[(.*?)\].*?TASKS:\s*(.*?)(?:\s*\||$)",
                System.Text.RegularExpressions.RegexOptions.Singleline
            );

            foreach (System.Text.RegularExpressions.Match match in phaseMatches)
            {
                try
                {
                    var phaseNumber = match.Groups[1].Value;
                    var phaseName = match.Groups[2].Value.Trim();
                    var tasks = match.Groups[4].Value
                        .Split(';')
                        .Select(t => t.Trim())
                        .Where(t => !string.IsNullOrWhiteSpace(t));

                    var daysPerTask = Math.Max(1, (completionDate - StartDate).Days / Math.Max(1, tasks.Count()));
                    var currentDate = StartDate;

                    foreach (var task in tasks)
                    {
                        while (currentDate.DayOfWeek is DayOfWeek.Saturday or DayOfWeek.Sunday)
                        {
                            currentDate = currentDate.AddDays(1);
                        }

                        taskDtos.Add(new TaskDateDto
                        {
                            Task = $"Phase {phaseNumber} - {phaseName}: {task}",
                            DueDate = currentDate,
                            Complexity = "Low"
                        });

                        currentDate = currentDate.AddDays(daysPerTask);
                    }
                }
                catch { /* Log error */ }
            }

            return taskDtos
                .OrderBy(t => t.DueDate)
                .ToList();
        }

        private List<TaskDateDto> GetFallbackTasks(DateTime startDate) => new()
        {
            new TaskDateDto
            {
                Task = "Phase 1 - Initial Setup: Project kickoff meeting",
                DueDate = startDate.AddDays(1),
                Complexity = "Low"
            }
        };
    }
} 