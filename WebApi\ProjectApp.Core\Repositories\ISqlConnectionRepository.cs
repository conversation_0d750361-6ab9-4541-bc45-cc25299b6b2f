using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface ISqlConnectionRepository
    {
        Task<SqlConnectionInfoList> GetAllConnectionsAsync();
        Task<ResponseMessage> TestConnectionAsync(string connectionString);
        Task<SqlQueryResponse> ExecuteSqlQueryAsync(SqlQueryRequest queryRequest);
    }
}
