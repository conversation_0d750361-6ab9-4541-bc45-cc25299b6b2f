using Microsoft.Extensions.Options;
using System.Net.Mail;
using System.Net;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Core.Dtos;
using System.Text;

namespace ProjectApp.Infrastructure.Services
{
    public class EmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly AIService _aiService;
        private readonly IProjectRepository _projectRepository;
        private readonly ITaskRepository _taskRepository;
        private readonly IAssignWorkspaceRepository _assignWorkspaceRepository;
        private readonly IWorkspaceRepository _workspaceRepository;

        public EmailService(
            IOptions<EmailSettings> emailSettings,
            AIService aiService,
            IProjectRepository projectRepository,
            ITaskRepository taskRepository,
            IAssignWorkspaceRepository assignWorkspaceRepository,
            IWorkspaceRepository workspaceRepository)
        {
            _emailSettings = emailSettings.Value;
            _aiService = aiService;
            _projectRepository = projectRepository;
            _taskRepository = taskRepository;
            _assignWorkspaceRepository = assignWorkspaceRepository;
            _workspaceRepository = workspaceRepository;
        }

        public async Task SendEmailAsync(string to, List<string> cc, string subject, string htmlBody)
        {
            try
            {
                using var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort)
                {
                    EnableSsl = true,
                    Credentials = new NetworkCredential(_emailSettings.SenderEmail, _emailSettings.SenderPassword)
                };

                var message = new MailMessage
                {
                    From = new MailAddress(_emailSettings.SenderEmail, _emailSettings.SenderName),
                    Subject = subject,
                    Body = htmlBody,
                    IsBodyHtml = true
                };

                message.To.Add(to);

                // Add CC recipients
                if (cc != null && cc.Any())
                {
                    foreach (var ccEmail in cc.Distinct())
                    {
                        if (!string.IsNullOrEmpty(ccEmail) && ccEmail != to) // Avoid duplicating the main recipient
                        {
                            message.CC.Add(ccEmail);
                        }
                    }
                }

                await client.SendMailAsync(message);
            }
            catch (Exception ex)
            {
                // Log the error
                throw;
            }
        }

        public async Task SendProjectCreationEmail(Project project)
        {
            // Get workspace members
            var workspaceMembers = await _assignWorkspaceRepository.GetWorkspaceMemberEmails(project.WorkspaceId);

            // Create CC list including workspace members and project creator
            var ccList = new List<string>(workspaceMembers);
            if (!string.IsNullOrEmpty(project.UserEmail))
            {
                ccList.Add(project.UserEmail);
            }

            // Prepare parameters for the assigned developer's email
            var parameters = new Dictionary<string, string>
            {
                { "Project Title", project.Subject },
                { "Due Date", project.CompletionDate.ToString("MMM dd, yyyy") },
                { "Priority", project.Priority },
                { "Assigned To", project.AssignedEmail },
                { "Created By", project.UserEmail },
                { "Project Summary", project.Summary }
            };

            // Generate and send email to assigned developer
            var assigneeTemplate = await _aiService.GenerateEmailTemplate(
                "New Project Assignment Notification",
                parameters
            );

            await SendEmailAsync(
                project.AssignedEmail,
                ccList,
                assigneeTemplate.Subject,
                assigneeTemplate.HtmlBody
            );
        }

        public async Task SendDailyProjectUpdates()
        {
            // Get all relevant data
            var dueTasks = await _taskRepository.GetPastDueProjectTasks();
            var upcomingTasks = await _taskRepository.GetUpcomingProjectTasks();
            var projects = await _projectRepository.GetAll();

            // Group tasks and projects by assignee and workspace
            var assigneeWorkspaceUpdates = new Dictionary<string, Dictionary<int, (List<ProjectTask> DueTasks, List<ProjectTask> UpcomingTasks, List<ProjectViewDto> Projects)>>();

            foreach (var task in dueTasks.Concat(upcomingTasks))
            {
                if (string.IsNullOrEmpty(task.AssignedEmails)) continue;

                var emails = task.AssignedEmails.Split(',');
                foreach (var email in emails)
                {
                    var trimmedEmail = email.Trim();
                    if (!IsValidEmail(trimmedEmail)) continue; // Skip invalid emails

                    // Assuming each task is associated with a project to get WorkspaceId
                    var project = projects.FirstOrDefault(p => p.Id == task.ProjectId);
                    if (project == null) continue;

                    if (!assigneeWorkspaceUpdates.ContainsKey(trimmedEmail))
                    {
                        assigneeWorkspaceUpdates[trimmedEmail] = new Dictionary<int, (List<ProjectTask>, List<ProjectTask>, List<ProjectViewDto>)>();
                    }

                    var workspaceId = project.WorkspaceId;
                    if (!assigneeWorkspaceUpdates[trimmedEmail].ContainsKey(workspaceId))
                    {
                        assigneeWorkspaceUpdates[trimmedEmail][workspaceId] = (new List<ProjectTask>(), new List<ProjectTask>(), new List<ProjectViewDto>());
                    }

                    if (dueTasks.Contains(task))
                    {
                        assigneeWorkspaceUpdates[trimmedEmail][workspaceId].DueTasks.Add(task);
                    }
                    else
                    {
                        assigneeWorkspaceUpdates[trimmedEmail][workspaceId].UpcomingTasks.Add(task);
                    }
                }
            }

            foreach (var workspaceId in projects.Select(p => p.WorkspaceId).Distinct())
            {
                var workspaceProjects = projects.Where(p => p.WorkspaceId == workspaceId).ToList();
                foreach (var project in workspaceProjects)
                {
                    if (string.IsNullOrEmpty(project.AssignedEmail)) continue;

                    var assignedEmail = project.AssignedEmail.Trim();
                    if (!IsValidEmail(assignedEmail)) continue; // Skip invalid email

                    if (!assigneeWorkspaceUpdates.ContainsKey(assignedEmail))
                    {
                        assigneeWorkspaceUpdates[assignedEmail] = new Dictionary<int, (List<ProjectTask>, List<ProjectTask>, List<ProjectViewDto>)>();
                    }

                    if (!assigneeWorkspaceUpdates[assignedEmail].ContainsKey(workspaceId))
                    {
                        assigneeWorkspaceUpdates[assignedEmail][workspaceId] = (new List<ProjectTask>(), new List<ProjectTask>(), new List<ProjectViewDto>());
                    }

                    assigneeWorkspaceUpdates[assignedEmail][workspaceId].Projects.Add(project);
                }
            }

            // Cache workspace members to minimize repository calls
            var workspaceIds = projects.Select(p => p.WorkspaceId).Distinct();
            var workspaceMembersDict = new Dictionary<int, List<string>>();
            foreach (var workspaceId in workspaceIds)
            {
                var members = await _assignWorkspaceRepository.GetWorkspaceMemberEmails(workspaceId);
                // Validate and trim emails
                workspaceMembersDict[workspaceId] = members
                    .Select(m => m.Trim())
                    .Where(m => IsValidEmail(m))
                    .ToList();
            }

            // Send personalized updates to each assignee
            foreach (var (email, workspaceUpdates) in assigneeWorkspaceUpdates)
            {
                // Gather unique workspace member emails for the assignee's workspaces
                var relatedWorkspaceIds = workspaceUpdates.Keys;
                var ccSet = new HashSet<string>();
                foreach (var workspaceId in relatedWorkspaceIds)
                {
                    if (workspaceMembersDict.TryGetValue(workspaceId, out var members))
                    {
                        foreach (var member in members)
                        {
                            if (!string.Equals(member, email, StringComparison.OrdinalIgnoreCase))
                            {
                                ccSet.Add(member);
                            }
                        }
                    }
                }
                var ccList = ccSet.ToList();

                // Prepare parameters with workspace-level grouping as HTML
                var workspaceDetailsHtml = await GenerateWorkspaceDetailsHtml(workspaceUpdates);

                var parameters = new Dictionary<string, string>
                {
                    { "WorkspaceDetails", workspaceDetailsHtml }
                };

                var emailTemplate = await _aiService.GenerateEmailTemplate(
                    "Daily Project Updates Summary",
                    parameters
                );

                try
                {
                    await SendEmailAsync(email, ccList, emailTemplate.Subject, emailTemplate.HtmlBody);
                }
                catch (FormatException ex)
                {
                    // Log the invalid email address and continue
                    // Example: _logger.LogError(ex, $"Invalid email address: {email}");
                    continue;
                }
            }
        }

        private async Task<string> GenerateWorkspaceDetailsHtml(Dictionary<int, (List<ProjectTask> DueTasks, List<ProjectTask> UpcomingTasks, List<ProjectViewDto> Projects)> workspaceUpdates)
        {
            var sb = new StringBuilder();

            foreach (var (workspaceId, updates) in workspaceUpdates)
            {
                var workspace = await _workspaceRepository.GetById(workspaceId);
                if (workspace == null) continue;

                sb.AppendLine($"<div class=\"workspace\">");
                sb.AppendLine($"  <h3>{workspace.Title}</h3>");

                // Overdue Tasks
                sb.AppendLine($"  <h4>Overdue Tasks</h4>");
                sb.AppendLine($"  <p>{FormatTaskList(updates.DueTasks)}</p>");

                // Upcoming Tasks
                sb.AppendLine($"  <h4>Upcoming Tasks</h4>");
                sb.AppendLine($"  <p>{FormatTaskList(updates.UpcomingTasks)}</p>");

                // Active Projects
                sb.AppendLine($"  <h4>Active Projects</h4>");
                sb.AppendLine($"  <p>{FormatProjectListFromDto(updates.Projects)}</p>");

                sb.AppendLine($"</div>");
            }

            return sb.ToString();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string FormatTaskList(List<ProjectTask> tasks)
        {
            if (!tasks.Any()) return "No tasks";
            return string.Join("<br>", tasks.Select(t =>
                $"• {t.Message} (Due: {t.DueDate:MMM dd, yyyy}) - {t.Complexity}"));
        }

        private string FormatProjectListFromDto(List<ProjectViewDto> projects)
        {
            if (!projects.Any()) return "No active projects";
            return string.Join("<br>", projects.Select(p =>
                $"• {p.Subject} (Due: {p.CompletionDate:MMM dd, yyyy}) - {p.Priority}"));
        }
    }

    public class EmailSettings
    {
        public string SmtpServer { get; set; }
        public int SmtpPort { get; set; }
        public string SenderEmail { get; set; }
        public string SenderPassword { get; set; }
        public string SenderName { get; set; }
    }
}