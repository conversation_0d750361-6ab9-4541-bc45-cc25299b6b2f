namespace ProjectApp.Core.Models
{
    public class ChatResponse
    {
        public Guid Id { get; set; }
        public Guid ChatHistoryId { get; set; }
        public string Response { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsSelected { get; set; }
        public bool IsRegenerated { get; set; }
        public string ChatSources { get; set; }
        public string ResponseType { get; set; } // For view rendering: "sqlView", "emailView", "simpleView"
    }
}