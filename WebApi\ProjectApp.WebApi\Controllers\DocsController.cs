﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DocsController : ControllerBase
    {
        private readonly IDocsRepository _docsRepository;
        private readonly IFileRepository _fileRepository;
        private readonly AIService _aiService;
        private readonly IUrlService _urlService;

        public DocsController(IDocsRepository docsRepository, IFileRepository fileRepository, AIService aiService, IUrlService urlService)
        {
            _docsRepository = docsRepository;
            _fileRepository = fileRepository;
            _aiService = aiService;
            _urlService = urlService;
        }

        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<Docs>>> GetAll()
        {
            var res = await _docsRepository.GetAll();
            return Ok(res);
        }

        [HttpGet("GetById")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<ViewDocsDto>> GetById(int id)
        {
            var res = await _docsRepository.GetById(id);
            return Ok(res);
        }

        [HttpGet("GetByWorkspaceName")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<List<Docs>>> GetByWorkspaceName(string workspaceName)
        {
            var res = await _docsRepository.GetByWorkspaceName(workspaceName);
            return Ok(res);
        }

        [HttpPost("CreateOrUpdate")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Docs>> CreateOrUpdate(CreateDocsDto request)
        {
            var res = await _docsRepository.CreateOrUpdate(request);
            return Ok(res);
        }

        [HttpDelete("Delete")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Docs>> Delete(int id)
        {
            var res = await _docsRepository.Delete(id);
            return Ok(res);
        }

        [HttpPost("UpdateFavoriteStatus")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> UpdateFavoriteStatus(UpdateFavoriteDto request)
        {
            var res = await _docsRepository.UpdateFavoriteStatus(request);
            return Ok(res);
        }

        [HttpPost("TrackDocumentOpen")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> TrackDocumentOpen(TrackDocumentOpenDto request)
        {
            var res = await _docsRepository.TrackDocumentOpen(request.Id);
            return Ok(res);
        }

        [HttpGet("GetRecentlyOpened")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<Docs>>> GetRecentlyOpened(string workspaceName = null, int limit = 10)
        {
            var res = await _docsRepository.GetRecentlyOpenedDocs(workspaceName, limit);
            return Ok(res);
        }

        [HttpGet("GetFavorites")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<Docs>>> GetFavorites(string workspaceName = null)
        {
            var res = await _docsRepository.GetFavoriteDocs(workspaceName);
            return Ok(res);
        }
    }
}
