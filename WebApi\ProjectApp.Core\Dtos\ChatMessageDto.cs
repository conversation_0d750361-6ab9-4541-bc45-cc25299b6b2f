using ProjectApp.Core.Models;

namespace ProjectApp.Core.Dtos
{
    public class ChatMessageDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsPinned { get; set; }
        public bool IsFavorite { get; set; }
        public bool IsArchived { get; set; }
    }

    public class ChatListResponseDto
    {
        public List<ChatMessage> Messages { get; set; }
        public bool HasMoreMessages { get; set; }
    }

    public class ChatHistoryDto
    {
        public Guid Id { get; set; }
        public string Message { get; set; }
        public string ModelName { get; set; }
        public string AgentName { get; set; }
        public List<ChatResponseDto> Responses { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsEdited { get; set; }
        public Guid? OriginalMessageId { get; set; }
    }

    public class ChatResponseDto
    {
        public Guid Id { get; set; }
        public string Response { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsSelected { get; set; }
        public bool IsRegenerated { get; set; }
        public List<ChatSource> ChatSources { get; set; }
        public string ResponseType { get; set; } // For view rendering: "sqlView", "emailView", "simpleView"
    }

    public class ChatSource
    {
        public string Source { get; set; }
        public List<ChatSourceDescription> ChatSourceDescriptions { get; set; }
    }
    public class ChatSourceDescription
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public string URL { get; set; }
    }

    public class ChatHistoryResponseDto
    {
        public Guid Id { get; set; }
        public string Title { get; set; }
        public DateTime CreatedDate { get; set; }
        public List<ChatHistoryDto> History { get; set; }
    }

    public class ChatRequestDto
    {
        public string Message { get; set; }
        //public string ModelName { get; set; }
        public string AgentName { get; set; }
        public string Workspace { get; set; }
        //public bool IsAdmin { get; set; }
    }

    public class ChatContinueRequestDto
    {
        public Guid ChatMessageId { get; set; }
        public string Message { get; set; }
        public string Workspace { get; set; }
        public string AgentName { get; set; }
        //public bool IsAdmin { get; set; }
    }

    public class ChatEditRequestDto
    {
        public Guid ChatMessageId { get; set; }
        public Guid ChatHistoryId { get; set; }
        public string NewMessage { get; set; }
        public string Workspace { get; set; }
        //public bool IsAdmin { get; set; }
    }

    public class RegenerateResponseRequestDto
    {
        public Guid ChatMessageId { get; set; }
        public Guid ChatHistoryId { get; set; }
        public string OldMessage { get; set; }
        public string OldResponse { get; set; }
        public string Workspace { get; set; }
        //public bool IsAdmin { get; set; }
    }

    public class UpdateChatStatusRequestDto
    {
        public Guid ChatMessageId { get; set; }
        public bool? IsPinned { get; set; }
        public bool? IsFavorite { get; set; }
        public bool? IsArchived { get; set; }
    }

    public class SaveBlogRequestDto
    {
        public string Url { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
    }
}