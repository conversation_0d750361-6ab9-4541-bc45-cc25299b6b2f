using Hangfire;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Services
{
    public class AgentLogProcessor
    {
        private readonly IAgentLogRepository _agentLogRepository;
        private readonly AIService _aiService;
        private readonly ICustomerRepository _customerRepository;

        public AgentLogProcessor(IAgentLogRepository agentLogRepository, AIService aiService, ICustomerRepository customerRepository)
        {
            _agentLogRepository = agentLogRepository;
            _aiService = aiService;
            _customerRepository = customerRepository;
        }

        public async Task ProcessPendingLogs()
        {
            var pendingLogs = await _agentLogRepository.GetPendingLogs();
            
            foreach (var log in pendingLogs)
            {
                await _agentLogRepository.UpdateLogStatus(log.Id, "Executing");

                try
                {
                    switch (log.FunctionName)
                    {
                        case "create_customer":
                            var createParams = JsonSerializer.Deserialize<Customer>(log.Parameters);
                            await _customerRepository.CreateAsync(createParams);
                            break;

                        case "update_customer":
                            var updateParams = JsonSerializer.Deserialize<Customer>(log.Parameters);
                            await _customerRepository.UpdateAsync(updateParams);
                            break;

                        case "delete_customer":
                            var deleteParams = JsonSerializer.Deserialize<DeleteCustomerParams>(log.Parameters);
                            await _customerRepository.DeleteAsync(deleteParams.Id);
                            break;
                    }

                    await _agentLogRepository.UpdateLogStatus(log.Id, "Executed");
                }
                catch (Exception ex)
                {
                    await _agentLogRepository.UpdateLogStatus(log.Id, "Failed", ex.Message);
                }
            }
        }
    }

    public class GenerateWorkspaceParams
    {
        public string Subject { get; set; }
        public string Description { get; set; }
        public List<string> FileNames { get; set; }
    }

    public class GenerateTasksParams
    {
        public string Subject { get; set; }
        public string Description { get; set; }
        public int WorkspaceId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime CompletionDate { get; set; }
        public List<string> FileNames { get; set; }
    }

    public class ToolsAgentParams
    {
        public string Question { get; set; }
    }

    public class CreateCustomerParams
    {
        public string Name { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        // Add other customer properties
    }

    public class UpdateCustomerParams
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        // Add other customer properties
    }

    public class DeleteCustomerParams
    {
        public int Id { get; set; }
    }
} 