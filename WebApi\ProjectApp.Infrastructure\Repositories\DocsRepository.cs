﻿using Dapper;
using Microsoft.Extensions.Configuration;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;

namespace ProjectApp.Infrastructure.Repositories
{
    public class DocsRepository(IDbConnection _dbConnection, AIService _aiService, 
        IFileRepository _fileRepository, IUrlService _urlService) : IDocsRepository
    {

        public async Task<Docs> CreateOrUpdate(CreateDocsDto request)
        {
            var existingDoc = await _dbConnection.QueryFirstOrDefaultAsync<Docs>(
                "SELECT * FROM DocsData WHERE Title = @Title AND Id != @Id",
                new { Title = request.Title, Id = request.Id }
            );

            if (existingDoc != null)
            {
                throw new Exception("Doc already exists");
            }

            var extractedContent = await ExtractContent(request.Content);
            var docs = new Docs
            {
                Id = request.Id,
                Title = request.Title,
                Content = request.Content,
                ExtractedContent = extractedContent,
                WorkspaceName = request.WorkspaceName,
                Files = ""
            };
            //var fileResponse = new ResponseMessage();
            //if (request.FilesToAdd != null)
            //{
            //    if (request.FilesToAdd.Count > 0)
            //    {
            //        fileResponse = await _fileRepository.UploadFiles(request.FilesToAdd);
            //        if (fileResponse.IsError == true)
            //        {
            //            throw new Exception(fileResponse.Message);
            //        }  
            //    }
            //}

            if (request.Id == 0)
            {
                docs.CreatedAt = DateTime.Now;
                //docs.Files = fileResponse.Message;
                docs.Files = request.FilesToAdd;

                var insertQuery = @"INSERT INTO DocsData (Title, Content, ExtractedContent, CreatedAt, WorkspaceName, Files) OUTPUT INSERTED.Id VALUES (@Title, @Content, @ExtractedContent, @CreatedAt, @WorkspaceName, @Files)";
                docs.Id = await _dbConnection.ExecuteScalarAsync<int>(insertQuery, docs);
            }
            else
            {
                docs.UpdatedAT = DateTime.Now;


               

                var existingFiles = await _dbConnection.QueryFirstOrDefaultAsync<string>(
                    "SELECT Files FROM DocsData WHERE Id = @Id",
                    new { Id = request.Id }
                );
                var filesList = existingFiles.Split(", ").ToList();



                //if (request.FilesToAdd != null)
                //{
                //    var newFilesList = fileResponse.Message.Split(", ").ToList();                    
                //    filesList.AddRange(newFilesList);

                //}

                if (!string.IsNullOrEmpty(request.FilesToAdd))
                {
                    var newFilesList = request.FilesToAdd.Split(", ").ToList();
                    filesList.AddRange(newFilesList);
                }

                if (request.FilesToDelete != null)
                {
                    if (request.FilesToDelete.Count > 0)
                    {
                        var responseMessage = await _fileRepository.DeleteFiles(request.FilesToDelete);
                        filesList.RemoveAll(f => request.FilesToDelete.Contains(f));
                    }
                }
                docs.Files = string.Join(", ", filesList);

                var updateQuery = @"UPDATE DocsData SET Title = @Title, Content = @Content, ExtractedContent = @ExtractedContent, UpdatedAT = @UpdatedAT, WorkspaceName = @WorkspaceName, Files = @Files WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(updateQuery, docs);

                await _aiService.DeleteSingleMemory(request.Id.ToString(), "DocsData");
            }

            var content = $"Title = {docs.Title} and Description = {extractedContent}";
            var memoryTags = new List<MemoryTag>
            {
                new MemoryTag { Name = "WorkspaceName", Value = request.WorkspaceName }
            };
            await _aiService.AddMemory(content, docs.Id.ToString(), "DocsData", memoryTags);
            return docs;
        }

        public async Task<Docs> Delete(int id)
        {
            var doc = await _dbConnection.QueryFirstOrDefaultAsync<Docs>("SELECT * FROM DocsData WHERE Id = @Id", new { Id = id });

            if (doc == null)
            {
                throw new Exception("Could not find Doc");
            }

            var deleteQuery = "DELETE FROM DocsData WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(deleteQuery, new { Id = id });
            await _aiService.DeleteSingleMemory(id.ToString(), "DocsData");

            if (!string.IsNullOrEmpty(doc.Files))
            {
                var files = doc.Files.Split(", ", StringSplitOptions.RemoveEmptyEntries);
                await _fileRepository.DeleteFiles(files.ToList());
            }
            return doc;
        }

        public async Task<List<Docs>> GetAll()
        {
            var docs = await _dbConnection.QueryAsync<Docs>("SELECT * FROM DocsData");
            return docs.ToList();
        }

        public async Task<ViewDocsDto> GetById(int id)
        {
            var doc = await _dbConnection.QueryFirstOrDefaultAsync<Docs>("SELECT * FROM DocsData WHERE Id = @Id", new { Id = id });

            if (doc == null)
            {
                throw new Exception("Could not find Doc");
            }

            var viewDocsDto = new ViewDocsDto
            {
                Id = doc.Id,
                Title = doc.Title,
                Content = doc.Content,
                WorkspaceName = doc.WorkspaceName,
                Files = new List<Files>(),
                IsFavorite = doc.IsFavorite,
                LastOpenedAt = doc.LastOpenedAt
            };

            if (!string.IsNullOrEmpty(doc.Files))
            {
                var fileNames = doc.Files.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var fileName in fileNames)
                {
                    var description = await _fileRepository.GetDescription(fileName.Trim());
                    
                    // Get the file URL using the URL service
                    var filePath = _urlService.GetFileUrl(fileName.Trim());

                    viewDocsDto.Files.Add(new Files
                    {
                        FileName = fileName.Trim(),
                        FilePath = filePath,
                        Description = description ?? "No description available"
                    });
                }
            }

            return viewDocsDto;
        }

        public async Task<List<Docs>> GetByWorkspaceName(string workspaceName)
        {
            var docs = await _dbConnection.QueryAsync<Docs>(
                "SELECT * FROM DocsData WHERE WorkspaceName = @WorkspaceName",
                new { WorkspaceName = workspaceName }
            );

            if (docs == null || !docs.Any())
            {
                throw new Exception("Could not find any documents for the given WorkspaceId");
            }

            return docs.ToList();
        }

        public async Task<ResponseMessage> UpdateFavoriteStatus(UpdateFavoriteDto request)
        {
            var doc = await _dbConnection.QueryFirstOrDefaultAsync<Docs>(
                "SELECT * FROM DocsData WHERE Id = @Id",
                new { Id = request.Id }
            );

            if (doc == null)
            {
                return new ResponseMessage
                {
                    IsError = true,
                    Message = "Document not found."
                };
            }

            var updateQuery = "UPDATE DocsData SET IsFavorite = @IsFavorite WHERE Id = @Id";
            var result = await _dbConnection.ExecuteAsync(updateQuery, new { Id = request.Id, IsFavorite = request.IsFavorite });

            return new ResponseMessage
            {
                IsError = result <= 0,
                Message = result > 0 ? "Favorite status updated successfully." : "Failed to update favorite status."
            };
        }

        public async Task<ResponseMessage> TrackDocumentOpen(int id)
        {
            var doc = await _dbConnection.QueryFirstOrDefaultAsync<Docs>(
                "SELECT * FROM DocsData WHERE Id = @Id",
                new { Id = id }
            );

            if (doc == null)
            {
                return new ResponseMessage
                {
                    IsError = true,
                    Message = "Document not found."
                };
            }

            var now = DateTime.Now;
            var updateQuery = "UPDATE DocsData SET LastOpenedAt = @LastOpenedAt WHERE Id = @Id";
            var result = await _dbConnection.ExecuteAsync(updateQuery, new { Id = id, LastOpenedAt = now });
            
            return new ResponseMessage
            {
                IsError = result <= 0,
                Message = result > 0 ? "Document opened successfully." : "Failed to track document open."
            };
        }

        public async Task<List<Docs>> GetRecentlyOpenedDocs(string workspaceName = null, int limit = 10)
        {
            string sql;
            object parameters;

            if (string.IsNullOrEmpty(workspaceName))
            {
                sql = @"SELECT TOP (@Limit) * FROM DocsData 
                      WHERE LastOpenedAt IS NOT NULL 
                      ORDER BY LastOpenedAt DESC";
                parameters = new { Limit = limit };
            }
            else
            {
                sql = @"SELECT TOP (@Limit) * FROM DocsData 
                      WHERE LastOpenedAt IS NOT NULL AND WorkspaceName = @WorkspaceName
                      ORDER BY LastOpenedAt DESC";
                parameters = new { Limit = limit, WorkspaceName = workspaceName };
            }

            var docs = await _dbConnection.QueryAsync<Docs>(sql, parameters);
            return docs.ToList();
        }

        public async Task<List<Docs>> GetFavoriteDocs(string workspaceName = null)
        {
            string sql;
            object parameters;

            if (string.IsNullOrEmpty(workspaceName))
            {
                sql = "SELECT * FROM DocsData WHERE IsFavorite = 1 ORDER BY Title";
                parameters = new { };
            }
            else
            {
                sql = "SELECT * FROM DocsData WHERE IsFavorite = 1 AND WorkspaceName = @WorkspaceName ORDER BY Title";
                parameters = new { WorkspaceName = workspaceName };
            }

            var docs = await _dbConnection.QueryAsync<Docs>(sql, parameters);
            return docs.ToList();
        }

        public async Task<string> ExtractContent(string content)
        {
            var extractedTextBuilder = new System.Text.StringBuilder();

            using (var jsonDoc = JsonDocument.Parse(content))
            {
                void Traverse(JsonElement element)
                {
                    if (element.ValueKind == JsonValueKind.Object)
                    {
                        // Handle link blocks specifically
                        if (element.TryGetProperty("type", out var typeProp) && typeProp.GetString() == "link")
                        {
                            if (element.TryGetProperty("data", out var dataProp))
                            {
                                // Extract link URL
                                if (dataProp.TryGetProperty("link", out var linkProp))
                                {
                                    extractedTextBuilder.AppendLine(linkProp.GetString());
                                }

                                // Extract link metadata
                                if (dataProp.TryGetProperty("meta", out var metaProp))
                                {
                                    if (metaProp.TryGetProperty("title", out var titleProp))
                                    {
                                        extractedTextBuilder.AppendLine(titleProp.GetString());
                                    }
                                    if (metaProp.TryGetProperty("description", out var descProp))
                                    {
                                        extractedTextBuilder.AppendLine(descProp.GetString());
                                    }
                                }
                            }
                            return;
                        }

                        foreach (var property in element.EnumerateObject())
                        {
                            if (property.Name == "text" || property.Name == "content")
                            {
                                if (property.Value.ValueKind == JsonValueKind.String)
                                {
                                    extractedTextBuilder.AppendLine(property.Value.GetString());
                                }
                            }
                            Traverse(property.Value);
                        }
                    }
                    else if (element.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var item in element.EnumerateArray())
                        {
                            Traverse(item);
                        }
                    }
                }

                Traverse(jsonDoc.RootElement);
            }

            return extractedTextBuilder.ToString();
        }

    }
}
