﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using AutoMapper;
using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver.Core.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ApiCredentialsRepository(HttpClient _httpClient, IDbConnection _dbConnetion, IMapper _mapper) : IApiCredentialsRepository
    {
        public async Task<Guid> Create(ApiCredentialsDto credentialsDto)
        {
            var credentials = _mapper.Map<ApiCredentials>(credentialsDto); // Map DTO to Model
            credentials.Id = Guid.NewGuid(); // Generate GUID in code
            var sql = "INSERT INTO ApiCredentials (Id, TokenUrl, ApiKey, HasCustomModels) VALUES (@Id, @TokenUrl, @A<PERSON><PERSON>ey, @HasCustomModels); SELECT @Id;";
            return await _dbConnetion.ExecuteScalarAsync<Guid>(sql, credentials);
        }

        public async Task<bool> Delete(Guid id)
        {
            var sql = "DELETE FROM ApiCredentials WHERE Id = @Id";
            var rowsAffected = await _dbConnetion.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<IEnumerable<ApiCredentials>> GetAll()
        {
            var sql = "SELECT * FROM ApiCredentials";
            return await _dbConnetion.QueryAsync<ApiCredentials>(sql);
        }

        public async Task<ApiCredentials> GetById(Guid id)
        {
            var sql = "SELECT * FROM ApiCredentials WHERE Id = @Id";
            return await _dbConnetion.QueryFirstOrDefaultAsync<ApiCredentials>(sql, new { Id = id });
        }

        public async Task<bool> IsValidCredentials(string tokenUrl, string apiKey)
        {
            try
            {
                var request = BuildHttpRequest(tokenUrl, apiKey);
                var response = await _httpClient.SendAsync(request);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<string>> GetModelsFromApi(string tokenUrl, string apiKey)
        {
            var request = BuildHttpRequest(tokenUrl, apiKey);
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(json);

            var modelsElement = jsonDoc.RootElement.TryGetProperty("data", out var data) ? data : jsonDoc.RootElement.GetProperty("models");
            var isGoogle = new Uri(tokenUrl.ToLower()).Host.Contains("generativelanguage.googleapis.com");

            var models = modelsElement
                .EnumerateArray()
                .Select(m =>
                {
                    var modelName = m.TryGetProperty("id", out var id) ? id.GetString() : m.GetProperty("name").GetString();
                    // Strip "models/" prefix for Google Gemini
                    return isGoogle && modelName.StartsWith("models/") ? modelName.Substring("models/".Length) : modelName;
                })
                .ToList();

            return models;
        }

        private HttpRequestMessage BuildHttpRequest(string tokenUrl, string apiKey)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, tokenUrl);
            var uri = new Uri(tokenUrl.ToLower());

            // Custom logic for famous AI providers
            if (uri.Host.Contains("openai.com"))
            {
                // OpenAI: Bearer token
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
            }
            else if (uri.Host.Contains("generativelanguage.googleapis.com"))
            {
                // Google Gemini: API key as query param
                var uriBuilder = new UriBuilder(tokenUrl);
                var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                query["key"] = apiKey;
                uriBuilder.Query = query.ToString();
                request.RequestUri = uriBuilder.Uri;
            }
            else if (uri.Host.Contains("deepseek.com"))
            {
                // DeepSeek: Bearer token
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
            }
            else if (uri.Host.Contains("x.ai"))
            {
                // xAI: Bearer token
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
            }
            else
            {
                // Default to Bearer token for unknown providers
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);
            }

            return request;
        }
    }
}
