﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IMemoryRepository
    {
        Task<List<Memory>> GetAllMemories();
        Task<Memory> GetMemoryById(Guid id);
        Task<Memory> CreateOrEditMemory(MemoryDto memory);
        Task<Memory> DeleteMemory(Guid id);
    }
}
