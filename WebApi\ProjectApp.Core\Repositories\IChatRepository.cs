using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IChatRepository
    {
        Task<(ChatMessage, ChatHistories)> SaveChatMessageAsync(string userEmail, string message, string response, string workspaceName, string agentName, string modelName, string responseType, List<ChatSource> chatSources);
        Task<ChatHistories> AddToChatHistoryAsync(Guid chatMessageId, string message, string response, string agentName, string modelName, string responseType, List<ChatSource> chatSources = null);
        Task<ChatHistories> EditChatHistoryAsync(Guid chatMessageId, Guid chatHistoryId, string newMessage, string newResponse);
        Task<ChatResponse> RegenerateResponseAsync(Guid chatHistoryId, string newResponse);
        Task<ChatMessage> GetChatMessageAsync(Guid id, string userEmail);
        Task<IEnumerable<ChatHistories>> GetChatHistoryAsync(Guid chatMessageId, string userEmail);
        Task<(IEnumerable<ChatMessage> Messages, int TotalCount)> GetUserChatMessagesAsync(string workspace, string userEmail, int pageSize, int pageNumber);
        Task<string> GetDemoResponseAsync(string message);
        
        // New methods for chat management
        Task<ChatMessage> PinChatAsync(Guid chatMessageId, string userEmail, bool isPinned);
        Task<ChatMessage> FavoriteChatAsync(Guid chatMessageId, string userEmail, bool isFavorite);
        Task<ChatMessage> ArchiveChatAsync(Guid chatMessageId, string userEmail, bool isArchived);
        Task<ChatMessage> UpdateChatStatusAsync(Guid chatMessageId, string userEmail, bool? isPinned, bool? isFavorite, bool? isArchived);
        Task<IEnumerable<ChatMessage>> GetPinnedChatsAsync(string workspace, string userEmail);
        Task<IEnumerable<ChatMessage>> GetFavoriteChatsAsync(string workspace, string userEmail);
        Task<IEnumerable<ChatMessage>> GetArchivedChatsAsync(string workspace, string userEmail);
    }
} 