﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MemoryController : ControllerBase
    {
        private readonly IMemoryRepository _memoryRepository;
        public MemoryController(IMemoryRepository memoryRepository)
        {
            _memoryRepository = memoryRepository;
        }
        [HttpGet("GetAll")]
        public async Task<ActionResult<List<Memory>>> GetAll()
        {
            var memories = await _memoryRepository.GetAllMemories();
            return Ok(memories);
        }
        [HttpGet("GetById")]
        public async Task<ActionResult<Memory>> GetById(Guid id)
        {
            var memory = await _memoryRepository.GetMemoryById(id);
            if (memory == null)
                return NotFound(new { IsError = true, Message = "Memory not found" });

            return Ok(memory);
        }
        [HttpPost("CreateOrEdit")]
        public async Task<ActionResult<Memory>> CreateOrEdit(MemoryDto memoryDto)
        {
            try
            {
                var result = await _memoryRepository.CreateOrEditMemory(memoryDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { IsError = true, Message = ex.Message });
            }
        }

        [HttpDelete("Delete")]
        public async Task<ActionResult<Memory>> Delete(Guid id)
        {
            var existingMemory = await _memoryRepository.GetMemoryById(id);
            if (existingMemory == null)
                return NotFound(new { IsError = true, Message = "Memory not found" });
            var result = await _memoryRepository.DeleteMemory(id);
            return Ok(result);
        }
    }
}
