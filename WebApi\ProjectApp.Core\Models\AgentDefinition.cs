﻿using System;
using System.Text.Json.Serialization;

namespace ProjectApp.Core.Models
{
    public class AgentDefinition
    {
        public Guid Guid { get; set; }
        public string AgentName { get; set; }
        public string Instructions { get; set; }
        public string UserInstructions { get; set; }
        public string ModelName { get; set; }
        public string Workspace { get; set; }

        [JsonIgnore] // Hide from JSON serialization
        public string Tools { get; set; } // Internal storage as comma-separated string

        // This is the property we want to expose to the API
        [JsonPropertyName("tools")] // This will show up as "tools" in JSON
        public string[] ToolsArray
        {
            get => Tools?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();
            set => Tools = value != null ? string.Join(',', value) : null;
        }
    }
}
