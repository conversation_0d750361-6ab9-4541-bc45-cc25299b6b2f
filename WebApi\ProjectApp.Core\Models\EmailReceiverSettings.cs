namespace ProjectApp.Core.Models
{
    public class EmailReceiverSettings
    {
        public string ImapServer { get; set; } = string.Empty;
        public int ImapPort { get; set; } = 993;
        public bool EnableSsl { get; set; } = true;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string InboxFolder { get; set; } = "INBOX";
        public int CheckIntervalMinutes { get; set; } = 5;
        public bool IsEnabled { get; set; } = false;
    }
}
