﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using Dapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using MongoDB.Driver.Core.Configuration;
using System.Data;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Authorization;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ApiCredentialsController(IApiCredentialsRepository _apiCredentialsRepository, IModelDetailsRepository _modelDetailsRepository, IDbConnection _dbConnection) : ControllerBase
    {

        [HttpPost("Create")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> Create(ApiCredentialsDto credentials)
        {
            try
            {
                // Save credentials first
                var credentialId = await _apiCredentialsRepository.Create(credentials);

                List<string> models;
                string provider = ExtractProviderFromUrl(credentials.TokenUrl);

                if (credentials.HasCustomModels && credentials.CustomModels != null && credentials.CustomModels.Any())
                {
                    // Use custom models provided by the user
                    models = credentials.CustomModels;
                }
                else
                {
                    // Get models from API
                    models = await _apiCredentialsRepository.GetModelsFromApi(credentials.TokenUrl, credentials.ApiKey);
                }

                // Create model details
                var modelDetailsList = models.Select(model => new ModelDetails
                {
                    ModelName = provider + "_" + model,
                    ModelProvider = provider,
                    IsActive = true,
                    IsCustom = credentials.HasCustomModels,
                    ApiCredentialsId = credentialId
                }).ToList();

                await _modelDetailsRepository.CreateRangeAsync(modelDetailsList);

                return Ok(new ResponseMessage { IsError = false, Message = $"Credentials created successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Error creating credentials: {ex.Message}" });
            }
        }

        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<ApiCredentials>>> GetAll()
        {
            var credentialsList = await _apiCredentialsRepository.GetAll();
            return Ok(credentialsList);
        }
        [HttpGet("GetById/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiCredentials>> GetById(Guid id)
        {
            var credentials = await _apiCredentialsRepository.GetById(id);
            if (credentials == null) return NotFound("Credentials not found");
            return Ok(credentials);
        }

        [HttpPost("validate")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> Validate(ApiCredentialsDto credentials)
        {
            var isValid = await _apiCredentialsRepository.IsValidCredentials(credentials.TokenUrl, credentials.ApiKey);
            if (!isValid)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = "Invalid credentials" });
            }
            return Ok(new ResponseMessage { IsError = false, Message = "Credentials is valid" });
        }

        [HttpDelete("Delete/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> Delete(Guid id)
        {
            // Get the provider from the credentials to delete associated models
            string provider;
            var sql = "SELECT TokenUrl FROM ApiCredentials WHERE Id = @Id";
            var tokenUrl = await _dbConnection.QuerySingleOrDefaultAsync<string>(sql, new { Id = id });
            if (tokenUrl == null) return NotFound(new ResponseMessage { IsError = true, Message = "Credentials not found" });
            provider = ExtractProviderFromUrl(tokenUrl);

            try
            {
                // Delete associated ModelDetails first
                await _modelDetailsRepository.DeleteByProvider(provider);

                // Delete ApiCredentials
                var deleted = await _apiCredentialsRepository.Delete(id);
                if (!deleted) return NotFound(new ResponseMessage { IsError = true, Message = "Credentials not found" });

                return Ok(new ResponseMessage { IsError = false, Message = "Credentials and associated models deleted successfully" });
            }
            catch (Exception ex)
            {
                // Check if the exception contains information about foreign key constraint violation
                if (ex.Message.Contains("REFERENCE constraint"))
                {
                    return BadRequest(new ResponseMessage { IsError = true, Message = "Cannot delete the credentials because there are associated models in Agent. Please remove the associations before deleting." });
                }
                else
                {
                    return StatusCode(500, new ResponseMessage { IsError = true, Message = "An error occurred while deleting the credentials. Please try again later." });
                }
            }
        }

        private string ExtractProviderFromUrl(string tokenUrl)
        {
            var uri = new Uri(tokenUrl.ToLower());
            if (uri.Host.Contains("azure.com")) return "Azure";
            if (uri.Host.Contains("openai.com")) return "OpenAI";
            if (uri.Host.Contains("generativelanguage.googleapis.com")) return "GoogleAI";
            if (uri.Host.Contains("localai")) return "LocalAI";
            return "Unknown";
        }

        [HttpPost("ResyncModels/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> ResyncModels(Guid id)
        {
            try
            {
                // Get the API credentials
                var credentials = await _apiCredentialsRepository.GetById(id);
                if (credentials == null)
                    return NotFound(new ResponseMessage { IsError = true, Message = "API credentials not found" });

                // If this connection uses custom models, don't resync
                if (credentials.HasCustomModels)
                {
                    return Ok(new ResponseMessage
                    {
                        IsError = false,
                        Message = "This connection uses custom models and cannot be resynced automatically."
                    });
                }

                // Get the provider
                var provider = ExtractProviderFromUrl(credentials.TokenUrl);

                // Get current models from the API
                var currentModels = await _apiCredentialsRepository.GetModelsFromApi(credentials.TokenUrl, credentials.ApiKey);
                if (currentModels == null || !currentModels.Any())
                    return BadRequest(new ResponseMessage { IsError = true, Message = "No models found from the API" });

                currentModels = currentModels.Select(model => $"{provider}_{model}").ToList();
                // Get existing models for this provider from the database
                var existingModels = await _modelDetailsRepository.GetByProviderAsync(provider);
                var existingModelNames = existingModels.Select(m => m.ModelName).ToList();

                // Find new models that need to be added
                var newModels = currentModels.Where(m => !existingModelNames.Contains(m)).ToList();

                // Create new model details for the new models
                if (newModels.Any())
                {
                    var newModelDetailsList = newModels.Select(model => new ModelDetails
                    {
                        ModelName = model,
                        ModelProvider = provider,
                        IsActive = true,
                        IsCustom = false,
                        ApiCredentialsId = id
                    }).ToList();

                    await _modelDetailsRepository.CreateRangeAsync(newModelDetailsList);
                }

                // Remove models that no longer exist in the API (but don't remove custom models)
                var removedCount = await _modelDetailsRepository.DeleteByProviderExcept(provider, currentModels);

                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = $"Resync completed successfully. Added {newModels.Count} new models and removed {removedCount} obsolete models."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Error resyncing models: {ex.Message}" });
            }
        }

        [HttpPost("ResyncAllModels")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> ResyncAllModels()
        {
            try
            {
                // Get all API credentials
                var allCredentials = await _apiCredentialsRepository.GetAll();
                if (!allCredentials.Any())
                    return NotFound(new ResponseMessage { IsError = true, Message = "No API credentials found" });

                int totalNewModels = 0;
                int totalRemovedModels = 0;
                var results = new List<string>();

                // Process each credential
                foreach (var credentials in allCredentials)
                {
                    try
                    {
                        // Get the provider
                        var provider = ExtractProviderFromUrl(credentials.TokenUrl);
                        // Skip connections with custom models
                        if (credentials.HasCustomModels)
                        {
                            results.Add($"Provider {provider}: Skipped (uses custom models)");
                            continue;
                        }

                        // Get current models from the API
                        var currentModels = await _apiCredentialsRepository.GetModelsFromApi(credentials.TokenUrl, credentials.ApiKey);
                        if (currentModels == null || !currentModels.Any())
                        {
                            results.Add($"Provider {provider}: No models found from the API");
                            continue;
                        }
                        currentModels = currentModels.Select(model => $"{provider}_{model}").ToList();

                        // Get existing models for this provider from the database
                        var existingModels = await _modelDetailsRepository.GetByProviderAsync(provider);
                        var existingModelNames = existingModels.Select(m => m.ModelName).ToList();

                        // Find new models that need to be added
                        var newModels = currentModels.Where(m => !existingModelNames.Contains(m)).ToList();

                        // Create new model details for the new models
                        if (newModels.Any())
                        {
                            var newModelDetailsList = newModels.Select(model => new ModelDetails
                            {
                                ModelName = model,
                                ModelProvider = provider,
                                IsActive = true,
                                IsCustom = false,
                                ApiCredentialsId = credentials.Id
                            }).ToList();

                            await _modelDetailsRepository.CreateRangeAsync(newModelDetailsList);
                            totalNewModels += newModels.Count;
                        }

                        // Remove models that no longer exist in the API (but don't remove custom models)
                        var removedCount = await _modelDetailsRepository.DeleteByProviderExcept(provider, currentModels);
                        totalRemovedModels += removedCount;

                        results.Add($"Provider {provider}: Added {newModels.Count} new models and removed {removedCount} obsolete models");
                    }
                    catch (Exception ex)
                    {
                        results.Add($"Provider {ExtractProviderFromUrl(credentials.TokenUrl)}: Error - {ex.Message}");
                    }
                }

                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = $"Resync completed. Total: Added {totalNewModels} new models and removed {totalRemovedModels} obsolete models.\n\nDetails:\n{string.Join("\n", results)}"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Error resyncing models: {ex.Message}" });
            }
        }

        [HttpPost("AddCustomModels/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> AddCustomModels(Guid id, [FromBody] List<string> modelNames)
        {
            try
            {
                // Get the API credentials  
                var credentials = await _apiCredentialsRepository.GetById(id);
                if (credentials == null)
                    return NotFound(new ResponseMessage { IsError = true, Message = "API credentials not found" });

                // Get the provider  
                var provider = ExtractProviderFromUrl(credentials.TokenUrl);
                modelNames = modelNames.Select(model => $"{provider}_{model}").ToList();


                // Get existing custom models for this connection  
                var existingModels = await _modelDetailsRepository.GetByApiCredentialsIdAsync(id);
                var existingModelNames = existingModels.Where(m => m.IsCustom).Select(m => m.ModelName).ToList();


                // Determine models to add and models to delete  
                var modelsToAdd = modelNames.Except(existingModelNames).ToList();
                var modelsToDelete = existingModelNames.Except(modelNames).ToList();

                // Delete models that are no longer needed  
                if (modelsToDelete.Any())
                {
                    await _modelDetailsRepository.DeleteByModelNameAsync(modelsToDelete);
                }

                // Add new models  
                if (modelsToAdd.Any())
                {
                    var newModelDetailsList = modelsToAdd.Select(model => new ModelDetails
                    {
                        ModelName = model,
                        ModelProvider = provider,
                        IsActive = true,
                        IsCustom = true,
                        ApiCredentialsId = id
                    }).ToList();

                    await _modelDetailsRepository.CreateRangeAsync(newModelDetailsList);
                }

                // Update the credentials to mark as having custom models  
                var sql = "UPDATE ApiCredentials SET HasCustomModels = 1 WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(sql, new { Id = id });

                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = $"Custom models updated successfully. Added {modelsToAdd.Count} models and removed {modelsToDelete.Count} models."
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Error updating custom models: {ex.Message}" });
            }
        }
    }
}
