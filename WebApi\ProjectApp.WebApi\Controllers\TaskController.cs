﻿using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Repositories;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TaskController : ControllerBase
    {
        private readonly ITaskRepository _taskRepository;

        public TaskController(ITaskRepository taskRepository)
        {
            _taskRepository = taskRepository;
        }
        [HttpGet("GetAllProjectTasks")]
        public async Task<ActionResult<List<ProjectTask>>> GetAllProjectTasks()
        {
            var res = await _taskRepository.GetAllProjectTasks();
            return Ok(res);
        }
        [HttpGet("GetByProjectId")]
        public async Task<ActionResult<List<ProjectTask>>> GetByProjectId(int projectId)
        {
            var res = await _taskRepository.GetByProjectId(projectId);
            return Ok(res);
        }
        [HttpGet("GetTaskById")]
        public async Task<ActionResult<ProjectTask>> GetTaskById(int id)
        {
            var res = await _taskRepository.GetTaskById(id);
            return Ok(res);
        }
        [HttpPost("CreateOrUpdate")]
        public async Task<ActionResult<ProjectTask>> CreateOrUpdate([FromBody] ProjectTask request)
        {
            var res = await _taskRepository.CreateOrUpdate(request);
            return Ok(res);
        }
        [HttpDelete("Delete")]
        public async Task<ActionResult<ProjectTask>> Delete(int id)
        {
            var res = await _taskRepository.Delete(id);
            return Ok(res);
        }
        [HttpPut("UpdateTaskStatus")]
        public async Task<ActionResult<ProjectTask>> UpdateTaskStatus(int taskId, string isCompleted)
        {
            var res = await _taskRepository.UpdateTaskStatus(taskId, isCompleted);
            return Ok(res);
        }

        [HttpGet("GetTodayProjectTasks")]
        public async Task<ActionResult<List<ProjectTask>>> GetTodayProjectTasks()
        {
            var res = await _taskRepository.GetTodayProjectTasks();
            return Ok(res);
        }
        [HttpGet("GetUpcomingProjectTasks")]
        public async Task<ActionResult<List<ProjectTask>>> GetUpcomingProjectTasks()
        {
            var res = await _taskRepository.GetUpcomingProjectTasks();
            return Ok(res);
        }
        [HttpGet("GetPastDueProjectTasks")]
        public async Task<ActionResult<List<ProjectTask>>> GetPastDueProjectTasks()
        {
            var res = await _taskRepository.GetPastDueProjectTasks();
            return Ok(res);
        }
        [HttpGet("GetProjectTasksByWorkspacesId")]
        public async Task<ActionResult<List<ProjectTask>>> GetProjectTasksByWorkspacesId(int workspacesId)
        {
            var selectQuery = @"SELECT * FROM ProjectTasks WHERE WorkspaceId = @WorkspaceId";
            var tasks = await _taskRepository.GetProjectTasksByWorkspacesId(workspacesId);
            return Ok(tasks);
        }



     


     

     

    }
}
