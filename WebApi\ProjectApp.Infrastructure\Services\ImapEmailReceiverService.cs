using MailKit.Net.Imap;
using MailKit.Search;
using MailKit.Security;
using MailKit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MimeKit;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.Services
{
    public class ImapEmailReceiverService : IEmailReceiverService, IDisposable
    {
        private readonly EmailReceiverSettings _settings;
        private readonly ILogger<ImapEmailReceiverService> _logger;
        private readonly HashSet<string> _processedMessageIds = new();

        public ImapEmailReceiverService(
            IOptions<EmailReceiverSettings> settings,
            ILogger<ImapEmailReceiverService> logger)
        {
            _settings = settings.Value;
            _logger = logger;
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var client = new ImapClient();
                await client.ConnectAsync(_settings.ImapServer, _settings.ImapPort, _settings.EnableSsl);
                await client.AuthenticateAsync(_settings.Email, _settings.Password);
                await client.DisconnectAsync(true);
                
                _logger.LogInformation("IMAP connection test successful");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "IMAP connection test failed");
                return false;
            }
        }        public async Task<List<ReceivedEmail>> GetNewEmailsAsync()
        {
            var emails = new List<ReceivedEmail>();

            try
            {
                using var client = new ImapClient();
                await client.ConnectAsync(_settings.ImapServer, _settings.ImapPort, _settings.EnableSsl);
                await client.AuthenticateAsync(_settings.Email, _settings.Password);

                var inbox = client.Inbox;
                await inbox.OpenAsync(FolderAccess.ReadWrite); // Changed to ReadWrite so we can mark as read

                // Search for unread messages from the last 7 days (to avoid processing very old emails)
                var query = SearchQuery.And(
                    SearchQuery.Not(SearchQuery.Seen),
                    SearchQuery.DeliveredAfter(DateTime.Now.AddDays(-7))
                );

                var uids = await inbox.SearchAsync(query);

                foreach (var uid in uids)
                {
                    var message = await inbox.GetMessageAsync(uid);
                    
                    var receivedEmail = ConvertToReceivedEmail(message);
                    emails.Add(receivedEmail);
                    
                    // Mark the email as read after processing to avoid reprocessing
                    await inbox.AddFlagsAsync(uid, MessageFlags.Seen, true);
                    
                    _logger.LogDebug($"Marked email {message.MessageId} as read");
                }

                await client.DisconnectAsync(true);
                
                _logger.LogInformation($"Retrieved {emails.Count} new emails");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving emails");
            }

            return emails;
        }

        private static ReceivedEmail ConvertToReceivedEmail(MimeMessage message)
        {
            var receivedEmail = new ReceivedEmail
            {
                MessageId = message.MessageId ?? Guid.NewGuid().ToString(),
                From = message.From.ToString(),
                To = message.To.ToString(),
                Subject = message.Subject ?? string.Empty,
                Body = message.TextBody ?? string.Empty,
                HtmlBody = message.HtmlBody ?? string.Empty,
                ReceivedDate = message.Date.DateTime,
                IsRead = false
            };

            // Extract attachments
            foreach (var attachment in message.Attachments)
            {
                if (attachment is MimePart mimePart && !string.IsNullOrEmpty(mimePart.FileName))
                {
                    receivedEmail.Attachments.Add(mimePart.FileName);
                }
            }

            return receivedEmail;
        }        public void Dispose()
        {
            // Clean disposal
            GC.SuppressFinalize(this);
        }
    }
}
