using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.AIServices
{
    public class BlogService
    {
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;
        private readonly AIAgents.AIAgentFactory _agentFactory;

        public BlogService(
            IAgentDefinitionRepository agentDefinitionRepository,
            AIAgents.AIAgentFactory agentFactory)
        {
            _agentDefinitionRepository = agentDefinitionRepository;
            _agentFactory = agentFactory;
        }

        public async Task<string> GenerateBlog(BlogRequest request)
        {
            // Get the blog generator agent
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName("BlogGeneratorAgent");
            if (agentDefinition == null)
            {
                throw new System.Exception("Blog generator agent configuration not found");
            }

            // Create agent definition DTO
            var agentDefinitionDto = new AgentDefinitionDto
            {
                AgentName = agentDefinition.AgentName,
                Instructions = agentDefinition.Instructions,
                ModelName = agentDefinition.ModelName,
                Tools = agentDefinition.ToolsArray
            };

            // Construct the message with blog details
            var messageContent = new StringBuilder();
            messageContent.AppendLine("Please generate a blog based on the following information and return it in Markdown format Starting with Title don't include backtick in starting and ending:");
            messageContent.AppendLine($"Title: {request.Title}");
            messageContent.AppendLine($"Description: {request.Description}");

            // Add links and their transcripts
            if (request.Links != null && request.Links.Any())
            {
                messageContent.AppendLine("Links:");
                foreach (var link in request.Links)
                {
                    messageContent.AppendLine($"- [Link]({link.Link}): {link.Transcript}");
                }
            }

            // Add image URLs as Markdown images
            if (request.ImageUrls != null && request.ImageUrls.Any())
            {
                messageContent.AppendLine("Images:");
                foreach (var imageUrl in request.ImageUrls)
                {
                    messageContent.AppendLine($"![Image]({imageUrl})");
                }
            }

            // Call the agent to generate blog
            var blogContent = await _agentFactory.CallAIAgentAsync(agentDefinition.AgentName, messageContent.ToString());
            
            return blogContent;
        }
    }
} 