﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IModelDetailsRepository
    {
        Task<Guid> Create(ModelDetails modelDetails);
        Task CreateRangeAsync(List<ModelDetails> modelDetailsList);
        Task<IEnumerable<ModelDetailsDto>> GetAll();
        Task<IEnumerable<ModelDetailsNameDto>> GetAllActiveModel();
        Task<ModelDetailsDto> GetByModelNameAsync(string modelName);
        Task<ResponseMessage> UpdateIsActive(string modelName, bool isActive);
        Task<int> DeleteByProvider(string modelProvider);
        Task<int> DeleteByProviderExcept(string modelProvider, List<string> modelNamesToKeep);
        Task<IEnumerable<EmbeddingModelDto>> GetAllEmbeddingModels();
        Task<ResponseMessage> SetEmbeddingToTrue(string modelName);
        Task<EmbeddingModelAndKey> GetActiveEmbeddingConfigAsync();
        Task<IEnumerable<ModelDetailsDto>> GetByProviderAsync(string modelProvider);
        Task<int> DeleteCustomModelsByApiCredentialsId(Guid apiCredentialsId);
        Task<IEnumerable<ModelDetailsDto>> GetByApiCredentialsIdAsync(Guid apiCredentialsId);
        Task<int> DeleteByModelNameAsync(List<string> modelNames);
    }
}
