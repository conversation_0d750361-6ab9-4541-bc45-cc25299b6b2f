using System;
using System.Linq;
using System.Text;
using UglyToad.PdfPig;
using UglyToad.PdfPig.Content;
using UglyToad.PdfPig.DocumentLayoutAnalysis.TextExtractor;

namespace ProjectApp.Infrastructure.Services
{
    public class PdfTextExtractor
    {
        /// <summary>
        /// Extracts text from a PDF file
        /// </summary>
        /// <param name="pdfBytes">The PDF file as a byte array</param>
        /// <returns>Extracted text content from the PDF</returns>
        public static string ExtractText(byte[] pdfBytes)
        {
            try
            {
                var result = new StringBuilder();

                // Use PdfPig to parse the PDF
                using (var document = PdfDocument.Open(pdfBytes))
                {
                    // Get the number of pages
                    int pageCount = document.NumberOfPages;
                    result.AppendLine($"PDF Document - {pageCount} page(s)");

                    // Process each page
                    for (int i = 1; i <= pageCount; i++)
                    {
                        var page = document.GetPage(i);

                        // Extract text using ContentOrderTextExtractor for better text ordering
                        var text = ContentOrderTextExtractor.GetText(page);

                        // Add page header and content
                        result.AppendLine($"--- Page {i} ---");
                        result.AppendLine(text);

                        // Try to detect and extract tables
                        ExtractTablesFromPage(page, result);
                    }
                }

                return result.ToString();
            }
            catch (Exception ex)
            {
                return $"Error extracting text from PDF: {ex.Message}";
            }
        }

        /// <summary>
        /// Simple method to attempt to extract tabular data from a page
        /// </summary>
        private static void ExtractTablesFromPage(Page page, StringBuilder result)
        {
            try
            {
                // Group words by their Y position (approximate rows)
                var wordsByRow = page.GetWords()
                    .GroupBy(w => Math.Round(w.BoundingBox.Bottom, 1))
                    .OrderByDescending(g => g.Key)
                    .ToList();

                // If we have what looks like rows of data with similar word counts
                if (wordsByRow.Count > 2)
                {
                    var avgWordsPerRow = wordsByRow.Average(g => g.Count());
                    var potentialTableRows = wordsByRow
                        .Where(g => g.Count() >= Math.Max(3, avgWordsPerRow * 0.7))
                        .ToList();

                    if (potentialTableRows.Count >= 3) // At least 3 rows to consider it a table
                    {
                        result.AppendLine("Detected Table:");
                        foreach (var row in potentialTableRows)
                        {
                            // Sort words by X position (left to right)
                            var rowText = string.Join(" | ", row.OrderBy(w => w.BoundingBox.Left).Select(w => w.Text));
                            result.AppendLine(rowText);
                        }
                    }
                }
            }
            catch
            {
                // Silently continue if table extraction fails
            }
        }
    }
}
