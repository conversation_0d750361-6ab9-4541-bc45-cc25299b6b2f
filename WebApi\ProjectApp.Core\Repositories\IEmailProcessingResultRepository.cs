using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IEmailProcessingResultRepository
    {
        Task<IEnumerable<EmailProcessingResult>> GetAllByUserEmailAsync(string userEmail);
        Task<IEnumerable<EmailProcessingResult>> GetRecentResultsAsync(string userEmail, int count = 50);
        Task<EmailProcessingResult> CreateAsync(EmailProcessingResult result);
        Task DeleteOldResultsAsync(DateTime olderThan); // For cleanup
    }
}
