﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IAssignWorkspaceRepository
    {
        Task<AssignWorkspace> AssignUser(int workspaceId, string email);
        Task<AssignWorkspace> RemoveUser(int workspaceId, string email);
        Task<List<UserDto>> GetUsersByWorkspaceId(int workspaceId);
        Task<bool> IsUserInAnyWorkspace(string email);
        Task<List<string>> GetWorkspaceMemberEmails(int workspaceId);
    }
}
