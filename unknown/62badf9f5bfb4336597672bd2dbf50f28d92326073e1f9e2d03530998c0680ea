﻿using Microsoft.SemanticKernel;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ProjectApp.Core.Dtos;
using System.Text.Json;
using ProjectApp.Infrastructure.Services;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class CustomerPlugin
    {
        private readonly ICustomerRepository _customerRepository;
        private readonly IAgentLogRepository _agentLogRepository;

        public CustomerPlugin(ICustomerRepository customerRepository, IAgentLogRepository agentLogRepository)
        {
            _customerRepository = customerRepository;
            _agentLogRepository = agentLogRepository;
        }

        [KernelFunction("get_customers")]
        [Description("Gets a list of all customers")]
        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            return await _customerRepository.GetAllAsync();
        }

        [KernelFunction("get_customer_by_id")]
        [Description("Gets a customer by their ID")]
        public async Task<Customer> GetCustomerByIdAsync(int id)
        {
            return await _customerRepository.GetByIdAsync(id);
        }

        [KernelFunction("create_customer")]
        [Description("Creates a new customer")]
        public async Task<Customer> CreateCustomerAsync(Customer customer)
        {

            var log = new AgentLog
            {
                AgentName = "CustomerAgent",
                FunctionName = "create_customer",
                Parameters = JsonSerializer.Serialize(customer),
                Status = "Created",
                CreatedAt = DateTime.UtcNow
            };

            await _agentLogRepository.CreateLog(log);
            return customer;
        }

        [KernelFunction("update_customer")]
        [Description("Updates an existing customer")]
        public async Task<string> UpdateCustomerAsync(Customer customer)
        {

            var log = new AgentLog
            {
                AgentName = "CustomerAgent",
                FunctionName = "update_customer",
                Parameters = JsonSerializer.Serialize(customer),
                Status = "Created",
                CreatedAt = DateTime.UtcNow
            };

            await _agentLogRepository.CreateLog(log);
            return $"Customer update request logged. Log ID: {log.Id}";
        }

        [KernelFunction("delete_customer")]
        [Description("Deletes a customer by their ID")]
        public async Task<string> DeleteCustomerAsync(int id)
        {
            var log = new AgentLog
            {
                AgentName = "CustomerAgent",
                FunctionName = "delete_customer",
                Parameters = JsonSerializer.Serialize(new DeleteCustomerParams { Id = id} ),
                Status = "Created",
                CreatedAt = DateTime.UtcNow
            };

            await _agentLogRepository.CreateLog(log);
            return $"Customer deletion request logged. Log ID: {log.Id}";
        }
    }
}
