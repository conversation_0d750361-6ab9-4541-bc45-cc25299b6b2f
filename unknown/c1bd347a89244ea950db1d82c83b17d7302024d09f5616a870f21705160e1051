# Agent Setup Guide

This guide explains how to set up agents with template arguments in your database.

## Agent Definition Table Structure

Your `AgentDefinitions` table should have the following columns:

| Column Name    | Data Type     | Description                                                |
|----------------|---------------|------------------------------------------------------------|
| Guid           | GUID/UUID     | Unique identifier for the agent                            |
| AgentName      | VARCHAR(100)  | Name of the agent (used to reference it in code)           |
| Instructions   | TEXT          | Template instructions with placeholders for arguments      |
| ModelName      | VARCHAR(50)   | Name of the model to use (e.g., "gpt-4o-mini")             |
| Workspace      | VARCHAR(100)  | Workspace the agent belongs to                             |
| Tools          | TEXT          | Comma-separated list of tools the agent can use            |
| ArgumentsJson  | TEXT          | JSON string containing default arguments for the agent     |

## Template Syntax for Instructions

When writing instructions, use the following syntax for template arguments:

```
{{$argumentName}}
```

For arrays or collections, you can use the Handlebars-style each helper:

```
{{#each arrayName}}
- Item: {{propertyName}}
{{/each}}
```

## Example Agent Definitions to Add

### 1. Agent Selector

```sql
INSERT INTO AgentDefinitions (Guid, AgentName, Instructions, ModelName, Workspace, Tools, ArgumentsJson)
VALUES (
    NEWID(), 
    'AgentSelector',
    'You are an agent selection expert. Your task is to analyze the user''s question and select the most appropriate agent from the available agents list.

The user''s question is: {{$userQuestion}}

Available agents:
{{#each availableAgents}}
- AgentName: {{AgentName}}
  Description: {{AgentDescription}}
{{/each}}

Review each agent''s name and description carefully to determine which one is best suited to handle the user''s question.
Return ONLY the name of the selected agent without any additional text or explanation.
If none of the available agents are suitable, return ''Default''.',
    'gpt-4o-mini',
    'System',
    '',
    '{"userQuestion":"","availableAgents":[]}'
);
```

### 2. Story Teller Agent

```sql
INSERT INTO AgentDefinitions (Guid, AgentName, Instructions, ModelName, Workspace, Tools, ArgumentsJson)
VALUES (
    NEWID(), 
    'StoryTellerAgent',
    'Tell a story about {{$topic}} that is {{$length}} sentences long.',
    'gpt-4o-mini',
    'Creative',
    '',
    '{"topic":"AI assistants","length":"3"}'
);
```

### 3. Developer Assistant Agent

```sql
INSERT INTO AgentDefinitions (Guid, AgentName, Instructions, ModelName, Workspace, Tools, ArgumentsJson)
VALUES (
    NEWID(), 
    'DeveloperAssistant',
    'You are a helpful coding assistant specialized in {{$language}}. Help the user with their coding questions, focusing on {{$framework}} development. Provide code examples when appropriate.',
    'gpt-4o-mini',
    'Developer',
    'CodeAnalyzerPlugin,DocumentationPlugin',
    '{"language":"C#","framework":".NET Core"}'
);
```

### 4. SQL Query Agent

```sql
INSERT INTO AgentDefinitions (Guid, AgentName, Instructions, ModelName, Workspace, Tools, ArgumentsJson)
VALUES (
    NEWID(), 
    'SqlQueryAgent',
    'You are a SQL expert. Help the user write and optimize SQL queries for {{$database}} databases. Consider performance implications and best practices.',
    'gpt-4o-mini',
    'Database',
    'SqlAnalyzerPlugin',
    '{"database":"SQL Server"}'
);
```

### 5. Data Analysis Agent

```sql
INSERT INTO AgentDefinitions (Guid, AgentName, Instructions, ModelName, Workspace, Tools, ArgumentsJson)
VALUES (
    NEWID(), 
    'DataAnalyst',
    'You are a data analysis expert. Analyze the data provided by the user and provide insights about {{$dataType}} data. Focus on {{$analysisType}} analysis.',
    'gpt-4o-mini',
    'Data',
    'DataVisualizationPlugin,StatisticsPlugin',
    '{"dataType":"financial","analysisType":"trend"}'
);
```

## How to Use in Code

When calling an agent, you can pass custom arguments:

```csharp
// Create a dictionary for arguments
var arguments = new Dictionary<string, object>
{
    { "topic", "space exploration" },
    { "length", "5" }
};

// Call the agent with the arguments
var response = await _aiAgentFactory.AgentResponseAsync("StoryTellerAgent", userQuestion, arguments);
```

## Agent Selection Process

The agent selection process works as follows:

1. Search memory for relevant agents based on the user's question
2. Parse the agent information into a structured format
3. Pass the structured data as arguments to the AgentSelector
4. The AgentSelector analyzes the user's question and available agents
5. The AgentSelector returns the name of the most appropriate agent
6. The selected agent is then used to respond to the user's question

## Debugging Tips

If the agent selection is not working as expected:

1. Check the console logs to see what agents are being found in memory
2. Verify the format of the agent information in memory
3. Make sure the AgentSelector instructions are properly formatted
4. Check that the arguments are being passed correctly
5. Verify that the agent names in memory match the agent names in the database

## Important Notes

1. Make sure your agent names are unique across the system
2. Template arguments in instructions must match the keys in the ArgumentsJson
3. The ArgumentsJson column should contain a valid JSON string
4. When adding new agents, make sure to update your memory index so they can be found by the AgentSelector
