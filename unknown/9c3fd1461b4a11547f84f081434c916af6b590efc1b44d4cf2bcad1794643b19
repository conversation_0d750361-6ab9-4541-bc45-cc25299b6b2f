using System;

namespace ProjectApp.Core.Models
{
    public class PromptLibrary
    {
        public int Id { get; set; }
        public string Prompt { get; set; }
        public string ShortMessage { get; set; } // Short description or name for the prompt
        public bool IsGlobal { get; set; } // True for admin-created global prompts, False for user-created local prompts
        public string WorkspaceName { get; set; } // For categorizing prompts (optional)
        public int UsageCount { get; set; } = 0; // To track how often a prompt is used
        public string UserEmail { get; set; } // Null for global prompts, user ID for local prompts
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }
    }
} 