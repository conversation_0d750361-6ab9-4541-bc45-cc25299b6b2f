﻿using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VectorSearchApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VectorSearchController(VectorSearchExample vectorSearchExample) : ControllerBase
    {
        [HttpPost("CreateVector")]
        public async Task<IActionResult> CreateVector()
        {
            await VectorSearchExample.CreateAndInsertVectorsAsync();
            return Ok("Vector inserted successfully.");
        }

        [HttpPost("CreateEmbedding")]
        public async Task<IActionResult> CreateEmbedding()
        {
            await vectorSearchExample.CreateAndInsertEmbeddingAsync();
            return Ok("Embedding inserted successfully.");
        }

        [HttpGet("ReadVectors")]
        public async Task<IActionResult> ReadVectors()
        {
            await VectorSearchExample.ReadVectorsAsync();
            return Ok();
        }

        [HttpGet("FindMatchingVectors")]
        public async Task<IActionResult> FindMatchingVectors([FromQuery] string text, [FromQuery] int howMany = 5)
        {
            var matchingVectors = await vectorSearchExample.GetMatching(howMany, text);
            return Ok(matchingVectors);
        }
    }
}
