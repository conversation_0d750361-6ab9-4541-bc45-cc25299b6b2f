﻿using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;


namespace ProjectApp.Infrastructure.Repositories
{
    public class TaskRespository :ITaskRepository
    {
        private readonly IDbConnection _dbConnection;

        public TaskRespository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }
        public async Task<ProjectTask> CreateOrUpdate(ProjectTask request)
        {
            if (request.Id == 0)
            {
                var insertQuery = @"INSERT INTO ProjectTasks (Message, IsCompleted, CreatedAt, ProjectId ,DueDate,AssignedEmails, Complexity,WorkspaceId) OUTPUT INSERTED.Id VALUES (@Message, @IsCompleted, @CreatedAt, @ProjectId,@DueDate,@AssignedEmails, @Complexity,@WorkspaceId)";

                request.Id = await _dbConnection.ExecuteScalarAsync<int>(insertQuery, request);
            }
            else
            {
                var updateQuery = @"UPDATE ProjectTasks SET Message = @Message, IsCompleted = @IsCompleted, CreatedAt = @CreatedAt, ProjectId = @ProjectId ,DueDate=@DueDate,AssignedEmails=@AssignedEmails, Complexity=@Complexity,WorkspaceId=@WorkspaceId WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(updateQuery, request);
            }
            return request;
        }

        public async Task<ProjectTask> Delete(int id)
        {
            var deleteQuery = @"DELETE FROM ProjectTasks WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(deleteQuery, new { Id = id });
            return null;
        }

        public async Task<List<ProjectTask>> GetAllProjectTasks()
        {
            var selectQuery = @"SELECT * FROM ProjectTasks";
            return (await _dbConnection.QueryAsync<ProjectTask>(selectQuery)).ToList();
        }
        public async Task<List<ProjectTask>> GetByProjectId(int projectId)
        {
            var selectQuery = @"SELECT * FROM ProjectTasks WHERE ProjectId = @ProjectId";
            return (await _dbConnection.QueryAsync<ProjectTask>(selectQuery, new { ProjectId = projectId })).ToList();
        }
        public async Task<ProjectTask> GetTaskById(int id)
        {
            var selectQuery = @"SELECT * FROM ProjectTasks WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<ProjectTask>(selectQuery, new { Id = id });
        }
        public async Task<ProjectTask> UpdateTaskStatus(int taskId, string IsCompleted)
        {
            var updateQuery = @"UPDATE ProjectTasks SET IsCompleted = @IsCompleted WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(updateQuery, new { Id = taskId, IsCompleted = IsCompleted });

            return await GetTaskById(taskId);
        }
        /*   public async Task<List<TaskDto>> GetTodayProjectTasks()
           {
               var selectQuery = @"SELECT * FROM ProjectTasks WHERE DueDate = @DueDate";
               return (await _dbConnection.QueryAsync<TaskDto>(selectQuery, new { DueDate = DateTime.Today })).ToList();
           }*/

        public async Task<List<ProjectTask>> GetTodayProjectTasks()
        {
            var startOfDay = DateTime.Today;  // 00:00:00
            var endOfDay = DateTime.Today.AddDays(1).AddMilliseconds(-1);  // 23:59:59.999

            var selectQuery = @"
        SELECT * 
        FROM ProjectTasks 
        WHERE DueDate >= @StartOfDay AND DueDate <= @EndOfDay";

            return (await _dbConnection.QueryAsync<ProjectTask>(
                selectQuery, new { StartOfDay = startOfDay, EndOfDay = endOfDay })
            ).ToList();
        }

        public async Task<List<ProjectTask>> GetUpcomingProjectTasks()
        {
            var startOfTomorrow = DateTime.Today.AddDays(1);  // 00:00:00 of tomorrow
            var endOfTomorrow = DateTime.Today.AddDays(2).AddMilliseconds(-1);  // 23:59:59.999 of tomorrow

            var selectQuery = @"
                    SELECT * 
                    FROM ProjectTasks 
                    WHERE DueDate >= @StartOfTomorrow AND DueDate <= @EndOfTomorrow";

            return (await _dbConnection.QueryAsync<ProjectTask>(
                selectQuery, new { StartOfTomorrow = startOfTomorrow, EndOfTomorrow = endOfTomorrow })
            ).ToList();
        }

        public async Task<List<ProjectTask>> GetPastDueProjectTasks()
        {
            var selectQuery = @"SELECT * FROM ProjectTasks WHERE DueDate < @DueDate";
            return (await _dbConnection.QueryAsync<ProjectTask>(selectQuery, new { DueDate = DateTime.Today })).ToList();
        }
        public async Task CreateProjectTasksFromAi(List<TaskDateDto> tasksDateDtos, int projectId, string assignedEmails)
        {
            var insertQuery = @"INSERT INTO ProjectTasks (Message, IsCompleted, CreatedAt, ProjectId, DueDate, AssignedEmails, Complexity) 
                                VALUES (@Message, @IsCompleted, @CreatedAt, @ProjectId, @DueDate, @AssignedEmails, @Complexity)";

            var tasks = tasksDateDtos.Select(tasksDateDto => new ProjectTask
            {
                Message = tasksDateDto.Task,
                IsCompleted = false,
                CreatedAt = DateTime.UtcNow,
                ProjectId = projectId,
                DueDate = tasksDateDto.DueDate,
                Complexity = tasksDateDto.Complexity,
                AssignedEmails = assignedEmails,
            }).ToList();

            await _dbConnection.ExecuteAsync(insertQuery, tasks);
        }

        public async Task<List<ProjectTask>> GetProjectTasksByWorkspacesId(int workspacesId)
        {
            var selectQuery = @"SELECT * FROM ProjectTasks WHERE WorkspaceId = @WorkspaceId";
            return (await _dbConnection.QueryAsync<ProjectTask>(selectQuery, new { WorkspaceId = workspacesId })).ToList();
        }

     
       
     
       
    }
}
