using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class PromptLibraryRepository : IPromptLibraryRepository
    {
        private readonly IDbConnection _dbConnection;

        public PromptLibraryRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<PromptLibraryResponseDto>> GetAllPromptsAsync(string workspaceName, string userEmail)
        {
            string sql;
            object parameters;

            if (userEmail == null)
            {
                if (string.IsNullOrEmpty(workspaceName))
                {
                    sql = "SELECT * FROM PromptLibrary ORDER BY CreatedAt DESC";
                    parameters = new { };
                }
                else
                {
                    sql = "SELECT * FROM PromptLibrary WHERE WorkspaceName = @WorkspaceName ORDER BY UsageCount DESC";
                    parameters = new { WorkspaceName = workspaceName };
                }
            }
            else
            {
                if (string.IsNullOrEmpty(workspaceName))
                {
                    sql = "SELECT * FROM PromptLibrary WHERE IsGlobal = 1 OR UserEmail = @UserEmail ORDER BY UserEmail DESC, UsageCount DESC";
                    parameters = new { UserEmail = userEmail };
                }
                else
                { 
                    sql = "SELECT * FROM PromptLibrary WHERE WorkspaceName = @WorkspaceName AND (IsGlobal = 1 OR UserEmail = @UserEmail) ORDER BY UserEmail DESC, UsageCount DESC";
                    parameters = new { WorkspaceName = workspaceName, UserEmail = userEmail };
                }
            }

            var prompts = await _dbConnection.QueryAsync<PromptLibrary>(sql, parameters);
            return prompts.Select(MapToResponseDto).ToList();
        }

        public async Task<PromptLibraryResponseDto> GetPromptByIdAsync(int id)
        {
            var sql = "SELECT * FROM PromptLibrary WHERE Id = @Id";
            var prompt = await _dbConnection.QueryFirstOrDefaultAsync<PromptLibrary>(sql, new { Id = id });
            
            if (prompt == null)
                return null;

            return MapToResponseDto(prompt);
        }

        public async Task<PromptLibraryResponseDto> CreateOrUpdatePromptAsync(CreateOrUpdatePromptDto prompt)
        {
            if (prompt.Id == 0)
            {
                // Create new prompt
                var entity = new PromptLibrary
                {
                    Prompt = prompt.Prompt,
                    ShortMessage = prompt.ShortMessage,
                    IsGlobal = prompt.IsGlobal,
                    UserEmail = prompt.UserEmail,
                    WorkspaceName = prompt.WorkspaceName,
                    CreatedAt = DateTime.Now
                };

                var sql = @"
                    INSERT INTO PromptLibrary (Prompt, IsGlobal, WorkspaceName, UserEmail, ShortMessage, UsageCount, CreatedAt)
                    OUTPUT INSERTED.Id
                    VALUES (@Prompt, @IsGlobal, @WorkspaceName, @UserEmail, @ShortMessage, 0, @CreatedAt)";

                entity.Id = await _dbConnection.ExecuteScalarAsync<int>(sql, entity);
                return MapToResponseDto(entity);
            }
            else
            {
                // Update existing prompt
                var existingPrompt = await _dbConnection.QueryFirstOrDefaultAsync<PromptLibrary>(
                    "SELECT * FROM PromptLibrary WHERE Id = @Id",
                    new { Id = prompt.Id }
                );

                if (existingPrompt == null)
                    return null;

                // Update properties
                existingPrompt.Prompt = prompt.Prompt;
                existingPrompt.IsGlobal = prompt.IsGlobal;
                existingPrompt.UserEmail = prompt.UserEmail;
                existingPrompt.WorkspaceName = prompt.WorkspaceName;
                existingPrompt.ShortMessage = prompt.ShortMessage;
                existingPrompt.UpdatedAt = DateTime.Now;

                var sql = @"
                    UPDATE PromptLibrary 
                    SET Prompt = @Prompt, 
                        IsGlobal = @IsGlobal, 
                        WorkspaceName = @WorkspaceName, 
                        UserEmail = @UserEmail, 
                        UpdatedAt = @UpdatedAt,
                        ShortMessage = @ShortMessage
                    WHERE Id = @Id";

                await _dbConnection.ExecuteAsync(sql, existingPrompt);
                return MapToResponseDto(existingPrompt);
            }
        }

        public async Task<bool> DeletePromptAsync(int id, string userEmail)
        {
            string sql;
            object parameters;

            if (userEmail == null)
            {
                // Admin can delete any prompt
                sql = "DELETE FROM PromptLibrary WHERE Id = @Id";
                parameters = new { Id = id };
            }
            else
            {
                // Users can only delete their own prompts
                sql = "DELETE FROM PromptLibrary WHERE Id = @Id AND UserEmail = @UserEmail";
                parameters = new { Id = id, UserEmail = userEmail };
            }

            var affectedRows = await _dbConnection.ExecuteAsync(sql, parameters);
            return affectedRows > 0;
        }

        public async Task<bool> IncrementUsageCountAsync(int id)
        {
            var sql = "UPDATE PromptLibrary SET UsageCount = UsageCount + 1 WHERE Id = @Id";
            var affectedRows = await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }

        // Helper method to map entity to DTO
        private PromptLibraryResponseDto MapToResponseDto(PromptLibrary prompt)
        {
            return new PromptLibraryResponseDto
            {
                Id = prompt.Id,
                Prompt = prompt.Prompt,
                ShortMessage = prompt.ShortMessage,
                IsGlobal = prompt.IsGlobal,
                WorkspaceName = prompt.WorkspaceName,
                UsageCount = prompt.UsageCount,
                UserEmail = prompt.UserEmail,
                CreatedAt = prompt.CreatedAt,
                UpdatedAt = prompt.UpdatedAt
            };
        }
    }
} 