﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace ProjectApp.Core.Models
{
    public class Docs
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string ExtractedContent { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAT { get; set; }
        public string WorkspaceName { get; set; }
        public string Files { get; set; }
        public bool IsFavorite { get; set; } = false;
        public DateTime? LastOpenedAt { get; set; }
    }
}
