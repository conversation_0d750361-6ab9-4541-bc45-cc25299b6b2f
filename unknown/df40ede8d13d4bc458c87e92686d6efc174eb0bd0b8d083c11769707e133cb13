﻿using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class AssignWorkspaceRepository(IDbConnection _dbConnection) : IAssignWorkspaceRepository
    {

        public async Task<List<UserDto>> GetUsersByWorkspaceId(int workspaceId)
        {
            var sql = @"
                SELECT ua.Name, ua.Email, ua.Role, ua.Skills
                FROM AssignWorkspaces aw
                INNER JOIN UserAccounts ua ON aw.Email = ua.Email
                WHERE aw.WorkspaceId = @WorkspaceId";

            var users = await _dbConnection.QueryAsync<RegisterDto>(sql, new { WorkspaceId = workspaceId });

            return users.Select(user => new UserDto
            {
                Name = user.Name,
                Email = user.Email,
                Roles = user.Role.Split(", ").ToList(),
                Skills = user.Skills
            }).ToList();
        }

        public async Task<AssignWorkspace> AssignUser(int workspaceId, string email)
        {
            var existingAssignment = await _dbConnection.QueryFirstOrDefaultAsync<AssignWorkspace>("SELECT * FROM AssignWorkspaces WHERE WorkspaceId = @WorkspaceId AND Email = @Email", new { WorkspaceId = workspaceId, Email = email });

            if (existingAssignment != null)
            {
                throw new Exception("Assignment already exists");
            }

            var existingEmail = await _dbConnection.QueryFirstOrDefaultAsync<string>("SELECT Email FROM UserAccounts WHERE Email = @Email", new { Email = email });

            if (existingEmail == null)
            {
                throw new Exception("Email does not exist");
            }

            var assignWorkspace = new AssignWorkspace
            {
                WorkspaceId = workspaceId,
                Email = email
            };

            var insertQuery = "INSERT INTO AssignWorkspaces (WorkspaceId, Email) OUTPUT INSERTED.Id VALUES (@WorkspaceId, @Email)";
            assignWorkspace.Id = await _dbConnection.ExecuteScalarAsync<int>(insertQuery, assignWorkspace);

            return assignWorkspace;
        }

        public async Task<AssignWorkspace> RemoveUser(int workspaceId, string email)
        {
            var assignment = await _dbConnection.QueryFirstOrDefaultAsync<AssignWorkspace>("SELECT * FROM AssignWorkspaces WHERE WorkspaceId = @WorkspaceId AND Email = @Email", new { WorkspaceId = workspaceId, Email = email });

            if (assignment == null)
            {
                throw new Exception("Could not find assignment");
            }

            var deleteQuery = "DELETE FROM AssignWorkspaces WHERE WorkspaceId = @WorkspaceId AND Email = @Email";
            await _dbConnection.ExecuteAsync(deleteQuery, new { WorkspaceId = workspaceId, Email = email });
            return assignment;
        }
        public async Task<bool> IsUserInAnyWorkspace(string email)
        {
            var query = "SELECT COUNT(1) FROM AssignWorkspaces WHERE Email = @Email";
            var count = await _dbConnection.ExecuteScalarAsync<int>(query, new { Email = email });
            return count > 0;
        }

        public async Task<List<string>> GetWorkspaceMemberEmails(int workspaceId)
        {
            var query = @"SELECT Email FROM AssignWorkspaces WHERE WorkspaceId = @WorkspaceId";
            var members = await _dbConnection.QueryAsync<string>(query, new { WorkspaceId = workspaceId });
            return members.ToList();
        }

    }
}
