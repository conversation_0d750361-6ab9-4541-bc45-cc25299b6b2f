﻿using Dapper;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ProjectCategoryRepository : IProjectCategoryRepository
    {
        private readonly IDbConnection _dbConnection;

        public ProjectCategoryRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<List<ProjectCategory>> GetAllAsync()
        {
            var sql = "SELECT * FROM ProjectCategories";
            var result = await _dbConnection.QueryAsync<ProjectCategory>(sql);
            return result.ToList();
        }

        public async Task<ProjectCategory> GetByIdAsync(int id)
        {
            var sql = "SELECT * FROM ProjectCategories WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<ProjectCategory>(sql, new { Id = id });
        }

        public async Task<ProjectCategory> GetByNameAsync(string name)
        {
            var sql = "SELECT * FROM ProjectCategories WHERE Name = @Name";
            return await _dbConnection.QueryFirstOrDefaultAsync<ProjectCategory>(sql, new { Name = name });
        }

        public async Task<ProjectCategory> CreateOrUpdateAsync(ProjectCategory projectCategory)
        {
            var existingCategory = await _dbConnection.QueryFirstOrDefaultAsync<ProjectCategory>(
                "SELECT * FROM ProjectCategories WHERE Name = @Name AND Id != @Id",
                new { Name = projectCategory.Name, Id = projectCategory.Id, WorkspaceId = projectCategory.WorkspaceId });

            if (existingCategory != null)
            {
                throw new ArgumentException("A project category with the same name already exists in the workspace.");
            }

            if (projectCategory.Id == 0)
            {
                var insertSql = "INSERT INTO ProjectCategories (Name, WorkspaceId) VALUES (@Name, @WorkspaceId); SELECT CAST(SCOPE_IDENTITY() as int)";
                var id = await _dbConnection.ExecuteScalarAsync<int>(insertSql, projectCategory);
                projectCategory.Id = id;
            }
            else
            {
                var updateSql = "UPDATE ProjectCategories SET Name = @Name, WorkspaceId = @WorkspaceId WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(updateSql, projectCategory);
            }
            return projectCategory;
        }

        public async Task<ProjectCategory> DeleteAsync(int id)
        {
            var sql = "DELETE FROM ProjectCategories WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return new ProjectCategory { Id = id };
        }
        public async Task<List<ProjectCategory>> GetAllByWorkspaceIdAsync(int workspaceId)
        {
            var sql = "SELECT * FROM ProjectCategories WHERE WorkspaceId = @WorkspaceId";
            var result = await _dbConnection.QueryAsync<ProjectCategory>(sql, new { WorkspaceId = workspaceId });
            return result.ToList();
        }
    }
}
