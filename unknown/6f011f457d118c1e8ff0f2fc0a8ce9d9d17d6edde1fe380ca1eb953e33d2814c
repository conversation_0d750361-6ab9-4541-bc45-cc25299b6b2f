﻿using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Authorization;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserAccountController(IUserAccountRepository _userAccountRepository) : ControllerBase
    {
        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<UserDto>>> GetAll()
        {
            var users = await _userAccountRepository.GetAll();
            return Ok(users);
        }

        [HttpGet("GetAllUsers")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<UserDto>>> GetAllUsers()
        {
            var users = await _userAccountRepository.GetAllUsers();
            return Ok(users);
        }

        [HttpPost("register")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Register(RegisterDto model)
        {
            try
            {
                await _userAccountRepository.Register(model);
                return Ok(new { message = "Account Added successfully" });
            }
            catch (Exception ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }

        // Keeping the login endpoint for backward compatibility, but it's recommended to use AuthController instead
        [HttpPost("Login")]
        [AllowAnonymous]
        public async Task<ActionResult<UserDto>> Login(LoginDto model)
        {
            var user = await _userAccountRepository.Login(model.Email, model.Password);
            if (user == null)
                return Unauthorized(new { message = "Invalid credentials" });
            return Ok(user);
        }

        [HttpDelete("DeleteUser")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteUser(string email)
        {
            try
            {
                await _userAccountRepository.DeleteUser(email);
                return Ok(new { message = "User deleted successfully" });
            }
            catch (Exception ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }

        [HttpPost("AssignRole")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> AssignRole(string email, string role)
        {
            try
            {
                await _userAccountRepository.AssignRole(email, role);
                return Ok(new { message = "Role assigned successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPost("RemoveRole")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> RemoveRole(string email, string role)
        {
            try
            {
                await _userAccountRepository.RemoveRole(email, role);
                return Ok(new { message = "Role removed successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        //[HttpGet("GetByRole")]
        //public async Task<ActionResult<List<UserDto>>> GetByRole(string role)
        //{
        //    try
        //    {
        //        var users = await _userAccountRepository.GetByRole(role);
        //        return Ok(users);
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(new { message = ex.Message });
        //    }
        //    }
        //}

        //[HttpPost("verify-otp")]
        //public async Task<ActionResult> VerifyOtp(VerifyOtpDto model)
        //{
        //    try
        //    {
        //        var result = await _userAccountRepository.VerifyOtp(model.Email, model.Otp);
        //        if (result)
        //            return Ok(new { message = "Account verified successfully" });
        //        return BadRequest(new { message = "Invalid OTP or OTP expired" });
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message);
        //    }
        //}

        //[HttpPost("resend-otp")]
        //public async Task<ActionResult> ResendOtp(ResendOtpDto model)
        //{
        //    try
        //    {
        //        var result = await _userAccountRepository.GenerateOtp(model.Email);
        //        if (result)
        //            return Ok(new { message = "OTP sent successfully" });
        //        return BadRequest(new { message = "Failed to send OTP" });
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message);
        //    }
        //}
    }
}
