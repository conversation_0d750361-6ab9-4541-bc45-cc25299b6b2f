﻿using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AssignWorkspaceController(IAssignWorkspaceRepository _assignWorkspaceRepository, IExtractEmailFromAccessor _emailExtract) : ControllerBase
    {

        [HttpPost("AssignUser")]
        public async Task<IActionResult> AssignUser(int workspaceId, string email)
        {
            try
            {
                var result = await _assignWorkspaceRepository.AssignUser(workspaceId, email);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpDelete("RemoveUser")]
        public async Task<IActionResult> RemoveUser(int workspaceId, string email)
        {
            try
            {
                var result = await _assignWorkspaceRepository.RemoveUser(workspaceId, email);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("GetUsersByWorkspaceId")]
        public async Task<ActionResult<List<UserDto>>> GetUsersByWorkspaceId(int workspaceId)
        {
            try { 
                var result = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspaceId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        [HttpGet("IsUserInAnyWorkspace")]
        public async Task<ActionResult<ResponseMessage>> IsUserInAnyWorkspace()
        {
            try
            {
                var email = _emailExtract.GetEmail();
                var result = await _assignWorkspaceRepository.IsUserInAnyWorkspace(email);
                return Ok(new ResponseMessage { IsError = !result, Message=""});
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

    }
}
