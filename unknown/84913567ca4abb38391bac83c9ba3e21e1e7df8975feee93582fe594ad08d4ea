﻿using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Repositories;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProjectCategoryController : ControllerBase
    {
        private readonly IProjectCategoryRepository _repository;

        public ProjectCategoryController(IProjectCategoryRepository repository)
        {
            _repository = repository;
        }

        [HttpGet("GetAllAsync")]
        public async Task<ActionResult<List<ProjectCategory>>> GetAll()
        {
            var categories = await _repository.GetAllAsync();
            return Ok(categories);
        }

        [HttpGet("GetByIdAsync/{id}")]
        public async Task<ActionResult<ProjectCategory>> GetById(int id)
        {
            var category = await _repository.GetByIdAsync(id);
            if (category == null)
            {
                return NotFound();
            }
            return Ok(category);
        }



        [HttpPost("CreateOrUpdateAsync")]
        public async Task<ActionResult<ProjectCategory>> Add(ProjectCategory projectCategory)
        {
            var category = await _repository.CreateOrUpdateAsync(projectCategory);
            return Ok(category);
        }

        [HttpDelete("Delete/{id}")]
        public async Task<ActionResult> Delete(int id)
        {
            var category = await _repository.GetByIdAsync(id);
            if (category == null)
            {
                return NotFound();
            }

            await _repository.DeleteAsync(id);
            return NoContent();
        }
        [HttpGet("GetAllByWorkspaceIdAsync/{workspaceId}")]
        public async Task<ActionResult<List<ProjectCategory>>> GetAllByWorkspaceId(int workspaceId)
        {
            var categories = await _repository.GetAllByWorkspaceIdAsync(workspaceId);
            return Ok(categories);
        }
    }
}
