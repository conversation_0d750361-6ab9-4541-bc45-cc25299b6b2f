﻿using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Services
{
    public class DailyInsightService
    {
        private readonly IDailyInsightAgentRepository _dailyInsightAgentRepository;
        private readonly AIAgentFactory _aiAgentFactory;

        public DailyInsightService(
            IDailyInsightAgentRepository dailyInsightAgentRepository,
            AIAgentFactory aiAgentFactory)
        {
            _dailyInsightAgentRepository = dailyInsightAgentRepository;
            _aiAgentFactory = aiAgentFactory;
        }

        // Process a single agent by ID
        public async Task<string> ProcessSingleAgentAsync(int agentId)
        {
            Console.WriteLine($"[{DateTime.Now}] Processing single agent with ID: {agentId}");

            try
            {
                var agent = await _dailyInsightAgentRepository.GetByIdAsync(agentId);
                if (agent == null)
                {
                    var errorMessage = $"Agent with ID {agentId} not found";
                    Console.WriteLine($"[{DateTime.Now}] {errorMessage}");
                    return errorMessage;
                }

                Console.WriteLine($"[{DateTime.Now}] Processing agent: {agent.AgentName} (ID: {agent.Id})");

                // Call the AI agent with the prompt
                var response = await _aiAgentFactory.CallAIAgentAsync(agent.AgentName, agent.Prompt);

                // Update the agent with the response
                await _dailyInsightAgentRepository.UpdateLastRunAsync(agent.Id, response);
                Console.WriteLine($"[{DateTime.Now}] Successfully processed agent: {agent.AgentName}");

                return response;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing agent with ID {agentId}: {ex.Message}";
                Console.WriteLine($"[{DateTime.Now}] {errorMessage}");
                Console.WriteLine($"[{DateTime.Now}] Stack trace: {ex.StackTrace}");

                // Don't update the agent here - let the caller decide what to do with the error
                throw new Exception(errorMessage, ex);
            }
        }

        public async Task SyncAllDailyInsights()
        {
            Console.WriteLine($"[{DateTime.Now}] Starting daily insights sync...");

            try
            {
                var agents = await _dailyInsightAgentRepository.GetAllAgentsForSyncAsync();
                Console.WriteLine($"[{DateTime.Now}] Found {agents.Count()} agents to sync");

                foreach (var agent in agents)
                {
                    try
                    {
                        Console.WriteLine($"[{DateTime.Now}] Processing agent: {agent.AgentName} (ID: {agent.Id})");

                        // Call the AI agent with the prompt
                        var response = await _aiAgentFactory.CallAIAgentAsync(agent.AgentName, agent.Prompt);

                        // Update the agent with the response
                        await _dailyInsightAgentRepository.UpdateLastRunAsync(agent.Id, response);
                        Console.WriteLine($"[{DateTime.Now}] Successfully processed agent: {agent.AgentName}");
                    }
                    catch (Exception ex)
                    {
                        // Log the error and continue with the next agent
                        Console.WriteLine($"[{DateTime.Now}] Error processing agent {agent.AgentName}: {ex.Message}");
                        Console.WriteLine($"[{DateTime.Now}] Stack trace: {ex.StackTrace}");

                        // Update with error message
                        await _dailyInsightAgentRepository.UpdateLastRunAsync(
                            agent.Id,
                            $"Error: {ex.Message}"
                        );
                    }
                }

                Console.WriteLine($"[{DateTime.Now}] Daily insights sync completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now}] Critical error in SyncAllDailyInsights: {ex.Message}");
                Console.WriteLine($"[{DateTime.Now}] Stack trace: {ex.StackTrace}");
                throw; // Rethrow to let Hangfire know the job failed
            }
        }

        // Method to be called by Hangfire
        [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 300, 600, 1200 })] // Retry after 5, 10, and 20 minutes
        [DisableConcurrentExecution(timeoutInSeconds: 600)] // Prevent concurrent execution for 10 minutes
        public async Task ProcessDailyInsightsJob()
        {
            var jobStartTime = DateTime.Now;
            Console.WriteLine($"[{jobStartTime}] Daily Insights job started");

            try
            {
                // Sync all daily insights
                await SyncAllDailyInsights();

                var jobEndTime = DateTime.Now;
                var duration = jobEndTime - jobStartTime;
                Console.WriteLine($"[{jobEndTime}] Daily Insights job completed successfully in {duration.TotalSeconds:F2} seconds");
            }
            catch (Exception ex)
            {
                // Log the error with detailed information
                var jobErrorTime = DateTime.Now;
                Console.WriteLine($"[{jobErrorTime}] Error in ProcessDailyInsightsJob: {ex.Message}");
                Console.WriteLine($"[{jobErrorTime}] Stack trace: {ex.StackTrace}");

                // Check for inner exceptions
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"[{jobErrorTime}] Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"[{jobErrorTime}] Inner stack trace: {ex.InnerException.StackTrace}");
                }

                throw; // Rethrow to let Hangfire handle retries
            }
        }

        //// Static method for Hangfire to call directly
        //[AutomaticRetry(Attempts = 3)]
        //[DisableConcurrentExecution(timeoutInSeconds: 300)]
        //public static async Task ProcessDailyInsightsJob(IServiceProvider serviceProvider)
        //{
        //    try
        //    {
        //        // Create a scope to resolve scoped services
        //        using (var scope = serviceProvider.CreateScope())
        //        {
        //            // Resolve the DailyInsightService from the service provider
        //            var service = scope.ServiceProvider.GetRequiredService<DailyInsightService>();

        //            // Call the instance method
        //            await service.SyncAllDailyInsights();
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        // Log the error
        //        Console.WriteLine($"Error in static ProcessDailyInsightsJob: {ex.Message}");
        //        throw; // Rethrow to let Hangfire handle retries
        //    }
        //}
    }
}
