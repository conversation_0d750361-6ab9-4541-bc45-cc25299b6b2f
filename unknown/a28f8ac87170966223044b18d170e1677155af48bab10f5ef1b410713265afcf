﻿using ProjectApp.Core.Models;
using ProjectApp.Core.Dtos;
using AutoMapper;
using Dapper;
using System.Data;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Services;
using Hangfire;
using ProjectApp.Infrastructure.AIAgents;
using Newtonsoft.Json;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ProjectRepository(IDbConnection _dbConnection, IMapper _mapper, IExtractEmailFromAccessor _emailExtract,
           AIService _aiService, IWorkspaceRepository _workspaceRepository,
           ITaskRepository _taskRepository, AIAgentFactory _aiAgenFactory, IProjectCategoryRepository _projectCategory) : IProjectRepository
    {

        private async Task<List<ProjectViewDto>> MapProjectsToDto(IEnumerable<(Project Project, string WorkspaceTitle, string ProjectCategory)> projects)
        {
            var projectViewDtos = new List<ProjectViewDto>();

            foreach (var (project, workspaceTitle, projectCategory) in projects)
            {
                //var matchedDocument = await _aiService.GetMatchedDocumentTitlesAndIds(project.Subject, project.Message, project.WorkspaceId);
                var projectViewDto = _mapper.Map<ProjectViewDto>(project);
                var fileNames = project.FileNames.Split("@#").ToList();
                projectViewDto.FilesName = fileNames;
                projectViewDto.WorkspaceTitle = workspaceTitle;
                projectViewDto.ProjectCategory = projectCategory;
                projectViewDto.MatchedDocuments = [];

                projectViewDtos.Add(projectViewDto);
            }

            return projectViewDtos;
        }

        public async Task<List<ProjectViewDto>> GetAll()
        {
            var sql = @"  
           SELECT   
               p.*,   
               w.Title AS WorkspaceTitle,  
               c.Name AS ProjectCategory  
           FROM Projects p  
           LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
           LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
           ORDER BY p.CompletionDate DESC";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            return await MapProjectsToDto(result);
        }

        public async Task<List<ProjectViewDto>> GetAllForUser()
        {
            var email = _emailExtract.GetEmail();
            var sql = @"  
           SELECT   
               p.*,   
               w.Title AS WorkspaceTitle,  
               c.Name AS ProjectCategory  
           FROM Projects p  
           LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
           LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
           WHERE p.UserEmail = @UserEmail  
           ORDER BY p.CreatedDate DESC";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                new { UserEmail = email },
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            return await MapProjectsToDto(result);
        }

        public async Task<List<ProjectViewDto>> GetAllUserByWorkspaceId(int workspaceId)
        {
            var email = _emailExtract.GetEmail();
            var sql = @"  
           SELECT   
               p.*,   
               w.Title AS WorkspaceTitle,  
               c.Name AS ProjectCategory  
           FROM Projects p  
           LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
           LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
           WHERE p.AssignedEmail = @AssignedEmail  
           AND p.WorkspaceId = @WorkspaceId  
           ORDER BY p.CompletionDate DESC";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                new { AssignedEmail = email, WorkspaceId = workspaceId },
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            return await MapProjectsToDto(result);
        }

        public async Task<List<ProjectViewDto>> GetForWorkspace(int workspaceId)
        {
            var sql = @"  
           SELECT   
               p.*,   
               w.Title AS WorkspaceTitle,  
               c.Name AS ProjectCategory  
           FROM Projects p  
           LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
           LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
           WHERE p.WorkspaceId = @WorkspaceId  
           ORDER BY p.CreatedDate DESC";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                new { WorkspaceId = workspaceId },
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            return await MapProjectsToDto(result);
        }



        public async Task<List<ProjectViewDto>> GetPastDueProjects(int workspaceId)
        {
            var sql = @"  
           SELECT   
               p.*,   
               w.Title AS WorkspaceTitle,  
               c.Name AS ProjectCategory  
           FROM Projects p  
           LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
           LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
           WHERE p.Status = 'Open'  
           AND p.WorkspaceId = @WorkspaceId  
           AND p.CompletionDate < @CurrentDate  
           ORDER BY p.CreatedDate DESC";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                new { CurrentDate = DateTime.UtcNow, WorkspaceId = workspaceId },
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            return await MapProjectsToDto(result);
        }

        public async Task<ProjectViewDto> GetById(int id)
        {
            var sql = @"  
           SELECT   
               p.*,   
               w.Title AS WorkspaceTitle,  
               c.Name AS ProjectCategory  
           FROM Projects p  
           LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
           LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
           WHERE p.Id = @Id";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                new { Id = id },
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            var projectDtos = await MapProjectsToDto(result);
            var projectDto = projectDtos.FirstOrDefault();
            projectDto.MatchedDocuments = await _aiService.GetMatchedDocumentTitlesAndIds(projectDto.Subject, projectDto.Message, projectDto.WorkspaceId);
            return projectDto;
        }

        public async Task<Project> CreateOrUpdate(ProjectDto request, string assigneeEmail = null)
        {
            Project project;

            if (request.Id == 0)
            {
                project = _mapper.Map<Project>(request);
                project.Status = "Open";
                project.CreatedDate = DateTime.Now;
                project.FileNames = string.Join("@#", request.FilesName);
                project.UserEmail = assigneeEmail == null ? _emailExtract.GetEmail() : assigneeEmail;

                string inputMessage = $"Title: {project.Subject}, Description: {project.Message}, Index: Workspaces";
                var workspaceAgentAnswer = await _aiAgenFactory.CallAIAgentAsync("WorkspaceAgent", inputMessage);
                var workspaceAndCategory = JsonConvert.DeserializeObject<Dictionary<string, int>>(workspaceAgentAnswer);
                if (workspaceAndCategory != null && workspaceAndCategory.ContainsKey("workspaceId") && workspaceAndCategory.ContainsKey("categoryId"))
                {
                    project.WorkspaceId = workspaceAndCategory["workspaceId"];
                    project.ProjectCategoryId = workspaceAndCategory["categoryId"];
                }

                if (project.WorkspaceId != 0)
                {
                    inputMessage = $"Title: {project.Subject}, Description: {project.Message}, workspace Id: {project.WorkspaceId}";
                    var assigneeEmailAnswer = await _aiAgenFactory.CallAIAgentAsync("SkillMatcherAgent", inputMessage);
                    var email = JsonConvert.DeserializeObject<Dictionary<string, string>>(assigneeEmailAnswer);
                    if (email != null && email.ContainsKey("email"))
                    {
                        project.AssignedEmail = email["email"];
                    }
                }
                else
                {
                    var defaultWorkspace = await _workspaceRepository.GetDefaultWorkspace();
                    project.WorkspaceId = defaultWorkspace?.Id ?? throw new Exception("Default workspace not found");
                    project.ProjectCategoryId = _projectCategory.GetAllByWorkspaceIdAsync(project.WorkspaceId).Result.FirstOrDefault()?.Id ?? throw new Exception("Default category not found");
                    project.AssignedEmail = "unassigned";
                }
                // Set temporary values for async processing  
                project.Summary = "Processing...";
                project.CompletionDate = DateTime.Now.AddDays(1); // Temporary date  

                var insertQuery = @"INSERT INTO Projects   
                       (Status, CreatedDate, CompletionDate, FileNames, UserEmail,   
                        AssignedEmail, Priority, Subject, Message, WorkspaceId, Summary, ProjectCategoryId)   
                       OUTPUT INSERTED.Id   
                       VALUES (@Status, @CreatedDate, @CompletionDate, @FileNames, @UserEmail,   
                               @AssignedEmail, @Priority, @Subject, @Message, @WorkspaceId, @Summary, @ProjectCategoryId)";

                project.Id = await _dbConnection.ExecuteScalarAsync<int>(insertQuery, project);

                // Fire background job for remaining AI tasks  
                BackgroundJob.Enqueue<ProjectBackgroundService>(x =>
                    x.ProcessProjectAfterCreation(project.Id));

                return project;
            }
            else
            {
                project = await _dbConnection.QueryFirstOrDefaultAsync<Project>("SELECT * FROM Projects WHERE Id = @Id", new { Id = request.Id });

                if (project == null)
                {
                    throw new Exception("Could not find Project");
                }

                _mapper.Map(request, project);
                project.CreatedDate = DateTime.Now;
                project.CompletionDate = DateTime.Now.AddDays(3);
                project.FileNames = string.Join("@#", request.FilesName);

                var updateQuery = "UPDATE Projects SET Status = @Status, CreatedDate = @CreatedDate, CompletionDate = @CompletionDate, Priority = @Priority, Subject = @Subject, FileNames = @FileNames, Message = @Message WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(updateQuery, project);
            }

            return project;
        }


        public async Task<Project> newProjectCreate(NewProjectDto request)
        {
            ProjectDto projectDto = new ProjectDto
            {
                Subject = request.Subject,
                Message = request.Message,
                Priority = request.Priority,
                FilesName = request.FilesName,
            };
            var res = await CreateOrUpdate(projectDto, request.UserEmail);
            return res;
        }

        public int GetWorkspaceId(string message)
        {
            var random = new Random();
            var workspaceId = random.Next(6, 9);
            return workspaceId;
        }

        public async Task<Project> Delete(int id)
        {
            var project = await _dbConnection.QueryFirstOrDefaultAsync<Project>("SELECT * FROM Projects WHERE Id = @Id", new { Id = id });

            if (project == null)
            {
                throw new Exception("Could not find Project");
            }

            var deleteQuery = "DELETE FROM Projects WHERE Id = @Id";
            await _dbConnection.ExecuteAsync(deleteQuery, new { Id = id });
            return project;
        }

        public async Task<ResponseMessage> ChangeStatus(string status, int id)
        {
            var project = await _dbConnection.QueryFirstOrDefaultAsync<Project>("SELECT * FROM Projects WHERE Id = @Id", new { Id = id });
            var responseMessage = new ResponseMessage();

            if (project == null)
            {
                responseMessage.IsError = true;
                responseMessage.Message = "Project not found";
            }
            else
            {
                project.Status = status;

                var updateQuery = "UPDATE Projects SET Status = @Status WHERE Id = @Id";
                await _dbConnection.ExecuteAsync(updateQuery, new { Status = status, Id = id });

                responseMessage.IsError = false;
                responseMessage.Message = "Status Updated";
            }

            return responseMessage;
        }

        public async Task<string> EmailForProjectAssigned()
        {
            // TASK: all get the userAccounts where there it that workspace members only  
            // TASK: if none of the user to assign task then return this <-------------- "Unassigned"  

            var users = await _dbConnection.QueryAsync<string>("SELECT Email FROM UserAccounts WHERE Role = 'User'");
            var assignedProjects = await _dbConnection.QueryAsync<dynamic>("SELECT AssignedEmail, COUNT(*) AS Count FROM Projects WHERE AssignedEmail IS NOT NULL AND Status = 'Open' GROUP BY AssignedEmail");

            var usersProjectCount = users.ToDictionary(email => email, email => 0);

            foreach (var assigned in assignedProjects)
            {
                usersProjectCount[assigned.AssignedEmail] = assigned.Count;
            }

            var selected = usersProjectCount
                 .OrderBy(kvp => kvp.Value)
                 .FirstOrDefault();

            return selected.Key; // Return the selected users's email  
        }
        public async Task<List<ProjectViewDto>> GetAllByCategoryName(string categoryName)
        {
            var sql = @"  
                SELECT   
                    p.*,   
                    w.Title AS WorkspaceTitle,  
                    c.Name AS ProjectCategory  
                FROM Projects p  
                LEFT JOIN Workspaces w ON p.WorkspaceId = w.Id  
                LEFT JOIN ProjectCategories c ON p.ProjectCategoryId = c.Id  
                WHERE c.Name = @CategoryName  
                ORDER BY p.CreatedDate DESC";

            var result = await _dbConnection.QueryAsync<Project, string, string, (Project, string, string)>(
                sql,
                (project, workspaceTitle, projectCategory) => (project, workspaceTitle, projectCategory),
                new { CategoryName = categoryName },
                splitOn: "WorkspaceTitle,ProjectCategory"
            );

            return await MapProjectsToDto(result);
        }

    }
}
