﻿namespace ProjectApp.Core.Dtos
{
    public class DailyInsightAgentDto
    {
        public string AgentName { get; set; }
        public string Prompt { get; set; }
        public string Title { get; set; }
    }

    public class DailyInsightAgentResponseDto
    {
        public int Id { get; set; }
        public string AgentName { get; set; }
        public string Prompt { get; set; }
        public string Title { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastRunAt { get; set; }
        public string LastResponse { get; set; }
    }
}
