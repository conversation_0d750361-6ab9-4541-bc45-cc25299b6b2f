using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace ProjectApp.Infrastructure.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUserAccountRepository _userRepository;
        private readonly JwtSettings _jwtSettings;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthService(
            IUserAccountRepository userRepository,
            IOptions<JwtSettings> jwtSettings,
            IHttpContextAccessor httpContextAccessor)
        {
            _userRepository = userRepository;
            _jwtSettings = jwtSettings.Value;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<TokenResponseDto> Login(LoginDto loginDto)
        {
            var user = await _userRepository.Login(loginDto.Email, loginDto.Password);
            if (user == null)
            {
                return null;
            }

            var token = GenerateJwtToken(user);
            return new TokenResponseDto
            {
                Token = token,
                Expiration = DateTime.UtcNow.AddDays(_jwtSettings.DurationInDays),
                User = user
            };
        }

        public async Task<TokenResponseDto> Register(RegisterDto registerDto)
        {
            await _userRepository.Register(registerDto);
            var user = await _userRepository.Login(registerDto.Email, registerDto.Password);
            
            var token = GenerateJwtToken(user);
            return new TokenResponseDto
            {
                Token = token,
                Expiration = DateTime.UtcNow.AddDays(_jwtSettings.DurationInDays),
                User = user
            };
        }

        public string GenerateJwtToken(UserDto user)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.Key);
            
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Name, user.Name)
            };

            // Add role claims
            foreach (var role in user.Roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role.Trim()));
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddDays(_jwtSettings.DurationInDays),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature),
                Issuer = _jwtSettings.Issuer,
                Audience = _jwtSettings.Audience
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        public UserDto GetCurrentUser()
        {
            var user = _httpContextAccessor.HttpContext.User;
            
            if (!user.Identity.IsAuthenticated)
                return null;

            var email = user.FindFirst(ClaimTypes.Email)?.Value;
            var name = user.FindFirst(ClaimTypes.Name)?.Value;
            var roles = user.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();

            return new UserDto
            {
                Email = email,
                Name = name,
                Roles = roles,
                Skills = ""  // Cannot retrieve skills from claims, need to retrieve from repository if needed
            };
        }
    }
} 