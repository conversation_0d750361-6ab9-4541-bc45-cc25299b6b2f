﻿using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Authorization;

namespace AdminPortalBackend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ProjectsController : ControllerBase
    {
        private readonly IProjectRepository _projectRepo;

        public ProjectsController(IProjectRepository projectRepo)
        {
            _projectRepo = projectRepo;
        }

        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<ProjectViewDto>>> GetAll()
        {
            var res = await _projectRepo.GetAll();
            return Ok(res);
        }
        
        [HttpGet("GetAllForUSer")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<List<ProjectViewDto>>> GetAllForUSer()
        {
            var res = await _projectRepo.GetAllForUser();
            return Ok(res);
        }
        
        [HttpGet("GetPastDueProjects")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<List<ProjectViewDto>>> GetPastDueProjects(int workspaceId)
        {
            var res = await _projectRepo.GetPastDueProjects(workspaceId);
            return Ok(res);
        }
        
        [HttpGet("GetAllUserByWorkspaceId")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<List<ProjectViewDto>>> GetAllUserByWorkspaceId(int workspaceId)
        {
            var res = await _projectRepo.GetAllUserByWorkspaceId(workspaceId);
            return Ok(res);
        }

        [HttpGet("GetForWorkspace")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<List<ProjectViewDto>>> GetForWorkspace(int workspaceId)
        {
            var res = await _projectRepo.GetForWorkspace(workspaceId);
            return Ok(res);
        }

        [HttpGet("GetById")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<ProjectViewDto>> GetById(int id)
        {
            var res = await _projectRepo.GetById(id);
            return Ok(res);
        }

        [HttpPost("CreateOrUpdate")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<Project>> CreateOrUpdate(ProjectDto request)
        {
            var res = await _projectRepo.CreateOrUpdate(request);
            return Ok(res);
        }

        [HttpPost("newProjectCreate")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<Project>> newProjectCreate(NewProjectDto request)
        {
            var res = await _projectRepo.newProjectCreate(request);
            return Ok(res);
        }


        [HttpDelete("Delete")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<Project>> Delete(int id)
        {
            var res = await _projectRepo.Delete(id);
            return Ok(res);
        }

        [HttpPost("ChangeStatus")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<ResponseMessage>> ChangeStatus(string status, int id)
        {
            var res = await _projectRepo.ChangeStatus(status, id);
            return Ok(res);
        }
        
        [HttpGet("GetAllByCategoryName")]
        [Authorize(Roles = "Admin,User")]
        public async Task<ActionResult<List<ProjectViewDto>>> GetAllByCategoryName(string categoryName)
        {
            var res = await _projectRepo.GetAllByCategoryName(categoryName);
            return Ok(res);
        }
    }
}
