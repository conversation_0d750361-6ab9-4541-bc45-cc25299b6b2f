﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Repositories;

namespace ProjectApp.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ProjectMemoryController(IProjectMemoryRepository _repository) : ControllerBase
    {

        [HttpPost("SaveProjectMemory")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProjectMemory>> SaveProjectMemory(ProjectMemory projectMemory)
        {
            var result = await _repository.SaveProjectMemoryAsync(projectMemory);
            return Ok(result);
        }

        [HttpGet("GetProjectMemoryById/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ProjectMemory>> GetProjectMemoryById(int id)
        {
            var result = await _repository.GetProjectMemoryByIdAsync(id);
            if (result == null)
            {
                return NotFound();
            }
            return Ok(result);
        }

        [HttpGet("GetAllProjectMemory")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<ProjectMemory>>> GetAllProjectMemory(string workspace)
        {
            var result = await _repository.GetAllProjectMemoriesAsync(workspace);
            return Ok(result);
        }

        [HttpDelete("DeleteProjectMemory/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteProjectMemory(int id)
        {
            await _repository.DeleteProjectMemoryAsync(id);
            return NoContent();
        }
    }
}

