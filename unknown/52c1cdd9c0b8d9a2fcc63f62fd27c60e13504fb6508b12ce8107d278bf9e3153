﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Models
{
    public class OpenAIOptions
    {
        [Required]
        public string ApiKey { get; set; } = string.Empty;

        [Required]
        public string ChatModelId { get; set; } = string.Empty;

        [Required]
        public string EmbeddingModelId { get; set; } = string.Empty;
    }
}
