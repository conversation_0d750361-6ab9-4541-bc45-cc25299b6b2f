﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using AutoMapper;
using Dapper;
using System.Data;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.Repositories
{
    public class ChatModelRepository : IChatModelRepository
    {
        private readonly IDbConnection _dbConnection;
        private readonly IMapper _mapper;

        public ChatModelRepository(IDbConnection dbConnection, IMapper mapper)
        {
            _dbConnection = dbConnection;
            _mapper = mapper;
        }

        public async Task<IEnumerable<ChatModel>> GetAllAsync()
        {
            var sql = "SELECT * FROM ChatModels";
            return await _dbConnection.QueryAsync<ChatModel>(sql);
        }

        public async Task<ChatModel> GetByModelIdAsync(string modelId)
        {
            var sql = "SELECT * FROM ChatModels WHERE ModelId = @ModelId";
            return await _dbConnection.QueryFirstOrDefaultAsync<ChatModel>(sql, new { ModelId = modelId });
        }

        public async Task<ChatModel> CreateOrUpdate(CreateChatModelDto createChatModelDto)
        {
            var existingChatModel = await GetByModelIdAsync(createChatModelDto.ModelId);
            if (existingChatModel == null)
            {
                var newChatModel = _mapper.Map<ChatModel>(createChatModelDto);
                return await Create(newChatModel);
            }
            else
            {
                _mapper.Map(createChatModelDto, existingChatModel);
                return await Update(existingChatModel);
            }
        }

        private async Task<ChatModel> Create(ChatModel chatModel)
        {
            var sql = @"INSERT INTO ChatModels (Id, ModelId, APIkey, Endpoint, Provider) 
                       VALUES (@Id, @ModelId, @APIkey, @Endpoint, @Provider)";

            chatModel.Id = Guid.NewGuid();
            await _dbConnection.ExecuteAsync(sql, chatModel);
            return chatModel;
        }

        private async Task<ChatModel> Update(ChatModel chatModel)
        {
            var sql = @"UPDATE ChatModels 
                       SET ModelId = @ModelId, 
                           APIkey = @APIkey, 
                           Endpoint = @Endpoint, 
                           Provider = @Provider 
                       WHERE Id = @Id";

            await _dbConnection.ExecuteAsync(sql, chatModel);
            return chatModel;
        }

        public async Task<bool> DeleteAsync(string ModelId)
        {
            var sql = "DELETE FROM ChatModels WHERE ModelId = @ModelId";
            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new { ModelId = ModelId });
            return rowsAffected > 0;
        }
    }
}