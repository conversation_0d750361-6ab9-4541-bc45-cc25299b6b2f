﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Infrastructure.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Microsoft.SemanticKernel;
using System.ComponentModel;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class WorksapceAgentChooserPlugin
    {
        private readonly EmailExtractFromAccessor _extractEmail;
        private readonly IDbConnection _dbConnection;
        private readonly AssignWorkspaceRepository _assignWorkspaceRepository;

        public WorksapceAgentChooserPlugin(
            EmailExtractFromAccessor extractEmail,
            IDbConnection dbConnection,
            AssignWorkspaceRepository assignWorkspaceRepository)
        {
            _extractEmail = extractEmail;
            _dbConnection = dbConnection;
            _assignWorkspaceRepository = assignWorkspaceRepository;
        }

        [KernelFunction("get_workspaces")]
        [Description("Gets a list of workspaces based on the user or admin")]
        public async Task<List<Workspace>> GetWorkspaces()
        {
            var userRoles = _extractEmail.GetRoles().ToList();

            bool isAdmin = userRoles.Contains("Admin");

            var workspaces = new List<Workspace>();

            if (isAdmin)
            {
                workspaces = (await _dbConnection.QueryAsync<Workspace>("SELECT * FROM Workspaces")).ToList();
            }
            else
            {
                var email = _extractEmail.GetEmail();
                var sql = @"  
                          SELECT w.*  
                          FROM AssignWorkspaces aw  
                          INNER JOIN Workspaces w ON aw.WorkspaceId = w.Id  
                          WHERE aw.Email = @Email  
                          ORDER BY w.Id DESC";

                workspaces = (await _dbConnection.QueryAsync<Workspace>(sql, new { Email = email })).ToList();
            }

            return workspaces;
        }

        [KernelFunction("get_agents_by_workspace")]
        [Description("Gets all agent definitions for a specific workspace")]
        public async Task<IEnumerable<AgentDefinition>> GetAllByWorkspace(string workspace)
        {
            var sql = "SELECT * FROM AgentDefinitions where Workspace = @Workspace";
            return await _dbConnection.QueryAsync<AgentDefinition>(sql, new { Workspace = workspace });
        }
    }
}
