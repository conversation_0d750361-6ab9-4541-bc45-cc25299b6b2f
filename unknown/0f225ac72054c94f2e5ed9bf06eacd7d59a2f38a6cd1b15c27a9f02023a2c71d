﻿using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace ProjectApp.Core;

public static class CoreServiceRegistration
{
    public static IServiceCollection AddCoreServices(this IServiceCollection services)
    {
        services.AddAutoMapper(Assembly.GetExecutingAssembly());
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        services.AddMemoryCache();

        return services;
    }
}
