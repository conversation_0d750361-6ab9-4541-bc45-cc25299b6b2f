using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;

        public AuthController(IAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost("login")]
        public async Task<ActionResult<TokenResponseDto>> Login(LoginDto model)
        {
            var response = await _authService.Login(model);
            
            if (response == null)
                return Unauthorized(new { message = "Invalid credentials" });
            
            return Ok(response);
        }

        [HttpPost("register")]
        public async Task<ActionResult<TokenResponseDto>> Register(RegisterDto model)
        {
            try
            {
                var response = await _authService.Register(model);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpGet("current-user")]
        public ActionResult<UserDto> GetCurrentUser()
        {
            var user = _authService.GetCurrentUser();
            
            if (user == null)
                return Unauthorized(new { message = "Not authenticated" });
            
            return Ok(user);
        }
    }
} 