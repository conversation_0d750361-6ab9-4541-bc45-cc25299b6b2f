using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.OpenApi.Models;
using Microsoft.OpenApi.Readers;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;

namespace ProjectApp.Infrastructure.Services
{
    public class OpenApiPluginService
    {
        private readonly IPluginRepository _pluginRepository;
        private readonly IHttpClientFactory _httpClientFactory;

        public OpenApiPluginService(IPluginRepository pluginRepository, IHttpClientFactory httpClientFactory)
        {
            _pluginRepository = pluginRepository;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<PluginResponseDto> CreateFromOpenApiUrl(string url, string pluginName)
        {
            try
            {
                var functions = await ExtractFunctionsFromOpenApiUrl(url);

                // Create a new plugin
                var pluginDto = new PluginRequestDto
                {
                    PluginName = pluginName,
                    Type = "OpenAPI",
                    Functions = functions,
                    Url = url
                };

                // Check if plugin already exists
                var existingPlugin = await _pluginRepository.GetPluginByNameAsync(pluginName);
                if (existingPlugin != null)
                {
                    // Update existing plugin
                    pluginDto.Id = existingPlugin.Id;
                    return await _pluginRepository.UpdatePluginAsync(pluginDto);
                }
                else
                {
                    // Create new plugin
                    return await _pluginRepository.CreatePluginAsync(pluginDto);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to process OpenAPI document: {ex.Message}", ex);
            }
        }

        public async Task<PluginResponseDto> ResyncOpenApiPlugin(string pluginName)
        {
            var existingPlugin = await _pluginRepository.GetPluginByNameAsync(pluginName);
            if (existingPlugin == null)
                throw new Exception($"Plugin with name '{pluginName}' not found");

            if (existingPlugin.Type != "OpenAPI")
                throw new Exception($"Plugin '{pluginName}' is not an OpenAPI plugin");

            if (string.IsNullOrEmpty(existingPlugin.Url))
                throw new Exception($"Plugin '{pluginName}' does not have a stored URL");

            return await CreateFromOpenApiUrl(existingPlugin.Url, existingPlugin.PluginName);
        }

        private async Task<string> ExtractFunctionsFromOpenApiUrl(string url)
        {
            // Fetch and parse the OpenAPI document
            var httpClient = _httpClientFactory.CreateClient();
            var stream = await httpClient.GetStreamAsync(url);
            
            var openApiDocument = new OpenApiStreamReader().Read(stream, out var diagnostic);
            
            if (diagnostic.Errors.Count > 0)
            {
                throw new Exception($"OpenAPI document parsing errors: {string.Join(", ", diagnostic.Errors)}");
            }

            // Extract operations from the document
            var functions = new StringBuilder();
            foreach (var path in openApiDocument.Paths)
            {
                foreach (var operation in path.Value.Operations)
                {
                    var operationMethod = operation.Key.ToString();
                    var operationId = operation.Value.OperationId ?? $"{operationMethod}_{path.Key}";
                    var summary = operation.Value.Summary ?? "No description available";
                    
                    functions.AppendLine($"{operationId} - {summary}");
                }
            }

            return functions.ToString().TrimEnd();
        }
    }
} 