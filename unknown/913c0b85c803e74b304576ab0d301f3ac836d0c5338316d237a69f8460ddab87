﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
namespace ProjectApp.Core.Repositories
{
    public interface IDocsRepository
    {
        Task<List<Docs>> GetAll();
        Task<ViewDocsDto> GetById(int id);
        Task<Docs> CreateOrUpdate(CreateDocsDto request);
        Task<Docs> Delete(int id);
        Task<List<Docs>> GetByWorkspaceName(string workspaceName);
        Task<ResponseMessage> UpdateFavoriteStatus(UpdateFavoriteDto request);
        Task<ResponseMessage> TrackDocumentOpen(int id);
        Task<List<Docs>> GetRecentlyOpenedDocs(string workspaceName = null, int limit = 10);
        Task<List<Docs>> GetFavoriteDocs(string workspaceName = null);
    }
}
