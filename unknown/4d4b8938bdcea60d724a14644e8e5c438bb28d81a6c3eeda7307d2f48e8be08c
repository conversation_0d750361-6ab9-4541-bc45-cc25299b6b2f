<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plugin Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs" defer></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8" x-data="pluginManager()">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Plugin Manager</h1>
            <div class="flex gap-2">
                <button 
                    @click="openApiFormVisible = !openApiFormVisible" 
                    class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded shadow transition">
                    Add OpenAPI Plugin
                </button>
                <button 
                    @click="mcpFormVisible = !mcpFormVisible" 
                    class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded shadow transition">
                    Add MCP Plugin
                </button>
                <button 
                    @click="syncPlugins()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded shadow transition">
                    Sync Custom Plugins
                </button>
            </div>
        </div>

        <!-- Sync Message -->
        <div x-show="syncMessage" class="mb-4 p-4 rounded" :class="syncError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'">
            <p x-text="syncMessage"></p>
        </div>

        <!-- OpenAPI Plugin Form -->
        <div x-show="openApiFormVisible" class="mb-6 bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Add OpenAPI Plugin</h2>
            <form @submit.prevent="createOpenApiPlugin">
                <div class="mb-4">
                    <label for="pluginName" class="block text-sm font-medium text-gray-700 mb-1">Plugin Name</label>
                    <input type="text" id="pluginName" x-model="openApiForm.pluginName" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter a unique name for this plugin" required>
                </div>
                <div class="mb-4">
                    <label for="openApiUrl" class="block text-sm font-medium text-gray-700 mb-1">OpenAPI URL</label>
                    <input type="url" id="openApiUrl" x-model="openApiForm.url" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://api.example.com/swagger/v1/swagger.json" required>
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" @click="openApiFormVisible = false" 
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </button>
                    <button type="submit" 
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        :disabled="openApiForm.isLoading">
                        <span x-show="!openApiForm.isLoading">Create Plugin</span>
                        <span x-show="openApiForm.isLoading">Creating...</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- MCP Plugin Form -->
        <div x-show="mcpFormVisible" class="mb-6 bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Add MCP Plugin</h2>
            <form @submit.prevent="createMcpPlugin">
                <div class="mb-4">
                    <label for="mcpPluginName" class="block text-sm font-medium text-gray-700 mb-1">Plugin Name</label>
                    <input type="text" id="mcpPluginName" x-model="mcpForm.pluginName" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        placeholder="Enter a unique name for this plugin" required>
                </div>
                <div class="mb-4">
                    <label for="mcpUrl" class="block text-sm font-medium text-gray-700 mb-1">MCP URL</label>
                    <input type="url" id="mcpUrl" x-model="mcpForm.url" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        placeholder="https://example.com/.well-known/ai-plugin.json" required>
                </div>
                <div class="mb-4">
                    <label for="mcpAuthType" class="block text-sm font-medium text-gray-700 mb-1">Authentication Type</label>
                    <select id="mcpAuthType" x-model="mcpForm.authType" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="">No Authentication</option>
                        <option value="api_key">API Key</option>
                        <option value="bearer">Bearer Token</option>
                        <option value="basic">Basic Auth</option>
                        <option value="oauth2">OAuth 2.0</option>
                    </select>
                </div>
                <div class="mb-4" x-show="mcpForm.authType && mcpForm.authType !== ''">
                    <label for="mcpAuthValue" class="block text-sm font-medium text-gray-700 mb-1">
                        <span x-text="getAuthLabel()"></span>
                    </label>
                    <div class="relative">
                        <input :type="mcpForm.showAuthValue ? 'text' : 'password'" 
                            id="mcpAuthValue" 
                            x-model="mcpForm.authValue" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                            :placeholder="getAuthPlaceholder()" 
                            :required="mcpForm.authType && mcpForm.authType !== ''">
                        <button type="button" 
                            @click="mcpForm.showAuthValue = !mcpForm.showAuthValue"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <span class="text-gray-500 hover:text-gray-700">
                                <span x-show="!mcpForm.showAuthValue">Show</span>
                                <span x-show="mcpForm.showAuthValue">Hide</span>
                            </span>
                        </button>
                    </div>
                    <p class="mt-1 text-sm text-gray-500" x-text="getAuthHelpText()"></p>
                </div>
                <div class="flex justify-end gap-2">
                    <button type="button" @click="mcpFormVisible = false" 
                        class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        Cancel
                    </button>
                    <button type="submit" 
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                        :disabled="mcpForm.isLoading">
                        <span x-show="!mcpForm.isLoading">Create Plugin</span>
                        <span x-show="mcpForm.isLoading">Creating...</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Plugins List -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 bg-gray-50">
                <h2 class="text-lg font-semibold text-gray-700">Available Plugins</h2>
            </div>
            
            <div class="divide-y divide-gray-200">
                <template x-for="plugin in plugins" :key="plugin.id">
                    <div class="p-4 hover:bg-gray-50 transition">
                        <div class="flex justify-between items-start mb-2">
                            <a :href="'plugin-details.html?name=' + encodeURIComponent(plugin.pluginName)" class="text-lg font-medium text-blue-600 hover:text-blue-800 transition" x-text="plugin.pluginName"></a>
                            <div class="flex items-center space-x-2">
                                <button 
                                    x-show="plugin.type === 'OpenAPI' && plugin.url" 
                                    @click="resyncOpenApiPlugin(plugin.pluginName)"
                                    class="text-xs bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded">
                                    Re-sync
                                </button>
                                <button 
                                    @click="deletePlugin(plugin.pluginName)"
                                    class="text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded">
                                    Delete
                                </button>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full" 
                                      :class="{
                                          'bg-green-100 text-green-800': plugin.type === 'OpenAPI',
                                          'bg-purple-100 text-purple-800': plugin.type === 'MCP',
                                          'bg-blue-100 text-blue-800': plugin.type === 'Custom'
                                      }" 
                                      x-text="plugin.type"></span>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h4 class="text-md font-medium text-gray-700 mb-2">Functions:</h4>
                            <div class="bg-gray-50 rounded p-3 border border-gray-200">
                                <template x-for="(func, index) in formatFunctions(plugin.functions)" :key="index">
                                    <div class="mb-2 last:mb-0">
                                        <div class="flex">
                                            <span class="font-mono text-sm text-blue-600 font-semibold" x-text="func.name"></span>
                                            <span class="mx-2 text-gray-400">-</span>
                                            <span class="text-sm text-gray-600" x-text="func.description"></span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <div class="mt-4 text-sm text-gray-500">
                            <span>Created: <span x-text="formatDate(plugin.createdDate)"></span></span>
                            <span class="mx-2">|</span>
                            <span x-show="plugin.lastModifiedDate">Last Modified: <span x-text="formatDate(plugin.lastModifiedDate)"></span></span>
                        </div>
                    </div>
                </template>
                
                <div x-show="plugins.length === 0" class="p-8 text-center text-gray-500">
                    No plugins found. Click "Sync Plugins" to scan for available plugins or add an OpenAPI plugin.
                </div>
            </div>
        </div>
    </div>

    <script>
        function pluginManager() {
            return {
                plugins: [],
                syncMessage: '',
                syncError: false,
                openApiFormVisible: false,
                mcpFormVisible: false,
                openApiForm: {
                    pluginName: '',
                    url: '',
                    isLoading: false
                },
                mcpForm: {
                    pluginName: '',
                    url: '',
                    authType: '',
                    authValue: '',
                    showAuthValue: false,
                    isLoading: false
                },
                
                init() {
                    this.loadPlugins();
                },
                
                loadPlugins() {
                    fetch('/api/Plugin/GetAll')
                        .then(response => response.json())
                        .then(data => {
                            this.plugins = data;
                        })
                        .catch(error => {
                            console.error('Error loading plugins:', error);
                        });
                },
                
                syncPlugins() {
                    this.syncMessage = 'Syncing plugins...';
                    this.syncError = false;
                    
                    fetch('/api/Plugin/SyncPlugins', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.syncMessage = data.message;
                        this.syncError = data.isError;
                        if (!data.isError) {
                            this.loadPlugins();
                        }
                        
                        // Clear message after 5 seconds
                        setTimeout(() => {
                            this.syncMessage = '';
                        }, 5000);
                    })
                    .catch(error => {
                        this.syncMessage = 'Error syncing plugins: ' + error.message;
                        this.syncError = true;
                        console.error('Error:', error);
                    });
                },
                
                createOpenApiPlugin() {
                    this.openApiForm.isLoading = true;
                    this.syncMessage = 'Creating OpenAPI plugin...';
                    this.syncError = false;
                    
                    fetch('/api/Plugin/CreateFromOpenApi', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            pluginName: this.openApiForm.pluginName,
                            url: this.openApiForm.url
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.syncMessage = data.message;
                        this.syncError = data.isError;
                        
                        if (!data.isError) {
                            this.loadPlugins();
                            this.openApiFormVisible = false;
                            this.openApiForm.pluginName = '';
                            this.openApiForm.url = '';
                        }
                        
                        // Clear message after 5 seconds
                        setTimeout(() => {
                            this.syncMessage = '';
                        }, 5000);
                    })
                    .catch(error => {
                        this.syncMessage = 'Error creating OpenAPI plugin: ' + error.message;
                        this.syncError = true;
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        this.openApiForm.isLoading = false;
                    });
                },
                
                createMcpPlugin() {
                    this.mcpForm.isLoading = true;
                    this.syncMessage = 'Creating MCP plugin...';
                    this.syncError = false;
                    
                    // Clear auth values if no authentication is selected
                    if (!this.mcpForm.authType) {
                        this.mcpForm.authValue = '';
                    }
                    
                    fetch('/api/Plugin/CreateFromMcp', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            pluginName: this.mcpForm.pluginName,
                            url: this.mcpForm.url,
                            authType: this.mcpForm.authType || null,
                            authValue: this.mcpForm.authValue || null
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.syncMessage = data.message;
                        this.syncError = data.isError;
                        
                        if (!data.isError) {
                            this.loadPlugins();
                            this.mcpFormVisible = false;
                            this.mcpForm.pluginName = '';
                            this.mcpForm.url = '';
                            this.mcpForm.authType = '';
                            this.mcpForm.authValue = '';
                            this.mcpForm.showAuthValue = false;
                        }
                        
                        setTimeout(() => {
                            this.syncMessage = '';
                        }, 5000);
                    })
                    .catch(error => {
                        this.syncMessage = 'Error creating MCP plugin: ' + error.message;
                        this.syncError = true;
                        console.error('Error:', error);
                    })
                    .finally(() => {
                        this.mcpForm.isLoading = false;
                    });
                },
                
                formatFunctions(functionsStr) {
                    if (!functionsStr) return [];
                    
                    return functionsStr.split('\n').map(func => {
                        const parts = func.split(' - ');
                        return {
                            name: parts[0],
                            description: parts.length > 1 ? parts[1] : 'No description'
                        };
                    });
                },
                
                formatDate(dateStr) {
                    if (!dateStr) return '';
                    return new Date(dateStr).toLocaleString();
                },
                
                resyncOpenApiPlugin(pluginName) {
                    this.syncMessage = 'Re-syncing OpenAPI plugin...';
                    this.syncError = false;
                    
                    fetch(`/api/Plugin/ResyncOpenApiPlugin/${encodeURIComponent(pluginName)}`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.syncMessage = data.message;
                        this.syncError = data.isError;
                        
                        if (!data.isError) {
                            this.loadPlugins();
                        }
                        
                        // Clear message after 5 seconds
                        setTimeout(() => {
                            this.syncMessage = '';
                        }, 5000);
                    })
                    .catch(error => {
                        this.syncMessage = 'Error re-syncing OpenAPI plugin: ' + error.message;
                        this.syncError = true;
                        console.error('Error:', error);
                    });
                },

                deletePlugin(pluginName) {
                    if (!confirm('Are you sure you want to delete this plugin?')) return;
                    
                    fetch(`/api/Plugin/Delete/${encodeURIComponent(pluginName)}`, {
                        method: 'DELETE'
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.syncMessage = data.message;
                        this.syncError = data.isError;
                        
                        if (!data.isError) {
                            this.loadPlugins();
                        }
                        
                        setTimeout(() => {
                            this.syncMessage = '';
                        }, 5000);
                    })
                    .catch(error => {
                        this.syncMessage = 'Error deleting plugin: ' + error.message;
                        this.syncError = true;
                        console.error('Error:', error);
                    });
                },

                getAuthLabel() {
                    switch(this.mcpForm.authType) {
                        case 'api_key': return 'API Key';
                        case 'bearer': return 'Bearer Token';
                        case 'basic': return 'Basic Auth Credentials';
                        case 'oauth2': return 'OAuth 2.0 Token';
                        default: return 'Authentication Value';
                    }
                },

                getAuthPlaceholder() {
                    switch(this.mcpForm.authType) {
                        case 'api_key': return 'Enter your API key';
                        case 'bearer': return 'Enter your bearer token';
                        case 'basic': return 'Enter your basic auth credentials';
                        case 'oauth2': return 'Enter your OAuth 2.0 token';
                        default: return 'Enter authentication value';
                    }
                },

                getAuthHelpText() {
                    switch(this.mcpForm.authType) {
                        case 'api_key': return 'The API key will be securely stored and used for authentication.';
                        case 'bearer': return 'The bearer token will be used in the Authorization header.';
                        case 'basic': return 'Format: username:password';
                        case 'oauth2': return 'The OAuth 2.0 token will be used for authentication.';
                        default: return '';
                    }
                }
            };
        }
    </script>
</body>
</html> 