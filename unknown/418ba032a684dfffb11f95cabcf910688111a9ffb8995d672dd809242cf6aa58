﻿using ProjectApp.Core.Models;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Repositiories;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CommentController(ICommentRepository _commentRepository) : ControllerBase
    {
        [HttpGet("GetAllByProjectId")]
        public async Task<ActionResult<List<Comment>>> GetAllByProjectId(int projectId)
        {
            var res = await _commentRepository.GetAllByProjectId(projectId);
            return Ok(res);
        }

        [HttpPost("AddComment")]
        public async Task<ActionResult<Comment>> AddComment(CommentDto request)
        {
            var res = await _commentRepository.AddComment(request);
            return Ok(res);
        }

    }
}
