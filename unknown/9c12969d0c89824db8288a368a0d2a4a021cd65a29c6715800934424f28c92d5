﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Models
{
    public class SuggestWorkspaceResDto
    {
        public int WorkspaceId { get; set; }
        public string DeveloperEmail { get; set; }
        public int ProjectCategoryId { get; set; }
    }

    public class MemoryTag
    {
        public string Name { get; set; }
        public string Value { get; set; }
    }
    public class FileDescription
    {
        public string FileName { get; set; }
        public string Description { get; set; }
    }
    public class AddDataRequest
    {
        public string Data { get; set; }
        public string DocumentId { get; set; }
        public string Index { get; set; }
        public List<MemoryTag> Tags { get; set; }
    }
    public class MatchedDocument
    {
        public string Title { get; set; }
        public string ExtractedContent { get; set; }
        public string Files { get; set; }
        public int Id { get; set; }
    }
}
