﻿using Microsoft.AspNetCore.Http;
using ProjectApp.Core.Repositories;
using System.Security.Claims;

namespace ProjectApp.Infrastructure.Repositories
{
    public class EmailExtractFromAccessor(IHttpContextAccessor _httpContextAccessor) : IExtractEmailFromAccessor
    {
        public string GetEmail()
        {
            // Access the current HttpContext  
            var httpContext = _httpContextAccessor.HttpContext;

            // Check if HttpContext is not null and the user is authenticated  
            if (httpContext != null && httpContext.User != null && httpContext.User.Identity.IsAuthenticated)
            {
                // Try to get the email claim (ClaimTypes.Email or "email" claim)  
                var emailClaim = httpContext.User.FindFirst(ClaimTypes.Email);
                if (emailClaim != null)
                {
                    return emailClaim.Value;
                }
            }

            // Return an empty string if the email is not found  
            return string.Empty;
        }

        public IEnumerable<string> GetRoles()
        {
            // Access the current HttpContext  
            var httpContext = _httpContextAccessor.HttpContext;

            // Check if HttpContext is not null and the user is authenticated  
            if (httpContext != null && httpContext.User != null && httpContext.User.Identity.IsAuthenticated)
            {
                // Retrieve all role claims  
                var roleClaims = httpContext.User.FindAll(ClaimTypes.Role);
                if (roleClaims != null)
                {
                    return roleClaims.Select(role => role.Value);
                }
            }

            // Return an empty list if no roles are found  
            return Enumerable.Empty<string>();
        }

        public IDictionary<string, string> GetAllClaims()
        {
            // Access the current HttpContext  
            var httpContext = _httpContextAccessor.HttpContext;

            // Check if HttpContext is not null and the user is authenticated  
            if (httpContext != null && httpContext.User != null && httpContext.User.Identity.IsAuthenticated)
            {
                // Retrieve all claims, ensuring no duplicate keys by grouping claims with the same type  
                return httpContext.User.Claims
                    .GroupBy(claim => claim.Type)
                    .ToDictionary(group => group.Key, group => group.First().Value);
            }

            // Return an empty dictionary if no claims are found  
            return new Dictionary<string, string>();
        }
    }
}
