using Microsoft.SemanticKernel;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Services
{
    public class PluginScannerService
    {
        private readonly IPluginRepository _pluginRepository;

        public PluginScannerService(IPluginRepository pluginRepository)
        {
            _pluginRepository = pluginRepository;
        }

        public async Task<List<PluginResponseDto>> SyncPluginsAsync()
        {
            var plugins = ScanPlugins();
            var results = new List<PluginResponseDto>();

            foreach (var plugin in plugins)
            {
                var existingPlugin = await _pluginRepository.GetPluginByNameAsync(plugin.PluginName);
                PluginResponseDto result;

                if (existingPlugin != null)
                {
                    // Update existing plugin
                    var updateDto = new PluginRequestDto
                    {
                        Id = existingPlugin.Id,
                        PluginName = plugin.PluginName,
                        Type = plugin.Type,
                        Functions = plugin.Functions
                    };
                    result = await _pluginRepository.UpdatePluginAsync(updateDto);
                }
                else
                {
                    // Create new plugin
                    var createDto = new PluginRequestDto
                    {
                        PluginName = plugin.PluginName,
                        Type = plugin.Type,
                        Functions = plugin.Functions
                    };
                    result = await _pluginRepository.CreatePluginAsync(createDto);
                }

                results.Add(result);
            }

            return results;
        }

        private List<Plugin> ScanPlugins()
        {
            var pluginsList = new List<Plugin>();
            var namespaceToSearch = "ProjectApp.Infrastructure.AIAgents.Tools";
            
            var pluginTypes = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(t => t.Namespace == namespaceToSearch && t.IsClass && t.Name.EndsWith("Plugin"))
                .ToList();

            foreach (var pluginType in pluginTypes)
            {
                var functions = new StringBuilder();
                var methods = pluginType.GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .Where(m => m.GetCustomAttribute<KernelFunctionAttribute>() != null)
                    .ToList();

                foreach (var method in methods)
                {
                    var kernelFunction = method.GetCustomAttribute<KernelFunctionAttribute>();
                    var description = method.GetCustomAttribute<DescriptionAttribute>();
                    
                    if (kernelFunction != null)
                    {
                        string functionName = kernelFunction.Name ?? method.Name;
                        string functionDescription = description?.Description ?? "No description available";
                        
                        functions.AppendLine($"{functionName} - {functionDescription}");
                    }
                }

                var plugin = new Plugin
                {
                    Id = Guid.NewGuid(),
                    PluginName = pluginType.Name,
                    Type = "CustomPlugin",
                    Functions = functions.ToString().TrimEnd(),
                    CreatedDate = DateTime.Now
                };

                pluginsList.Add(plugin);
            }

            return pluginsList;
        }
    }
} 