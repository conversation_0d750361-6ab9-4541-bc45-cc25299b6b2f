﻿using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using OpenAI.Embeddings;
using ProjectApp.Core.Models;
using System.Text.Json;

public class VectorSearchExample(IOptions<OpenAIOptions> options)
{
    private static readonly string _cConnStr = "Server=sql-amexmakerclub.database.windows.net; Database=sqldb-amc-prod; User ID=svc_ailocal; Password=password@7894!;TrustServerCertificate=True";

    // Model representing the Vectors table
    public class VectorRecord
    {
        public int Id { get; set; }
        public string Text { get; set; }
        public string VectorShort { get; set; }
        public string Vector { get; set; }
    }

    // Method to insert a 3-dimensional vector into the table
    public static async Task CreateAndInsertVectorsAsync()
    {
        using (var connection = new SqlConnection(_cConnStr))
        {
            string sql = "INSERT INTO TestVectors ([VectorShort]) VALUES (@Vector)";
            var parameters = new { Vector = "[1.12, 2.22, 3.33]" };

            connection.Open();
            await connection.ExecuteAsync(sql, parameters);
            connection.Close();
        }
    }

    // Method to insert an embedding vector into the table
    public async Task CreateAndInsertEmbeddingAsync()
    {
        EmbeddingClient client = new(options.Value.EmbeddingModelId, options.Value.ApiKey);

        string text = "Welcome to native Vector Search for SQL Server";
        var res = await client.GenerateEmbeddingsAsync(new List<string>() { text });
        OpenAIEmbedding embedding = res.Value.First();
        ReadOnlyMemory<float> embeddingVector = embedding.ToFloats();

        using (var connection = new SqlConnection(_cConnStr))
        {
            string sql = "INSERT INTO TestVectors ([Vector], [Text]) VALUES (@Vector, @Text)";
            var parameters = new
            {
                Vector = JsonSerializer.Serialize(embeddingVector.ToArray()),
                Text = text
            };

            connection.Open();
            await connection.ExecuteAsync(sql, parameters);
            connection.Close();
        }
    }

    // Method to read vectors from the database
    public static async Task ReadVectorsAsync()
    {
        using (var connection = new SqlConnection(_cConnStr))
        {
            string sql = "SELECT TOP(100) Id, VectorShort, Vector, Text FROM TestVectors";
            connection.Open();
            var rows = await connection.QueryAsync<VectorRecord>(sql);
            connection.Close();

            foreach (var row in rows)
            {
                Console.WriteLine($"{row.Id}, {row.Vector}, {row.Text}");
            }
        }
    }

    // Method to find the top matching vectors based on cosine similarity
    public async Task<List<(long Id, double Distance, string Text)>> GetMatching(int howMany, string text)
    {
        List<(long Id, double Distance, string Text)> matchingRows = new();
        EmbeddingClient client = new(options.Value.EmbeddingModelId, options.Value.ApiKey);

        var res = await client.GenerateEmbeddingsAsync(new List<string>() { text });
        ReadOnlyMemory<float> embeddingVector = res.Value.First().ToFloats();

        using (var connection = new SqlConnection(_cConnStr))
        {
            string sql = $@"
                SELECT TOP({howMany}) 
                    Id, 
                    Text, 
                   1 - VECTOR_DISTANCE('cosine', CAST(@Embedding AS VECTOR(1536)), Vector) AS Distance
                FROM TestVectors
                ORDER BY Distance";

            var parameters = new
            {
                Embedding = JsonSerializer.Serialize(embeddingVector.ToArray())
            };

            connection.Open();
            var rows = await connection.QueryAsync<(int Id, string Text, double Distance)>(sql, parameters);
            connection.Close();

            foreach (var row in rows)
            {
                matchingRows.Add((row.Id, row.Distance, row.Text));
            }
        }

        return matchingRows;
    }
}
