﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IApiCredentialsRepository
    {
        Task<Guid> Create(ApiCredentialsDto credentials);
        Task<bool> IsValidCredentials(string tokenUrl, string apiKey);
        Task<List<string>> GetModelsFromApi(string tokenUrl, string apiKey);
        Task<bool> Delete(Guid id);
        Task<IEnumerable<ApiCredentials>> GetAll();
        Task<ApiCredentials> GetById(Guid id);
    }
}
