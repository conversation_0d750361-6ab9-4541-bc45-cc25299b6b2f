namespace ProjectApp.Core.Models
{
    public class AgentLog
    {
        public int Id { get; set; }
        public string AgentName { get; set; }
        public string FunctionName { get; set; }
        public string Parameters { get; set; }
        public string Status { get; set; } // Created, Executing, Executed, Failed
        public DateTime CreatedAt { get; set; }
        public DateTime? ExecutedAt { get; set; }
        public string? ErrorMessage { get; set; }
    }
} 