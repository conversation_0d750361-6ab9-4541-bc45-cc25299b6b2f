using Microsoft.AspNetCore.Http;
using ProjectApp.Core.Repositories;
using System;

namespace ProjectApp.Infrastructure.Services
{
    public class UrlService : IUrlService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const string FALLBACK_URL = "http://localhost:5000";

        public UrlService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetBaseUrl()
        {
            if (_httpContextAccessor.HttpContext != null)
            {
                var request = _httpContextAccessor.HttpContext.Request;
                return $"{request.Scheme}://{request.Host.Value}";
            }
            
            return FALLBACK_URL;
        }

        public string GetApiUrl(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath))
            {
                throw new ArgumentException("Relative path cannot be null or empty", nameof(relativePath));
            }

            if (relativePath.StartsWith("/"))
            {
                relativePath = relativePath.Substring(1);
            }

            return $"{GetBaseUrl()}/{relativePath}";
        }

        public string GetFileUrl(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                throw new ArgumentException("File name cannot be null or empty", nameof(fileName));
            }

            // Generate URL for file in wwwroot/uploads directory
            string encodedFileName = Uri.EscapeDataString(fileName.Trim());
            return $"{GetBaseUrl()}/uploads/{encodedFileName}";
        }
    }
}