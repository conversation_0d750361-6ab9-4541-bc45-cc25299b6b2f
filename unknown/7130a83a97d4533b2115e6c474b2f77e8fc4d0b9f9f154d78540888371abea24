﻿using Microsoft.SemanticKernel;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class FileDescriptionPlugin(IDbConnection _dbConntection)
    {

        [KernelFunction("get_files_info")]
        [Description("Gets file names and descriptions for provided file names")]
        public async Task<List<FileDescription>> GetFilesInfoAsync(List<string> fileNames)
        {
            string fileQuery = "SELECT FileName, Description FROM [dbo].[Files] WHERE FileName IN @FileNames";
            var files = await _dbConntection.QueryAsync<FileDescription>(fileQuery, new { FileNames = fileNames });
            return files.ToList();
        }
    }
}
