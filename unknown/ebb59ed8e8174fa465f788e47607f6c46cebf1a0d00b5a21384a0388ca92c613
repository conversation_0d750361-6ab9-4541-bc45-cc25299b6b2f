using ProjectApp.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Dtos
{
    public interface IEmbeddingConfigRepository
    {
        Task<EmbeddingConfiguration> GetActiveEmbeddingConfigAsync();
        Task<EmbeddingConfiguration> GetEmbeddingConfigByIdAsync(int id);
        Task<List<EmbeddingConfiguration>> GetAllEmbeddingConfigsAsync();
        Task<int> SaveEmbeddingConfigAsync(EmbeddingConfiguration config);
        Task<bool> SetEmbeddingConfigActiveAsync(int id);
        Task<bool> DeleteEmbeddingConfigAsync(int id);
    }
}