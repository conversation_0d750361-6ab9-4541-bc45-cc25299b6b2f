using Microsoft.SemanticKernel;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using System.ComponentModel;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class UserSkillMatchPlugin
    {
        private readonly IAssignWorkspaceRepository _assignWorkspaceRepository;

        public UserSkillMatchPlugin(IAssignWorkspaceRepository assignWorkspaceRepository)
        {
            _assignWorkspaceRepository = assignWorkspaceRepository;
        }


        [KernelFunction("suggest_user")]
        [Description("Analyzes project requirements and suggests the most suitable user based on their skills. Returns just the email of best matching user.")]
        public async Task<List<UserDto>> SuggestUserAsync(
            [Description("Detailed description of the project")]
                string projectDescription,
            [Description("The workspace ID to find users in")]
                int workspaceId)
        {
            var users = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspaceId);
            return users;
        }
    }
}