namespace ProjectApp.Core.Models
{
    public class ChatHistories
    {
        public Guid Id { get; set; }
        public Guid ChatMessageId { get; set; }
        public string Message { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsEdited { get; set; }
        public Guid? OriginalMessageId { get; set; }  // Reference to original message if this is edited
        public string ModelName { get; set; }
        public string AgentName { get; set; }
        public List<ChatResponse> Responses { get; set; } = new List<ChatResponse>();
    }
} 