﻿using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class DailyInsightAgentRepository : IDailyInsightAgentRepository
    {
        private readonly IDbConnection _dbConnection;

        public DailyInsightAgentRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<DailyInsightAgent>> GetAllByUserEmailAsync(string userEmail)
        {
            var sql = "SELECT * FROM DailyInsightAgents WHERE UserEmail = @UserEmail";
            return await _dbConnection.QueryAsync<DailyInsightAgent>(sql, new { UserEmail = userEmail });
        }

        public async Task<DailyInsightAgent> GetByIdAsync(int id)
        {
            var sql = "SELECT * FROM DailyInsightAgents WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<DailyInsightAgent>(sql, new { Id = id });
        }

        public async Task<DailyInsightAgent> CreateAsync(DailyInsightAgentDto dto, string userEmail)
        {
            var agent = new DailyInsightAgent
            {
                UserEmail = userEmail,
                AgentName = dto.AgentName,
                Prompt = dto.Prompt,
                Title = dto.Title,
                CreatedAt = DateTime.Now
            };

            var sql = @"INSERT INTO DailyInsightAgents (UserEmail, AgentName, Prompt, Title, CreatedAt)
                       OUTPUT INSERTED.Id
                       VALUES (@UserEmail, @AgentName, @Prompt, @Title, @CreatedAt)";

            agent.Id = await _dbConnection.ExecuteScalarAsync<int>(sql, agent);
            return agent;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var sql = "DELETE FROM DailyInsightAgents WHERE Id = @Id";
            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<bool> UpdateLastRunAsync(int id, string response)
        {
            var sql = @"UPDATE DailyInsightAgents
                       SET LastRunAt = @LastRunAt, LastResponse = @LastResponse
                       WHERE Id = @Id";

            var rowsAffected = await _dbConnection.ExecuteAsync(sql, new
            {
                Id = id,
                LastRunAt = DateTime.Now,
                LastResponse = response
            });

            return rowsAffected > 0;
        }

        public async Task<IEnumerable<DailyInsightAgent>> GetAllAgentsForSyncAsync()
        {
            // Get all agents regardless of when they were last run
            var sql = @"SELECT * FROM DailyInsightAgents
                       ORDER BY CreatedAt ASC";

            Console.WriteLine($"[{DateTime.Now}] Getting all agents for daily sync");

            var agents = await _dbConnection.QueryAsync<DailyInsightAgent>(sql);

            // Log the number of agents found
            Console.WriteLine($"[{DateTime.Now}] Found {agents.Count()} agents to sync");

            return agents;
        }
    }
}
