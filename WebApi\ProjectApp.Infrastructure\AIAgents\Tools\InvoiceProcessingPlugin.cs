using Microsoft.SemanticKernel;
using System.ComponentModel;
using System.Text.Json;
using Microsoft.SemanticKernel.ChatCompletion;
using System.Data;
using Dapper;
using Microsoft.AspNetCore.Http.HttpResults;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    // DTO matching the SQL Invoices table structure
    public class InvoiceDto
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerAddress { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime? DueDate { get; set; }
        public string Items { get; set; } // JSON string containing item details
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = "USD";
        public string Status { get; set; } = "Draft";
        public string Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class InvoiceItemDto
    {
        public string Description { get; set; }
        public decimal Quantity { get; set; } = 1;
        public decimal UnitPrice { get; set; }
        public decimal LineTotal { get; set; }
    }

    // DTO for invoice creation from user prompt
    public class CreateInvoiceDto
    {
        public string InvoiceNumber { get; set; }
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }
        public string CustomerAddress { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime? DueDate { get; set; }
        public List<InvoiceItemDto> Items { get; set; } = new List<InvoiceItemDto>();
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string Currency { get; set; } = "USD";
        public string Notes { get; set; }
    }

    public class InvoiceProcessingPlugin(IChatCompletionService chatCompletionService, IDbConnection dbConnection)
    {
        private readonly IDbConnection _dbConnection = dbConnection;


        [KernelFunction("save_invoice")]
        [Description("Saves an invoice to the database using structured invoice data")]
        public async Task<int> SaveInvoiceAsync(CreateInvoiceDto invoiceData)
        {
            try
            {
                var sql = @"INSERT INTO Invoices (InvoiceNumber, CustomerName, CustomerEmail, CustomerAddress,
                                                InvoiceDate, DueDate, Items, SubTotal, TaxAmount, TotalAmount,
                                                Currency, Status, Notes, CreatedAt, UpdatedAt)
                            OUTPUT INSERTED.Id
                            VALUES (@InvoiceNumber, @CustomerName, @CustomerEmail, @CustomerAddress,
                                    @InvoiceDate, @DueDate, @Items, @SubTotal, @TaxAmount, @TotalAmount,
                                    @Currency, @Status, @Notes, @CreatedAt, @UpdatedAt)";

                var parameters = new
                {
                    InvoiceNumber = invoiceData.InvoiceNumber ?? GenerateInvoiceNumber(),
                    CustomerName = invoiceData.CustomerName ?? "Unknown Customer",
                    CustomerEmail = invoiceData.CustomerEmail,
                    CustomerAddress = invoiceData.CustomerAddress,
                    InvoiceDate = invoiceData.InvoiceDate != default ? invoiceData.InvoiceDate : DateTime.Now,
                    DueDate = invoiceData.DueDate,
                    Items = JsonSerializer.Serialize(invoiceData.Items),
                    SubTotal = invoiceData.SubTotal,
                    TaxAmount = invoiceData.TaxAmount,
                    TotalAmount = invoiceData.TotalAmount,
                    Currency = invoiceData.Currency,
                    Status = "Draft",
                    Notes = invoiceData.Notes,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                return await _dbConnection.ExecuteScalarAsync<int>(sql, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save invoice: {ex.Message}");
            }
        }

        [KernelFunction("get_all_invoices")]
        [Description("Retrieves all invoices from the database")]
        public async Task<List<InvoiceDto>> GetAllInvoicesAsync()
        {
            try
            {
                var sql = @"SELECT *
                            FROM Invoices 
                            ORDER BY CreatedAt DESC";

                var invoices = await _dbConnection.QueryAsync<InvoiceDto>(sql);

                if (!invoices.Any())
                {
                    return null;
                }


                return invoices.ToList();
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        [KernelFunction("get_invoice")]
        [Description("Retrieves a specific invoice by invoice number, customer email, customer name, or status")]
        public async Task<List<InvoiceDto>> GetInvoiceAsync(string value, string filterType)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(value) || string.IsNullOrWhiteSpace(filterType))
                {
                    return null;
                }

                var sql = @"SELECT * FROM Invoices WHERE 1=1";
                var parameters = new DynamicParameters();

                switch (filterType.ToLower())
                {
                    case "invoicenumber":
                        sql += " AND InvoiceNumber = @Value";
                        parameters.Add("Value", value);
                        break;
                    case "customeremail":
                        sql += " AND CustomerEmail LIKE @Value";
                        parameters.Add("Value", $"%{value}%");
                        break;
                    case "customername":
                        sql += " AND CustomerName LIKE @Value";
                        parameters.Add("Value", $"%{value}%");
                        break;
                    case "status":
                        sql += " AND Status = @Value";
                        parameters.Add("Value", value);
                        break;
                    default:
                        return null;
                }

                sql += " ORDER BY CreatedAt DESC";

                var invoices = await _dbConnection.QueryAsync<InvoiceDto>(sql, parameters);

                return invoices.ToList();
            }
            catch
            {
                return null;
            }
        }

        [KernelFunction("update_invoice")]
        [Description("Updates an existing invoice using the provided invoice data")]
        public async Task<string> UpdateInvoiceAsync(InvoiceDto invoiceData)
        {
            try
            {
                var sql = @"UPDATE Invoices SET 
                            CustomerName = @CustomerName,
                            CustomerEmail = @CustomerEmail,
                            CustomerAddress = @CustomerAddress,
                            InvoiceDate = @InvoiceDate,
                            DueDate = @DueDate,
                            Items = @Items,
                            SubTotal = @SubTotal,
                            TaxAmount = @TaxAmount,
                            TotalAmount = @TotalAmount,
                            Currency = @Currency,
                            Status = @Status,
                            Notes = @Notes,
                            UpdatedAt = @UpdatedAt
                            WHERE Id = @Id";

                var parameters = new
                {
                    Id = invoiceData.Id,
                    CustomerName = invoiceData.CustomerName,
                    CustomerEmail = invoiceData.CustomerEmail,
                    CustomerAddress = invoiceData.CustomerAddress,
                    InvoiceDate = invoiceData.InvoiceDate,
                    DueDate = invoiceData.DueDate,
                    Items = invoiceData.Items,
                    SubTotal = invoiceData.SubTotal,
                    TaxAmount = invoiceData.TaxAmount,
                    TotalAmount = invoiceData.TotalAmount,
                    Currency = invoiceData.Currency,
                    Status = invoiceData.Status,
                    Notes = invoiceData.Notes,
                    UpdatedAt = DateTime.UtcNow
                };

                var rowsAffected = await _dbConnection.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    return $"Invoice successfully updated. Invoice ID: {invoiceData.Id}, Invoice Number: {invoiceData.InvoiceNumber}";
                }
                else
                {
                    return "Error: No rows were updated. Invoice not found.";
                }
            }
            catch (Exception ex)
            {
                return $"Error updating invoice: {ex.Message}";
            }
        }

        private static string GenerateInvoiceNumber()
        {
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var random = new Random().Next(1000, 9999);
            return $"INV-{timestamp}-{random}";
        }
    }
}
