
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Dtos
{
    public class ProjectDto
    {
        public int Id { get; set; }
        public string Message { get; set; }
        public List<String> FilesName { get; set; }
        public string Priority { get; set; }
        public string Subject { get; set; }
    }

    public class NewProjectDto
    {
        public int Id { get; set; }
        public string Message { get; set; }
        public List<String> FilesName { get; set; }
        public string Priority { get; set; }
        public string Subject { get; set; }
        public string UserEmail { get; set; }
    }


    public class ProjectViewDto
    {
        public int Id { get; set; }
        public string Message { get; set; }
        public List<String> FilesName { get; set; }
        public string UserEmail { get; set; }
        public string Status { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime CompletionDate { get; set; }
        public string Priority { get; set; }
        public string Subject { get; set; }
        public string AssignedEmail { get; set; }
        public string WorkspaceTitle { get; set; }
        public int WorkspaceId { get; set; }
        public string Summary { get; set; }
        public int ProjectCategoryId { get; set; }
        public string ProjectCategory { get; set; }
        public List<MatchedDocument> MatchedDocuments { get; set; }

    }
}
