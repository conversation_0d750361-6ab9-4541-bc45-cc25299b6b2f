using System;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using Microsoft.SemanticKernel;
using Microsoft.AspNetCore.Hosting;
using ProjectApp.Infrastructure.Services;
using Microsoft.SemanticKernel.ChatCompletion;
using ProjectApp.Core.Repositories;
using Dapper;
using System.Data;
using ProjectApp.Core.Dtos;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class FileTextExtractionPlugin
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;
        private readonly AIAgentFactory _aiAgentFactory;
        private readonly IChatCompletionService _chatCompletionService;
        private readonly IFileRepository _fileRepository;
        private readonly IDbConnection _dbConnection;

        public FileTextExtractionPlugin(IWebHostEnvironment webHostEnvironment, IAgentDefinitionRepository agentDefinitionRepository, AIAgentFactory aiAgentFactory, IChatCompletionService chatCompletionService, IFileRepository fileRepository, IDbConnection dbConnection)
        {
            _webHostEnvironment = webHostEnvironment;
            _agentDefinitionRepository = agentDefinitionRepository;
            _aiAgentFactory = aiAgentFactory;
            _chatCompletionService = chatCompletionService;
            _fileRepository = fileRepository;
            _dbConnection = dbConnection;
        }

        [KernelFunction("extract_text_only")]
        [Description("Extracts raw text content from files in wwwroot/uploads folder. Supports PDF, Word, Excel, text, Image and CSV files.")]
        public async Task<string> ExtractTextOnly(
            [Description("The name of the file in wwwroot/uploads folder (e.g., 'document.pdf', 'spreadsheet.xlsx', 'image.jpg')")] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                var filePath = GetFilePath(fileName);
                if (!File.Exists(filePath))
                {
                    return GetFileNotFoundMessage(fileName);
                }

                // Use async extraction and pass the chat completion service
                var extractedText = await DocumentTextExtractor.ExtractTextAsync(filePath, _chatCompletionService);

                // Save file information to database after successful extraction
                if (!extractedText.StartsWith("Error"))
                {
                    var fileDto = new FileDto
                    {
                        FileName = fileName,
                        Description = extractedText,
                    };
                    //await SaveFileToDatabase(fileDto);
                }

                return extractedText;
            }
            catch (Exception ex)
            {
                return $"Error extracting text from '{fileName}': {ex.Message}";
            }
        }

        [KernelFunction("extract_text_and_summarize")]
        [Description("Extracts text from files and provides an AI-generated summary. Works with all file types")]
        public async Task<string> ExtractTextAndSummarizeAsync(
            [Description("The name of the file in wwwroot/uploads folder")] string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return "Error: File name is required.";

                var filePath = GetFilePath(fileName);
                if (!File.Exists(filePath))
                {
                    return GetFileNotFoundMessage(fileName);
                }

                // Use the async method with chat completion service for advanced image processing
                var extractedText = await DocumentTextExtractor.ExtractTextAsync(filePath, _chatCompletionService);

                // Save file information to database after successful extraction
                if (!extractedText.StartsWith("Error"))
                {
                    var fileDto = new FileDto
                    {
                        FileName = fileName,
                        Description = "Extracted text from file",
                        FileType = Path.GetExtension(fileName).TrimStart('.')
                    };
                    //await SaveFileToDatabase(fileDto);
                }

                if (extractedText.StartsWith("Error"))
                {
                    return extractedText;
                }

                // For documents, if text is long, use AI to summarize
                // if (extractedText.Length > 3000)
                // {
                //     return CreateAISummary(extractedText, fileName);
                // }

                return extractedText;
            }
            catch (Exception ex)
            {
                return $"Error processing '{fileName}': {ex.Message}";
            }
        }

        // Helper methods
        private string GetFilePath(string fileName)
        {
            var uploadsPath = Path.Combine(_webHostEnvironment.WebRootPath, "uploads");
            return Path.Combine(uploadsPath, fileName);
        }

        private string GetFileNotFoundMessage(string fileName)
        {
            //var availableFiles = GetAvailableFiles();
            return $"Error: File '{fileName}' not found in uploads folder.";
        }

        private async Task SaveFileToDatabase(FileDto fileDto)
        {
            try
            {

                // Save to database using Dapper directly since we're not uploading a file
                var query = "INSERT INTO Files (FileName, Description) VALUES (@FileName, @Description)";
                await _dbConnection.ExecuteAsync(query, new { FileName = fileDto.FileName, Description = fileDto.Description});
            }
            catch (Exception ex)
            {
                // Log error but don't fail the extraction process
                Console.WriteLine($"Error saving file metadata to database: {ex.Message}");
            }
        }
    }
}
