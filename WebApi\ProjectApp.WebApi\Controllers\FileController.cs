﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure;
using ProjectApp.WebApi.Hubs;
using System.Text;

namespace AdminPortalBackend.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FileController : ControllerBase
    {
        private readonly IFileRepository _fileRepository;
        private readonly AIService _aiService;
        private readonly IHubContext<ChatHub> _hubContext;

        public FileController(
            IFileRepository fileRepository,
            AIService aiService,
            IHubContext<ChatHub> hubContext)
        {
            _fileRepository = fileRepository;
            _aiService = aiService;
            _hubContext = hubContext;
        }
        [HttpPost("Upload")]
        public async Task<ActionResult<ResponseMessage>> UploadFiles(List<IFormFile> files, string? source=null)
        {
            var result = await _fileRepository.UploadFiles(files, source);
            return Ok(result);
        }

        [HttpGet("Getfile/{fileName}")]
        public async Task<IActionResult> GetFile(string fileName)
        {
            var result = await _fileRepository.GetFile(fileName);
            if (result.IsError)
                return Ok(new { isError = true, message = result.Message });

            return File(result.FileBytes, result.MimeType);
        }

        [HttpGet("GetDescription/{fileName}")]
        public async Task<ActionResult<ResponseMessage>> GetDescription(string fileName)
        {
            var result = await _fileRepository.GetDescription(fileName);
            if (result == null)
                return new ResponseMessage { IsError = true, Message = "No any description." };

            return new ResponseMessage { IsError = false, Message = result };
        }

        [HttpDelete("DeleteFile")]
        public async Task<ActionResult<ResponseMessage>> DeleteFile(List<string> fileNames)
        {
            var result = await _fileRepository.DeleteFiles(fileNames);
            return Ok(result);
        }

        [HttpGet("GetAllFiles")]
        public async Task<ActionResult<List<FileDto>>> GetAllFiles(string source = null)
        {
            var result = await _fileRepository.GetAllFiles(source);
            return Ok(result);
        }

        [HttpPost("SyncWithAI")]
        public async Task<ActionResult<ResponseMessage>> SyncWithAI(string fileName, string prompt)
        {
            if (string.IsNullOrEmpty(fileName))
                return BadRequest(new ResponseMessage { IsError = true, Message = "File name is required" });

            // Get the file data
            var file = await _fileRepository.GetFileDto(fileName);
            var question = $"User Prompt: {prompt} \n File Name: {file.FileName} \n File Description {file.Description}";

            // Stream the AI response with SignalR
            var fullMessage = new StringBuilder();
            await foreach (var message in _aiService.CallAgent("FileAgent", question))
            {
                if (!string.IsNullOrEmpty(message.Message))
                {
                    fullMessage.Append(message.Message);
                    // Send each chunk to connected clients via SignalR
                    Console.WriteLine(message.Message);
                    await _hubContext.Clients.All.SendAsync("ReceiveMessage", message);
                }
            }

            // Update the database with the complete analysis
            var updateResult = await _fileRepository.UpdateFileAIAnalysis(fileName, fullMessage.ToString());

            return Ok(new ResponseMessage
            {
                IsError = false,
                Message = fullMessage.ToString(),
            });
        }

        [HttpGet("GetAIAnalysis/{fileName}")]
        public async Task<ActionResult<ResponseMessage>> GetAIAnalysis(string fileName)
        {
            var result = await _fileRepository.GetAIAnalysis(fileName);
            if (result == null)
                return new ResponseMessage { IsError = true, Message = "No AI analysis available." };

            return new ResponseMessage { IsError = false, Message = result };
        }

        [HttpGet("GetFileDto/{fileName}")]
        public async Task<ActionResult<FileDto>> GetFileDto(string fileName)
        {
            var result = await _fileRepository.GetFileDto(fileName);
            return Ok(result);
        }
    }
}
